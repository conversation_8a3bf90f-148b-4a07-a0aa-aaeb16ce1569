<template>
  <div class="rikka-input-container">
    <div class="rune-decoration">✪</div>
    <div class="input-wrapper">
      <!-- 单行输入框 -->
      <input
        v-if="type === 'text'"
        :type="password ? (showPassword ? 'text' : 'password') : 'text'"
        v-bind="$attrs"
        :value="modelValue"
        @input="handleInput"
        :maxlength="maxLength"
        class="input-field"
        :placeholder="placeholder"
        :class="{
          warning: isWarning,
          danger: isDanger,
          'has-password-toggle': password,
        }"
      />

      <!-- 多行输入框 -->
      <textarea
        v-else
        v-bind="$attrs"
        :value="modelValue"
        @input="handleInput"
        :maxlength="maxLength"
        class="input-field"
        :placeholder="placeholder"
        :class="{ warning: isWarning, danger: isDanger }"
        :rows="rows"
      ></textarea>

      <!-- 密码显示/隐藏切换按钮 -->
      <button
        v-if="password && type === 'text'"
        class="password-toggle"
        @click.prevent="togglePassword"
        :class="{ warning: isWarning, danger: isDanger }"
      >
        <svg
          v-if="showPassword"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          class="eye-icon"
        >
          <path
            d="M12 9a3 3 0 0 1 3 3 3 3 0 0 1-3 3 3 3 0 0 1-3-3 3 3 0 0 1 3-3m0-4.5c5 0 9.27 3.11 11 7.5-1.73 4.39-6 7.5-11 7.5s-9.27-3.11-11-7.5c1.73-4.39 6-7.5 11-7.5M3.18 12a9.821 9.821 0 0 0 17.64 0 9.821 9.821 0 0 0-17.64 0z"
          />
        </svg>
        <svg
          v-else
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          class="eye-icon"
        >
          <path
            d="M11.83 9L15 12.16V12a3 3 0 0 0-3-3h-.17m-4.3.8l1.55 1.55c-.05.21-.08.42-.08.65a3 3 0 0 0 3 3c.22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53a5 5 0 0 1-5-5c0-.79.2-1.53.53-2.2M2 4.27l2.28 2.28.45.45C3.08 8.3 1.78 10 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.43.42L19.73 22 21 20.73 3.27 3 2 4.27z"
          />
        </svg>
      </button>

      <!-- 字数统计（仅在有限制时显示） -->
      <div
        v-if="maxLength"
        class="counter"
        :class="{
          warning: isWarning,
          danger: isDanger,
          'has-toggle': password,
        }"
      >
        {{ currentLength }} / {{ maxLength }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 定义组件props
const props = defineProps({
  // 双向绑定的输入值
  modelValue: {
    type: String,
    default: '',
  },
  // 最大输入长度限制
  maxLength: {
    type: Number,
    default: null,
  },
  // 输入框占位文本
  placeholder: {
    type: String,
    default: '',
  },
  // 输入框类型：text(单行)或textarea(多行)
  type: {
    type: String,
    default: 'text',
    validator: (value: string) => ['text', 'textarea'].includes(value),
  },
  // 多行输入框的行数
  rows: {
    type: Number,
    default: 3,
  },
  // 是否为密码输入模式
  password: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue']);

// 计算当前输入长度
const currentLength = computed(() => props.modelValue.length);
// 计算是否达到警告状态(超过最大长度的80%)
const isWarning = computed(
  () => props.maxLength && currentLength.value > props.maxLength * 0.8
);
// 计算是否达到危险状态(达到最大长度)
const isDanger = computed(
  () => props.maxLength && currentLength.value >= props.maxLength
);

// 密码显示/隐藏状态
const showPassword = ref(false);
// 切换密码显示状态
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 处理输入事件
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement;
  let value = target.value;

  // 如果设置了最大长度，截断超出部分
  if (props.maxLength && value.length > props.maxLength) {
    value = value.slice(0, props.maxLength);
  }

  // 触发更新事件
  emit('update:modelValue', value);
};
</script>

<style lang="scss">
.rikka-input-container {
  position: relative;

  .rune-decoration {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -0.7rem;
    color: var(--input-rune-color);
    font-size: 1.8rem;
    opacity: 0.7;
    text-shadow: var(--input-rune-glow);
    transition: all 0.3s ease;
    animation: glow 3s infinite;
    z-index: 1;
  }

  &:hover .rune-decoration {
    opacity: 1;
    text-shadow: 0 0 0.9375rem rgba(var(--magic-pink-rgb), 0.8);
  }

  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .input-field {
    width: 100%;
    padding: 0.7rem 1rem;
    background: var(--input-field-bg);
    border: var(--input-field-border);
    border-radius: var(--border-radius-md);
    color: var(--input-field-text);
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow:
      0 0 0.625rem rgba(75, 0, 130, 0.3),
      inset 0 0 0.3125rem rgba(var(--interactive-primary-rgb), 0.2);
    outline: none;
    backdrop-filter: blur(0.125rem);
    text-shadow: 0 0 0.1875rem rgba(216, 193, 255, 0.7);
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }
    resize: vertical; /* 允许垂直调整大小 */

    /* 修复浏览器自动填充样式问题 */
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      -webkit-text-fill-color: var(--input-field-text) !important;
      -webkit-box-shadow: 0 0 0 1000px var(--input-field-bg) inset !important;
      transition: background-color 5000s ease-in-out 0s !important;
      background-color: var(--input-field-bg) !important;
      color: var(--input-field-text) !important;
    }

    &.has-password-toggle {
      padding-right: 2.5rem; /* 为密码切换按钮留出空间 */
    }

    &::placeholder {
      color: var(--input-placeholder);
      opacity: 0.7;
      text-shadow: none;
    }

    &:focus {
      border: var(--input-focus-border);
      box-shadow: var(--input-focus-shadow);
      background: rgba(35, 15, 65, 0.9);

      /* 修复浏览器自动填充样式问题 - 聚焦状态 */
      &:-webkit-autofill {
        -webkit-box-shadow:
          0 0 0 1000px rgba(35, 15, 65, 0.9) inset,
          var(--input-focus-shadow) !important;
        background-color: rgba(35, 15, 65, 0.9) !important;
      }
    }

    &.warning {
      border: var(--input-warning-border);
      box-shadow: var(--input-warning-shadow);

      /* 修复浏览器自动填充样式问题 - 警告状态 */
      &:-webkit-autofill {
        -webkit-box-shadow:
          0 0 0 1000px var(--input-field-bg) inset,
          var(--input-warning-shadow) !important;
        background-color: var(--input-field-bg) !important;
      }
    }

    &.danger {
      border: var(--input-danger-border);
      box-shadow: var(--input-danger-shadow);

      /* 修复浏览器自动填充样式问题 - 危险状态 */
      &:-webkit-autofill {
        -webkit-box-shadow:
          0 0 0 1000px var(--input-field-bg) inset,
          var(--input-danger-shadow) !important;
        background-color: var(--input-field-bg) !important;
      }
    }
  }

  /* 密码切换按钮 */
  .password-toggle {
    position: absolute;
    right: 0.8rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    width: 1.8rem;
    height: 1.8rem;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    z-index: 2; /* 确保在输入框上方 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--input-field-text);

    &:hover {
      opacity: 1;
      background: rgba(255, 255, 255, 0.1);
    }

    .eye-icon {
      width: 1.3rem;
      height: 1.3rem;
      fill: currentColor;
    }

    &.warning {
      color: var(--counter-warning-text);
    }

    &.danger {
      color: var(--counter-danger-text);
    }
  }

  .counter {
    position: absolute;
    right: 1.2rem;
    bottom: 0.5rem; /* 调整位置以适应多行输入 */
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-sm);
    background: var(--counter-bg);
    color: var(--counter-text);
    transition: all 0.3s ease;
    z-index: 1; /* 确保在输入框上方 */

    /* 当有密码切换按钮时调整位置 */
    &.has-toggle {
      right: 3.5rem;
    }

    &.warning {
      background: var(--counter-warning-bg);
      color: var(--counter-warning-text);
      text-shadow: 0 0 0.1875rem rgba(var(--magic-gold-rgb), 0.3);
    }

    &.danger {
      background: var(--counter-danger-bg);
      color: var(--counter-danger-text);
      text-shadow: 0 0 0.1875rem rgba(var(--interactive-danger-rgb), 0.3);
    }
  }
}

/* 动画部分保持不变 */
@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(183, 0, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 15px rgba(183, 0, 255, 0.8);
  }
  100% {
    text-shadow: 0 0 5px rgba(183, 0, 255, 0.5);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}
</style>

