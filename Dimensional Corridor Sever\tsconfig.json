{
  "compilerOptions": {
    "module": "commonjs", // 指定生成哪个模块系统代码，这里使用的是CommonJS
    "declaration": true, // 生成相应的.d.ts文件
    "removeComments": true, // 删除编译后的JavaScript文件中的所有注释
    "emitDecoratorMetadata": true, // 为装饰器声明生成元数据
    "experimentalDecorators": true, // 启用对实验性装饰器的支持
    "allowSyntheticDefaultImports": true, // 允许从没有默认导出的模块中默认导入
    "target": "ES2023", // 指定ECMAScript目标版本为ES2023
    "sourceMap": true, // 生成相应的.map文件
    "outDir": "./dist", // 指定编译后文件的输出目录
    "baseUrl": "./", // 指定解析非相对模块名称的基准目录
    "incremental": true, // 启用增量编译
    "skipLibCheck": true, // 跳过声明文件的类型检查
    "strictNullChecks": true, // 启用严格的null检查
    "forceConsistentCasingInFileNames": true, // 强制文件名的大小写必须与导入的大小写一致
    "noImplicitAny": false, // 不允许隐式的any类型
    "strictBindCallApply": false, // 不强制执行严格的bind, call和apply检查
    "noFallthroughCasesInSwitch": false // 不报告switch语句中缺少break的情况
  }
}
