<template>
  <div
    class="rikka-radio-container"
    :class="{
      selected: isSelected,
      disabled: disabled,
    }"
    @click="toggleRadio"
  >
    <div class="radio-control">
      <div class="outer-circle">
        <div class="inner-circle"></div>
        <div v-show="isSelected" class="magic-circle"></div>
        <div v-show="isSelected" class="magic-dot"></div>
      </div>
    </div>
    <div class="radio-label">
      <span class="label-text"><slot></slot></span>
      <span v-show="isSelected" class="rune">⟡</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 定义组件props
const props = defineProps({
  // 双向绑定的值，可以是布尔、字符串或数字类型
  modelValue: {
    type: [Boolean, String, Number],
    default: false,
  },
  // 单选按钮的值
  value: {
    type: [String, Number],
    default: null,
  },
  // 是否禁用状态
  disabled: {
    type: Boolean,
    default: false,
  },
  // 分组名称，用于单选按钮组
  group: {
    type: String,
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue']);

// 计算当前是否选中状态
const isSelected = computed(() => {
  // 如果是分组模式，比较modelValue和当前value是否相等
  if (props.group) {
    return props.modelValue === props.value;
  }
  // 非分组模式，直接返回modelValue的布尔值
  return !!props.modelValue;
});

// 切换单选按钮状态
const toggleRadio = () => {
  // 如果禁用则直接返回
  if (props.disabled) return;

  // 分组模式下，发射当前value值
  if (props.group) {
    emit('update:modelValue', props.value);
  } else {
    // 非分组模式下，切换布尔值
    emit('update:modelValue', !props.modelValue);
  }
};
</script>

<style lang="scss" scoped>
.rikka-radio-container {
  display: flex;
  align-items: center;
  padding: 0.7rem 1rem;
  border-radius: var(--border-radius-md);
  background: var(--radio-background);
  border: var(--border-focus-ring);
  box-shadow: var(--shadow-neon-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    &.disabled {
      transform: none;
      box-shadow: var(--shadow-neon-primary);
    }

    &:not(.disabled) {
      box-shadow: var(--radio-hover-glow), var(--shadow-dimension-md);
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.selected {
    background: var(--radio-selected-background);
    border: 0.1rem solid var(--radio-selected-border);
    box-shadow: var(--shadow-dimension);
    animation: pulse-glow 2s infinite;

    .outer-circle {
      border-color: var(--radio-selected-border);
      box-shadow: var(--shadow-neon-glow);
    }

    .inner-circle {
      background: var(--radio-inner-circle);
      box-shadow: var(--shadow-neon-glow);
    }

    .magic-circle {
      opacity: 1;
      animation:
        rotate 4s linear infinite,
        pulse-border 3s infinite alternate;
    }

    .magic-dot {
      opacity: 1;
      animation: pulse 2s infinite;
    }

    .radio-label {
      color: var(--radio-selected-text);
      text-shadow: 0 0 0.5rem #ff69b4cc;

      .rune {
        opacity: 1;
        animation: glow 1.5s infinite alternate;
      }
    }
  }

  .radio-control {
    position: relative;
    margin-right: 0.5rem;

    .outer-circle {
      width: 1.6rem;
      height: 1.6rem;
      border: 0.1rem solid var(--radio-outer-border);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      background: rgba(26, 10, 46, 0.7);
      transition: all 0.3s ease;

      .inner-circle {
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: transparent;
        transition: all 0.3s ease;
      }

      .magic-circle {
        position: absolute;
        width: 2.4rem;
        height: 2.4rem;
        border: 0.15rem dashed var(--radio-magic-circle);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .magic-dot {
        position: absolute;
        width: 0.6rem;
        height: 0.6rem;
        background: var(--radio-magic-dot);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }
    }
  }

  .radio-label {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    letter-spacing: 0.05rem;
    text-shadow: 0 0 0.2rem rgba(164, 93, 226, 0.7);
    font-size: 1.1rem;

    .rune {
      color: var(--radio-rune);
      font-weight: bold;
      text-shadow: 0 0 0.8rem rgba(255, 105, 180, 0.8);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  &:before {
    content: '';
    position: absolute;
    top: -0.1rem;
    left: -0.1rem;
    right: -0.1rem;
    bottom: -0.1rem;
    border: 0.1rem solid var(--magic-gold);
    border-radius: var(--border-radius-md);
    opacity: 0;
    transition: opacity 0.3s ease;

    &.selected {
      opacity: 1;
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.8;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.3);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.8;
    }
  }

  @keyframes glow {
    0% {
      text-shadow: 0 0 0.2rem rgba(255, 105, 180, 0.8);
    }
    100% {
      text-shadow:
        0 0 1rem rgba(255, 105, 180, 1),
        0 0 1.5rem rgba(255, 105, 180, 0.8);
    }
  }

  @keyframes pulse-glow {
    0% {
      box-shadow: var(--shadow-dimension);
    }
    50% {
      box-shadow:
        0 0 1.5rem rgba(255, 215, 0, 0.7),
        inset 0 0 1rem rgba(106, 48, 147, 0.9);
    }
    100% {
      box-shadow: var(--shadow-dimension);
    }
  }

  @keyframes pulse-border {
    0% {
      border-color: rgba(255, 215, 0, 0.7);
    }
    50% {
      border-color: rgba(255, 105, 180, 0.8);
    }
    100% {
      border-color: rgba(255, 215, 0, 0.7);
    }
  }
}
</style>

