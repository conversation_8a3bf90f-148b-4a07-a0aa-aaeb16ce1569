import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateChatDto {
  /** 用户问题 */
  @ApiProperty({
    description: '用户问题',
    example: '你是谁？',
  })
  @IsString({ message: '问题必须是字符串' })
  @IsNotEmpty({ message: '问题不能为空' })
  message: string;

  /** ai 身份 可选 */
  @ApiProperty({
    description: 'ai 身份',
    example: '机器人',
  })
  @IsString({ message: '身份必须是字符串' })
  @IsOptional()
  identity: string;
}
