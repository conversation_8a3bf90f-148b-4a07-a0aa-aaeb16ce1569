import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type UnsubscribeEmailDocument = HydratedDocument<UnsubscribeEmail>;

@Schema({
  timestamps: { createdAt: 'createTime', updatedAt: 'updateTime' },
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: UnsubscribeEmailDocument) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, ...rest } = ret;
      return {
        id: _id.toString(),
        ...rest,
      };
    },
  },
})
export class UnsubscribeEmail {
  /** 退订邮箱唯一标识符 */
  @Prop({})
  id: string;

  /** 邮箱地址 */
  @Prop({
    unique: [true, '邮箱已被退订'],
    trim: true,
    lowercase: true,
    match: [
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      '邮箱格式不正确',
    ],
  })
  email: string;

  /** 创建时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  createTime: Date;

  /** 更新时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  updateTime: Date;
}

export const UnsubscribeEmailSchema =
  SchemaFactory.createForClass(UnsubscribeEmail);
