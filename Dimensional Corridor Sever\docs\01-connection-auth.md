# WebSocket 连接与认证

## 📋 概述

本文档详细介绍WebSocket连接的建立、认证机制和连接管理。

## 🔗 连接配置

### 基本连接
```javascript
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3000/ws', {
  // 认证配置
  auth: {
    token: 'Bearer your-jwt-token'
  },
  
  // 传输方式配置
  transports: ['websocket', 'polling'],
  
  // 超时配置
  timeout: 5000,
  
  // 自动连接
  autoConnect: true
});
```

### 连接参数说明

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `auth.token` | string | JWT认证令牌 | 必填 |
| `transports` | array | 传输协议 | `['websocket', 'polling']` |
| `timeout` | number | 连接超时时间(ms) | `20000` |
| `autoConnect` | boolean | 是否自动连接 | `true` |

## 🔐 认证机制

### JWT Token 格式
```
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 认证流程
1. 客户端连接时传递JWT Token
2. 服务端验证Token有效性
3. 提取用户信息存储到Socket上下文
4. 认证失败自动断开连接

### 认证示例
```javascript
// 获取存储的Token
const token = localStorage.getItem('jwt_token');

if (!token) {
  console.error('未找到认证令牌，请先登录');
  return;
}

// 建立认证连接
const socket = io('ws://localhost:3000/ws', {
  auth: { token: `Bearer ${token}` }
});
```

## 📡 连接事件

### 基础连接事件
```javascript
// 连接成功
socket.on('connect', () => {
  console.log('✅ WebSocket连接成功');
  console.log('Socket ID:', socket.id);
});

// 连接失败
socket.on('connect_error', (error) => {
  console.error('❌ 连接失败:', error.message);
  
  // 根据错误类型处理
  if (error.message === '用户未登录') {
    // 跳转到登录页面
    window.location.href = '/login';
  }
});

// 连接断开
socket.on('disconnect', (reason) => {
  console.log('🔌 连接断开:', reason);
  
  // 断开原因处理
  switch (reason) {
    case 'io server disconnect':
      console.log('服务器主动断开连接');
      break;
    case 'io client disconnect':
      console.log('客户端主动断开连接');
      break;
    case 'ping timeout':
      console.log('心跳超时');
      break;
    default:
      console.log('其他原因:', reason);
  }
});

// 连接超时
socket.on('connect_timeout', () => {
  console.error('⏰ 连接超时');
});
```

## 🔄 重连机制

### 自动重连配置
```javascript
const socket = io('ws://localhost:3000/ws', {
  auth: { token: `Bearer ${token}` },
  
  // 重连配置
  reconnection: true,           // 启用自动重连
  reconnectionAttempts: 5,      // 最大重连次数
  reconnectionDelay: 1000,      // 重连延迟(ms)
  reconnectionDelayMax: 5000,   // 最大重连延迟(ms)
  randomizationFactor: 0.5      // 随机化因子
});
```

### 手动重连实现
```javascript
class ReconnectingSocket {
  constructor(url, options = {}) {
    this.url = url;
    this.options = options;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.isReconnecting = false;
    
    this.connect();
  }

  connect() {
    this.socket = io(this.url, {
      ...this.options,
      autoConnect: false
    });

    this.socket.on('connect', () => {
      console.log('✅ 连接成功');
      this.reconnectAttempts = 0;
      this.isReconnecting = false;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 连接断开:', reason);
      
      if (reason === 'io server disconnect' && !this.isReconnecting) {
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ 连接错误:', error);
      
      if (!this.isReconnecting) {
        this.reconnect();
      }
    });

    this.socket.connect();
  }

  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🚫 达到最大重连次数，停止重连');
      return;
    }

    if (this.isReconnecting) {
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;
    
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      5000
    );
    
    console.log(`🔄 ${delay}ms后进行第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      this.isReconnecting = false;
      this.socket.connect();
    }, delay);
  }

  disconnect() {
    this.socket.disconnect();
  }
}

// 使用示例
const reconnectingSocket = new ReconnectingSocket('ws://localhost:3000/ws', {
  auth: { token: `Bearer ${token}` }
});
```

## 🔍 连接状态检查

### 检查连接状态
```javascript
// 检查是否已连接
if (socket.connected) {
  console.log('当前已连接');
} else {
  console.log('当前未连接');
}

// 获取Socket ID
console.log('Socket ID:', socket.id);

// 检查传输方式
console.log('传输方式:', socket.io.engine.transport.name);
```

### 连接质量监控
```javascript
// 监控连接质量
let pingStart = Date.now();

socket.on('ping', () => {
  pingStart = Date.now();
});

socket.on('pong', (latency) => {
  const roundTripTime = Date.now() - pingStart;
  console.log(`延迟: ${latency}ms, 往返时间: ${roundTripTime}ms`);
});
```

## ⚠️ 错误处理

### 常见错误类型
```javascript
socket.on('connect_error', (error) => {
  switch (error.message) {
    case '用户未登录':
      console.error('认证失败，请重新登录');
      // 跳转到登录页面
      break;
      
    case 'Token已过期':
      console.error('Token过期，请刷新Token');
      // 刷新Token逻辑
      break;
      
    case '权限不足':
      console.error('权限不足，无法连接');
      break;
      
    default:
      console.error('未知连接错误:', error.message);
  }
});
```

### 错误恢复策略
```javascript
class ErrorRecoverySocket {
  constructor(url, options) {
    this.url = url;
    this.options = options;
    this.errorCount = 0;
    this.maxErrors = 3;
    
    this.connect();
  }

  connect() {
    this.socket = io(this.url, this.options);

    this.socket.on('connect_error', (error) => {
      this.errorCount++;
      
      if (this.errorCount >= this.maxErrors) {
        console.error('连续错误次数过多，停止连接');
        return;
      }

      // 根据错误类型采取不同策略
      if (error.message === 'Token已过期') {
        this.refreshTokenAndReconnect();
      } else {
        this.delayedReconnect();
      }
    });

    this.socket.on('connect', () => {
      this.errorCount = 0; // 重置错误计数
    });
  }

  async refreshTokenAndReconnect() {
    try {
      // 刷新Token的逻辑
      const newToken = await this.refreshToken();
      this.options.auth.token = `Bearer ${newToken}`;
      
      // 重新连接
      this.socket.disconnect();
      this.connect();
    } catch (error) {
      console.error('Token刷新失败:', error);
    }
  }

  delayedReconnect() {
    setTimeout(() => {
      this.socket.connect();
    }, 2000);
  }
}
```

## 🛠️ 调试工具

### 开启调试模式
```javascript
// 开启Socket.IO调试
localStorage.debug = 'socket.io-client:socket';

// 开启详细调试
localStorage.debug = 'socket.io-client:*';
```

### 事件监听器
```javascript
// 监听所有接收事件
socket.onAny((event, ...args) => {
  console.log(`📥 接收事件: ${event}`, args);
});

// 监听所有发送事件
socket.onAnyOutgoing((event, ...args) => {
  console.log(`📤 发送事件: ${event}`, args);
});

// 移除监听器
socket.offAny();
socket.offAnyOutgoing();
```

### 连接信息获取
```javascript
function getConnectionInfo() {
  return {
    connected: socket.connected,
    id: socket.id,
    transport: socket.io.engine.transport.name,
    upgraded: socket.io.engine.upgraded,
    readyState: socket.io.engine.readyState
  };
}

console.log('连接信息:', getConnectionInfo());
```

## 📝 最佳实践

### 1. 连接管理
- ✅ 在组件卸载时正确断开连接
- ✅ 避免重复创建连接
- ✅ 实现适当的重连策略

### 2. 错误处理
- ✅ 监听所有连接相关事件
- ✅ 根据错误类型采取相应措施
- ✅ 提供用户友好的错误提示

### 3. 性能优化
- ✅ 使用连接池管理多个连接
- ✅ 合理设置超时时间
- ✅ 监控连接质量

### 4. 安全考虑
- ✅ 定期刷新JWT Token
- ✅ 验证服务器证书
- ✅ 使用HTTPS/WSS协议

## 🔗 相关文档

- [消息传递功能](./02-messaging.md)
- [用户状态管理](./03-user-status.md)
- [群组管理](./04-group-management.md)
- [系统架构](./websocket-system-architecture.md)
