import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type HotKeyWordsDocument = HydratedDocument<HotKeyWords>;

@Schema({
  timestamps: { createdAt: 'createTime', updatedAt: 'updateTime' },
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: HotKeyWordsDocument) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, ...rest } = ret;
      return {
        id: _id.toString(),
        ...rest,
      };
    },
  },
})
export class HotKeyWords {
  /** 热点词唯一标识符 */
  @Prop({})
  id: string;
  /** 热点词 */
  @Prop({ required: true, unique: true })
  keyword: string;

  /** 创建时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  createTime: Date;

  /** 更新时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  updateTime: Date;
}

export const HotKeyWordsSchema = SchemaFactory.createForClass(HotKeyWords);
