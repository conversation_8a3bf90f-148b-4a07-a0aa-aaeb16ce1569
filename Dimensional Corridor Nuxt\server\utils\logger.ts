import { join } from 'path';
import {
  existsSync,
  mkdirSync,
  appendFileSync,
  statSync,
  unlinkSync,
  readdirSync,
} from 'fs';

interface LogOptions {
  type: 'info' | 'warn' | 'error' | 'debug';
  module: string;
  message: string;
  data?: any;
}

class Logger {
  private logDir: string;
  private currentDate: string;

  constructor() {
    this.logDir = join(process.cwd(), 'logs');
    this.currentDate = this.getCurrentDate();
    this.ensureLogDirectory();
    this.cleanOldLogs(); // 添加自动清理
  }

  // 自动清理30天前的日志
  private cleanOldLogs() {
    const files = readdirSync(this.logDir);
    const now = new Date();
    const threshold = 30 * 24 * 60 * 60 * 1000; // 30天

    files.forEach((file) => {
      if (file.endsWith('.log')) {
        const filePath = join(this.logDir, file);
        const stat = statSync(filePath);

        if (now.getTime() - stat.mtimeMs > threshold) {
          unlinkSync(filePath);
        }
      }
    });
  }

  private getCurrentDate(): string {
    const now = new Date();
    // 转换为北京时间 (UTC+8)
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return beijingTime.toISOString().split('T')[0];
  }

  private ensureLogDirectory() {
    if (!existsSync(this.logDir)) {
      mkdirSync(this.logDir, { recursive: true });
    }
  }

  private getLogFilePath(): string {
    return join(this.logDir, `${this.currentDate}.log`);
  }

  private formatLogMessage(options: LogOptions): string {
    // 添加安全的序列化函数
    const safeStringify = (data: any): string => {
      try {
        // 处理常见不可序列化类型
        if (data instanceof Error) {
          return JSON.stringify({
            message: data.message,
            stack: data.stack,
          });
        }

        // 处理 BigInt 等特殊类型
        const replacer = (_key: string, value: any) =>
          typeof value === 'bigint' ? value.toString() : value;

        return JSON.stringify(data, replacer);
      } catch (e) {
        return `[Serialization Error]: ${e instanceof Error ? e.message : String(e)}`;
      }
    };

    const now = new Date();
    // 转换为北京时间 (UTC+8)
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    const timestamp = beijingTime.toISOString();
    const logData = options.data ? ` | ${safeStringify(options.data)}` : '';

    return `[${timestamp}] [${options.type.toUpperCase()}] [${options.module}] ${
      options.message
    }${logData}\n`;
  }

  private writeLog(options: LogOptions) {
    const logMessage = this.formatLogMessage(options);
    const logFile = this.getLogFilePath();

    try {
      appendFileSync(logFile, logMessage);
    } catch (error: any) {
      console.error('写入日志失败:', error.message as string);
    }
  }

  info(module: string, message: string, data?: any) {
    this.writeLog({ type: 'info', module, message, data });
  }

  warn(module: string, message: string, data?: any) {
    this.writeLog({ type: 'warn', module, message, data });
  }

  error(module: string, message: string, data?: any) {
    this.writeLog({ type: 'error', module, message, data });
  }

  debug(module: string, message: string, data?: any) {
    this.writeLog({ type: 'debug', module, message, data });
  }
}

// 创建单例实例
export const logger = new Logger();
