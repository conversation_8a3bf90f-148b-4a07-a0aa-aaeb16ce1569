<template>
  <RikkaDialog
    :show="show"
    :title="(authStore.authStore?.phone ? '更换' : '绑定') + '手机号码'"
    :width="dialogWidth"
    :mask-closable="false"
    @close="close"
  >
    <!-- 表单主体区域 -->
    <div class="change-email-modal">
      <!-- 旧手机号输入区域 -->
      <div class="lab" v-if="authStore.authStore?.phone">
        <span> 旧号码: </span>
        <RikkaInput
          v-model="form.oldPhone"
          placeholder="请输入旧号码"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 新手机号输入区域 -->
      <div class="lab">
        <span> 新号码: </span>
        <RikkaInput
          v-model="form.newPhone"
          placeholder="请输入新号码"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 图片验证码区域 -->
      <div class="lab">
        <span> 校验码: </span>
        <div class="captcha">
          <div class="input">
            <RikkaInput
              v-model="form.captcha"
              placeholder="请输入图片验证码"
              @keyup.enter="sendphoneCode"
            />
          </div>
          <div class="captcha-img">
            <captcha ref="captchaRef" />
          </div>
        </div>
      </div>

      <!-- 短信验证码区域 -->
      <div class="lab">
        <span> 验证码: </span>
        <div class="code">
          <RikkaInput
            v-model="form.code"
            placeholder="请输入短信码"
            @keyup.enter="handleConfirm"
          />
          <div class="btn">
            <RippleButton
              class="send"
              @click="sendphoneCode"
              :disabled="sendPhoneCodeLock || countdown < 60"
            >
              {{ countdown >= 60 ? '发送验证码' : `${60 - countdown}秒后重发` }}
            </RippleButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <RippleButton
          class="btn-confirm"
          @click="handleConfirm"
          :disabled="isDisabled"
          >确定</RippleButton
        >
      </div>
    </template>
  </RikkaDialog>
</template>

<script lang="ts" setup>
// 组件属性定义
const { show } = defineProps({
  show: Boolean, // 控制模态框显示状态
});

// 认证状态管理
const authStore = useAuthStore();
const captchaRef = ref<CaptchaExpose | null>(null); // 图片验证码组件引用

// 移动端适配 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '30rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 事件发射器定义
const emit = defineEmits<{
  close: [value: boolean]; // 关闭事件，携带是否成功标志
}>();

// 关闭处理函数
const close = (flag = false) => {
  emit('close', flag);
};

// 表单数据模型
const form = ref({
  oldPhone: '', // 旧手机号
  newPhone: '', // 新手机号
  captcha: '', // 图片验证码
  code: '', // 短信验证码
});

// 验证码发送相关状态
const { setPhoneDate, getCodeDate } = useSendCodeStore();
const countdown = ref(0); // 倒计时秒数
const sendPhoneCodeLock = ref(false); // 发送按钮锁定状态

// 倒计时处理逻辑
const startCountdown = () => {
  countdown.value = Math.floor(
    (new Date().getTime() - new Date(getCodeDate().phone).getTime()) / 1000
  );
  if (countdown.value >= 60) return;
  const timer = setInterval(() => {
    if (countdown.value > 60) {
      clearInterval(timer);
    }

    countdown.value = Math.floor(
      (new Date().getTime() - new Date(getCodeDate().phone).getTime()) / 1000
    );
  }, 1000);
};
startCountdown();

// 发送手机号验证码
// 验证码发送按钮临时封锁
const sendphoneCode = useDebounceFn(async () => {
  if (
    authStore.authStore?.phone &&
    authStore.authStore?.phone !== form.value.oldPhone
  ) {
    return useMessage({
      name: '请重新输入旧手机号',
      description: '旧手机号错误',
      type: 'error',
    });
  }

  // 表单验证
  if (!form.value.newPhone) {
    return useMessage({
      name: '手机号未输入',
      description: '手机号必须输入',
      type: 'error',
    });
  } else {
    const phoneReg =
      /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/;
    if (!phoneReg.test(form.value.newPhone)) {
      return useMessage({
        name: '手机号格式错误',
        description: '手机号格式错误',
        type: 'error',
      });
    }
  }
  if (!form.value.captcha) {
    return useMessage({
      name: '验证码未输入',
      description: '验证码必须输入',
      type: 'error',
    });
  }

  return useMessage({
    name: '未接入服务',
    description: '该功能暂未开放',
    type: 'error',
  });

  try {
    sendPhoneCodeLock.value = true;
    await useApi().sendMobileCaptcha({
      phone: form.value.newPhone,
      code: form.value.captcha,
    });
    sendPhoneCodeLock.value = false;

    // 发送成功开始倒计时
    setPhoneDate();
    startCountdown();
  } catch (err) {
    captchaRef.value?.refresh();
    sendPhoneCodeLock.value = false;
  }
}, 300);

// 表单提交禁用状态计算
const isDisabled = computed(() => {
  return (
    !form.value.oldPhone ||
    !form.value.newPhone ||
    !form.value.captcha ||
    !form.value.code
  );
});

// 确认修改处理函数
const handleConfirm = async () => {
  if (isDisabled.value) return;
  try {
    await useApi().changePhone({
      oldPhone: form.value.oldPhone,
      phone: form.value.newPhone,
      code: form.value.code,
    });
    useMessage({
      name: '修改成功',
      description: '修改成功，请重新登录',
      type: 'success',
    });
    await useApi().logout();
    close(true);
  } catch (err) {}
};
</script>

<style lang="scss" scoped>
.lab {
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;

  > span {
    margin-right: 1rem;
  }

  > div {
    flex: 1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.captcha {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.3rem;

  > .input {
    flex: 1;
  }

  > .captcha-img {
    display: flex;
    height: 2.7rem;
    margin-left: 0.5rem;

    > .captcha-img {
      margin-left: 0.5rem;
    }
  }
}
.code {
  display: flex;
  align-items: center;

  > .btn {
    margin-left: 0.5rem;

    > .send {
      margin: 0 auto;
      width: 7.8rem;
      height: 2.7rem;
      background-color: var(--button-primary);
      color: var(--text-primary);

      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: var(--button-primary-hover);
      }
    }
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

