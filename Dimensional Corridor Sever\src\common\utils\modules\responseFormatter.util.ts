/**
 * ?: 返回数据格式化工具
 * @param {number} statusCode - 状态码，表示请求的处理结果。
 * @param {string} message - 状态信息，提供对状态码的简要描述。
 * @param {object} data - 数据，API响应的具体数据内容。
 * @returns {object} 返回一个包含状态码、状态信息和数据的对象。
 * @description 该工具函数用于格式化API响应数据，返回一个包含状态码、状态信息和数据的对象。
 */
export const formatResponse = (
  statusCode = 200, // 默认状态码为200，表示请求成功
  message = 'success', // 默认状态信息为'success'
  data = {} // 默认数据为空对象
) => {
  // 返回一个格式化的对象，包含状态码、状态信息和数据
  return {
    statusCode, // 状态码
    message, // 状态信息
    data, // 数据
  };
};
