import * as maxmind from 'maxmind'
import { join } from 'path'
import { fileURLToPath } from 'url'
import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs'
import { logger } from './logger'

// 高德地图API配置
const GAODE_API_KEY = 'c1faf8b5b9c326473b09e65577396ea9' // 替换为您的实际API密钥
const GAODE_API_URL = 'https://restapi.amap.com/v3/ip'

// 缓存配置
const CACHE_DIR = join(process.cwd(), 'logs', 'cache')
const CACHE_FILE_PATH = join(CACHE_DIR, 'ip-cache.json')
const CACHE_EXPIRY_DAYS = 30 // 缓存过期天数

let lookup: maxmind.Reader<maxmind.CityResponse> | null = null
let ipCache: Record<string, { data: IpInfo; timestamp: number }> = {}

// 确保缓存目录存在
function ensureCacheDir() {
  if (!existsSync(CACHE_DIR)) {
    try {
      mkdirSync(CACHE_DIR, { recursive: true })
      logger.info('IP', '创建缓存目录成功', { path: CACHE_DIR })
    } catch (error: any) {
      logger.error('IP', '创建缓存目录失败', {
        path: CACHE_DIR,
        error: error?.message,
      })
    }
  }
}

// 初始化缓存
function initCache() {
  ensureCacheDir()
  try {
    if (existsSync(CACHE_FILE_PATH)) {
      const cacheContent = readFileSync(CACHE_FILE_PATH, 'utf-8')
      ipCache = JSON.parse(cacheContent)
      logger.info('IP', '缓存加载成功', { cacheSize: Object.keys(ipCache).length })
    } else {
      ipCache = {}
      saveCache()
    }
  } catch (error: any) {
    logger.error('IP', '缓存加载失败', { error: error?.message })
    ipCache = {}
  }
}

// 保存缓存
function saveCache() {
  try {
    writeFileSync(CACHE_FILE_PATH, JSON.stringify(ipCache, null, 2))
  } catch (error: any) {
    logger.error('IP', '缓存保存失败', { error: error?.message })
  }
}

// 检查缓存是否过期
function isCacheExpired(timestamp: number): boolean {
  const now = Date.now()
  const expiryTime = timestamp + CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000
  return now > expiryTime
}

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now()
  const expiredKeys = Object.keys(ipCache).filter((key) => isCacheExpired(ipCache[key].timestamp))

  expiredKeys.forEach((key) => {
    delete ipCache[key]
  })

  if (expiredKeys.length > 0) {
    logger.info('IP', '清理过期缓存', { count: expiredKeys.length })
    saveCache()
  }
}

export async function initIpLookup() {
  if (!lookup) {
    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url)
    const __dirname = fileURLToPath(new URL('.', import.meta.url))

    // 尝试多个可能的路径
    const possiblePaths = [
      // 开发环境路径
      join(process.cwd(), 'server', 'db', 'GeoLite2-City.mmdb'),
      // 生产环境路径
      join(process.cwd(), 'public', 'db', 'GeoLite2-City.mmdb'),
      // 备用路径
      join(process.cwd(), 'db', 'GeoLite2-City.mmdb'),
    ]

    let loaded = false
    for (const dbPath of possiblePaths) {
      try {
        if (existsSync(dbPath)) {
          lookup = await maxmind.open(dbPath)
          logger.info('IP', '数据库加载成功', { path: dbPath })
          loaded = true
          break
        }
      } catch (error: any) {
        logger.error('IP', '数据库加载失败', {
          path: dbPath,
          error: error?.message,
        })
      }
    }

    if (!loaded) {
      const error = new Error('无法加载 MaxMind 数据库文件')
      logger.error('IP', error.message)
      throw error
    }
  }
  if (lookup) {
    logger.info('IP', `数据库版本: ${lookup.metadata}`) // 新增元数据检查
  }
  return lookup
}

export interface IpInfo {
  ip: string
  country?: string
  city?: string
  latitude?: number
  longitude?: number
  timezone?: string
}

/**
 * 调用高德地图API获取IP定位信息
 * @param ip IP地址
 * @returns 城市名称或null
 */
async function getCityFromGaode(ip: string): Promise<string | null> {
  const url = `${GAODE_API_URL}?ip=${ip}&key=${GAODE_API_KEY}`

  try {
    const response = await fetch(url)
    const data = await response.json()

    if (data.status === '1') {
      // 优先返回城市，如果城市为空则返回省份
      return data.city || data.province || null
    } else {
      logger.warn('IP', '高德API返回错误', {
        ip,
        status: data.status,
        info: data.info,
        infocode: data.infocode,
      })
      return null
    }
  } catch (error: any) {
    logger.error('IP', '高德API请求失败', {
      ip,
      error: error?.message || error.toString(),
    })
    return null
  }
}

export async function getIpInfo(ip: string): Promise<IpInfo> {
  // 初始化缓存
  if (Object.keys(ipCache).length === 0) {
    initCache()
  }

  // 检查缓存
  const cachedData = ipCache[ip]
  if (cachedData && !isCacheExpired(cachedData.timestamp)) {
    logger.debug('IP', '使用缓存数据', { ip })
    return cachedData.data
  }

  const lookup = await initIpLookup()

  if (!lookup) {
    const error = new Error('数据库未初始化')
    logger.error('IP', error.message)
    throw error
  }

  const result = lookup.get(ip)

  // 初始IP信息对象
  const ipInfo: IpInfo = {
    ip,
    country: '未知',
    city: '未知',
    latitude: undefined,
    longitude: undefined,
    timezone: undefined,
  }

  if (!result) {
    logger.warn('IP', '未找到IP信息', { ip })
    return ipInfo
  }

  // 获取国家名称（优先中文，其次英文）
  ipInfo.country = result?.country?.names?.['zh-CN'] || result?.country?.names?.en || '未知'

  // 获取城市名称（优先中文，其次英文）
  ipInfo.city =
    result?.city?.names?.['zh-CN'] ||
    result?.city?.names?.en ||
    // 尝试从subdivisions（省级）获取
    result?.subdivisions?.[0]?.names?.['zh-CN'] ||
    '未知'

  // 复制位置信息
  if (result.location) {
    ipInfo.latitude = result.location.latitude
    ipInfo.longitude = result.location.longitude
    ipInfo.timezone = result.location.time_zone
  }

  // 特殊处理：中国IP且城市信息缺失时使用高德服务
  if (ipInfo.country.includes('中国') && ipInfo.city === '未知') {
    logger.debug('IP', '尝试使用高德服务补充中国IP信息', { ip })
    const gaodeCity = await getCityFromGaode(ip)

    if (gaodeCity) {
      logger.info('IP', '高德服务补充城市信息成功', { ip, city: gaodeCity })
      ipInfo.city = gaodeCity
    }
  }

  // 更新缓存
  ipCache[ip] = {
    data: ipInfo,
    timestamp: Date.now(),
  }
  saveCache()

  // 定期清理过期缓存
  cleanExpiredCache()

  return ipInfo
}
