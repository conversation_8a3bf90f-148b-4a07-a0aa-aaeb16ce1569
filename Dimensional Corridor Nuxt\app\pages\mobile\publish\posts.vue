<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-帖子发布</Title>
    </Head>
    <MoblieNavBar>
      发布帖子
      <template #right>
        <RippleButton
          @click="publishPosts"
          class="publish-btn"
          :disabled="!isPublishable"
        >
          发布
        </RippleButton>
      </template>
    </MoblieNavBar>
    <main class="main-content">
      <!-- 发布表单 -->
      <div class="publish-form">
        <div class="form-section">
          <!-- 标题和分类 -->
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">标题</label>
              <RikkaInput
                v-model="publishParams.title"
                :maxLength="30"
                placeholder="请输入标题"
              />
            </div>
            <div class="form-item">
              <label class="form-label">分类</label>
              <RikkaSelect
                v-model="publishParams.category"
                :options="categories"
                placeholder="请选择分类"
              />
            </div>
          </div>

          <!-- 标签 -->
          <div class="form-item">
            <label class="form-label">标签</label>
            <RikkaTagInput
              v-model="publishParams.tags"
              :max-tags="10"
              :max-tag-length="10"
              placeholder="请输入标签"
            />
          </div>

          <!-- 内容 -->
          <div class="form-item">
            <label class="form-label">内容</label>
            <RikkaInput
              v-model="publishParams.content"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
            />
          </div>
        </div>

        <!-- 图片选择器 -->
        <div class="image-selector-section">
          <AppImageSelector
            :images="userImages"
            v-model="publishParams.photos"
            :has-more="hasMore"
            :loading="loading"
            @load-more="loadMoreImages"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
const authStore = useAuthStore();

// 发布参数
const publishParams = ref<PostsUploadInfo>({
  title: '',
  content: '',
  category: '其他',
  tags: [],
  photos: [],
  status: 'approved',
});

// 分类选项
const categories: Array<{ label: string; value: PhotoCategory }> = [
  { label: '美女', value: '美女' },
  { label: '动漫', value: '动漫' },
  { label: '城市', value: '城市' },
  { label: '风景', value: '风景' },
  { label: '二次元', value: '二次元' },
  { label: '美食', value: '美食' },
  { label: '其他', value: '其他' },
];

// 发布按钮是否可用
const isPublishable = computed(() => {
  return (
    publishParams.value.category &&
    publishParams.value.tags.length > 0 &&
    publishParams.value.tags.length <= 10 &&
    publishParams.value.title.length > 0 &&
    publishParams.value.content.length > 0
  );
});

// 图片相关数据
interface Image {
  id: string;
  url: string;
}

const userImages = ref<Image[]>([]);
const loading = ref(false);

// 分页信息
const paginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

// 是否还有更多数据
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
);

/**
 * 获取用户上传的图片列表
 */
const getUserImages = async (isLoadMore = false) => {
  if (!authStore.authStore?.uid) {
    return;
  }

  try {
    loading.value = true;

    const data = await usePhotosApi().getUserPhotosList(
      authStore.authStore?.uid,
      {
        page: paginated.value.page + 1,
        pageSize: 10,
        sortField: 'createTime',
        sortOrder: 'desc',
      }
    );

    // 更新分页信息
    paginated.value = {
      page: data.page,
      pageSize: data.pageSize,
      totalCount: data.totalCount,
      totalPage: data.totalPage,
      sortField: data.sortField,
      sortOrder: data.sortOrder,
    };

    const newImages = data.list.map((item) => ({
      id: item.id,
      url: item.url,
    }));

    if (isLoadMore) {
      // 加载更多时，追加到现有数据
      userImages.value = [...userImages.value, ...newImages];
    } else {
      // 首次加载时，替换数据
      userImages.value = newImages;
    }
  } catch (err) {
    console.error('获取用户图片失败:', err);
  } finally {
    loading.value = false;
  }
};

/**
 * 加载更多图片
 */
const loadMoreImages = async () => {
  if (loading.value || !hasMore.value) {
    return;
  }
  await getUserImages(true);
};

/**
 * 清空发布参数
 */
const clearPublishParams = () => {
  publishParams.value = {
    title: '',
    content: '',
    category: '其他',
    tags: [],
    photos: [],
    status: 'approved',
  };
  paginated.value = {
    page: 0,
    pageSize: 10,
    totalCount: 0,
    totalPage: 1,
    sortField: 'createTime',
    sortOrder: 'asc',
  };
  userImages.value = [];
};

/**
 * 发布帖子
 */
const publishPosts = async () => {
  try {
    // 开始加载动画
    useLoading().start({
      title: '发布中',
      description: '帖子正在发布中，请稍候...',
    });

    // 调用 API 发布帖子
    await usePostsApi().publishPost({
      ...publishParams.value,
    });

    // 停止加载动画
    useLoading().stop();

    // 显示发布成功的消息
    useMessage({
      name: '发布成功',
      description: '帖子已发布',
      type: 'success',
    });

    // 清空发布参数
    clearPublishParams();

    // 可选：返回上一页或跳转到其他页面
    // navigateTo('/mobile/mine');
  } catch (err) {
    // 停止加载动画
    useLoading().stop();

    // 显示发布失败的消息
    useMessage({
      name: '发布失败',
      description: '帖子发布失败，请稍后再试',
      type: 'error',
    });
  }
};

// 页面挂载时加载图片数据
onMounted(() => {
  getUserImages(false);
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);
}

.main-content {
  flex: 1;
  background: var(--background-elevated);
  overflow: auto;
  padding: 1rem;
}

.publish-btn {
  background-color: var(--button-primary);
  color: var(--text-primary);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 1.4rem;

  &:disabled {
    background-color: var(--button-cancel);
    opacity: 0.6;
  }
}

.publish-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;

  .form-item {
    flex: 1;
  }
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 1.4rem;
  font-weight: 500;
  color: var(--text-primary);
}

.image-selector-section {
  background: var(--background-base);
  border-radius: 1rem;
  padding: 1rem;
}
</style>

