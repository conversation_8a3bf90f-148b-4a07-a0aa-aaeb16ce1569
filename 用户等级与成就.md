# 用户等级与成就系统设计方案

## 系统概述

为"次元回廊"设计一个激励用户参与、增强社区互动的用户成长体系，包含等级系统和成就系统两大核心模块。系统将深度集成到现有平台中，通过视觉化展示和实质性奖励提升用户粘性。

## 系统架构

```mermaid
graph TD
    A[用户行为] --> B(等级系统)
    A --> C(成就系统)
    B --> D[等级特权]
    C --> E[成就徽章]
    D --> F[用户权益]
    E --> G[社交展示]
    F & G --> H[增强用户粘性]
```

## 一、等级系统设计

### 1. 经验值(EXP)获取机制

| 用户行为     | 基础 EXP | 每日上限 | 特殊条件         |
| ------------ | -------- | -------- | ---------------- |
| 每日登录     | 10       | 10       | -                |
| 上传图片     | 20       | 200      | 原创内容额外+5   |
| 获得点赞     | 5        | 100      | 精选内容双倍     |
| 获得收藏     | 10       | 200      | -                |
| 发表评论     | 5        | 50       | 高质量评论额外+3 |
| 关注用户     | 3        | 30       | -                |
| 参与社区活动 | 30-100   | -        | 根据活动级别     |
| 内容被精选   | 50       | -        | 无上限           |

### 2. 等级成长体系

```typescript
// 等级计算公式
const calculateLevel = (exp: number): number => {
  return Math.floor(5 * Math.log(exp / 100 + 1)) + 1
}

// 等级-经验对应表
const LEVEL_THRESHOLDS = [
  0, // Lv1
  100, // Lv2
  300, // Lv3
  600, // Lv4
  1000, // Lv5
  1500, // Lv6
  2100, // Lv7
  2800, // Lv8
  3600, // Lv9
  4500, // Lv10
  // ... 最高Lv100
]
```

### 3. 等级特权体系

| 等级段        | 特权内容                                             |
| ------------- | ---------------------------------------------------- |
| 1-10 (新手)   | 基础滤镜包、每日额外上传配额+5                       |
| 11-30 (进阶)  | 高级编辑工具、专属头像框、创建相册权限               |
| 31-50 (达人)  | Pro 级滤镜包、作品推广次数+3/月、专属主题            |
| 51-70 (大师)  | 1GB 额外云空间、优先审核通道、AI 修图工具            |
| 71-100 (宗师) | VIP 标识、专属客服、线下活动邀请、创作者基金申请资格 |

## 二、成就系统设计

### 1. 成就分类体系

```mermaid
graph LR
    A[成就系统] --> B(内容创作)
    A --> C(社区互动)
    A --> D(探索发现)
    A --> E(收集养成)
    A --> F(挑战活动)

    B --> B1[百图斩]
    B --> B2[精选大师]
    C --> C1[社交达人]
    C --> C2[评论专家]
    D --> D1[地点探索者]
    D --> D2[风格先锋]
    E --> E1[收藏家]
    E --> E2[全图鉴]
    F --> F1[活动冠军]
    F --> F2[连续挑战]
```

### 2. 核心成就示例

| 成就名称   | 获取条件                   | 稀有度 | 奖励                     |
| ---------- | -------------------------- | ------ | ------------------------ |
| 初露锋芒   | 上传第一张图片             | ★      | 新人徽章 + 100EXP        |
| 百图斩     | 累计上传 100 张图片        | ★★★    | 创作大师徽章 + 500EXP    |
| 万人迷     | 单张图片获 1000+点赞       | ★★★★   | 热门创作者徽章 + 1500EXP |
| 收藏家     | 收藏 100 张不同作品        | ★★     | 鉴赏家徽章 + 300EXP      |
| 摄影探险家 | 在 10 个不同地点拍摄并上传 | ★★★    | 旅行者徽章 + GPS 特效    |
| 全风格大师 | 使用所有滤镜类型发布作品   | ★★★★   | 艺术家徽章 + 专属滤镜    |
| 365 日坚持 | 连续登录 365 天            | ★★★★★  | 永恒徽章 + VIP 特权      |

### 3. 隐藏成就

- **夜之精灵**：在凌晨 2-5 点上传作品
- **色彩魔法师**：发布主色调相同的 9 张系列作品
- **时间旅行者**：上传 10 年前的老照片修复版
- **社区之心**：帮助 100 位新用户解答问题

## 三、技术实现方案

### 1. 数据库设计

```javascript
// MongoDB 模式设计

// 用户成长档案
const UserGrowthSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', unique: true },
  exp: { type: Number, default: 0 },
  level: { type: Number, default: 1 },
  achievements: [
    {
      achievementId: { type: Schema.Types.ObjectId, ref: 'Achievement' },
      unlockedAt: Date,
      progress: Number, // 用于进度型成就
      isHidden: Boolean, // 隐藏成就标识
    },
  ],
  dailyStats: {
    lastLogin: Date,
    loginStreak: Number,
    expEarnedToday: Number,
    actions: {
      uploads: Number,
      likesGiven: Number,
      comments: Number,
    },
  },
})

// 成就定义
const AchievementSchema = new Schema({
  name: String,
  description: String,
  icon: String, // 徽章图标URL
  type: {
    type: String,
    enum: ['single', 'progress', 'hidden', 'event'],
  },
  criteria: {
    target: Number, // 目标值
    actionType: String, // 关联行为类型
  },
  rewards: {
    exp: Number,
    badge: String,
    specialItem: String,
  },
  rarity: Number, // 1-5星
  unlockCount: { type: Number, default: 0 }, // 统计解锁人数
})
```

### 2. 成就追踪服务

```typescript
// achievement.service.ts

@Injectable()
export class AchievementService {
  constructor(
    @InjectModel('UserGrowth') private userGrowthModel,
    @InjectModel('Achievement') private achievementModel
  ) {}

  // 处理用户行为事件
  async handleUserAction(userId: string, actionType: string, value: number = 1) {
    // 1. 更新用户成长档案
    const growthDoc = await this.userGrowthModel.findOneAndUpdate(
      { userId },
      {
        $inc: {
          'exp': this.getExpForAction(actionType, value),
          'dailyStats.actions.$[action]': value,
        },
      },
      { new: true, upsert: true }
    )

    // 2. 检查等级提升
    const newLevel = this.calculateLevel(growthDoc.exp)
    if (newLevel > growthDoc.level) {
      await this.userGrowthModel.updateOne({ userId }, { $set: { level: newLevel } })
      this.triggerLevelUpEvent(userId, newLevel)
    }

    // 3. 检查成就解锁
    const relatedAchievements = await this.achievementModel.find({
      'criteria.actionType': actionType,
    })

    for (const achievement of relatedAchievements) {
      await this.checkAchievementProgress(userId, achievement, value)
    }
  }

  // 检查成就进度
  private async checkAchievementProgress(userId: string, achievement: any, increment: number) {
    const growthDoc = await this.userGrowthModel.findOne({ userId })
    const userAchievement = growthDoc.achievements.find((a) =>
      a.achievementId.equals(achievement._id)
    )

    let currentProgress = userAchievement?.progress || 0
    let newProgress = currentProgress + increment

    // 检查是否解锁成就
    if (newProgress >= achievement.criteria.target) {
      await this.unlockAchievement(userId, achievement._id)
    } else {
      // 更新进度
      await this.userGrowthModel.updateOne(
        { userId, 'achievements.achievementId': achievement._id },
        { $set: { 'achievements.$.progress': newProgress } }
      )
    }
  }

  // 解锁成就
  private async unlockAchievement(userId: string, achievementId: Types.ObjectId) {
    const achievement = await this.achievementModel.findById(achievementId)

    await this.userGrowthModel.updateOne(
      { userId },
      {
        $push: {
          achievements: {
            achievementId,
            unlockedAt: new Date(),
            progress: achievement.criteria.target,
            isHidden: achievement.type === 'hidden',
          },
        },
        $inc: { exp: achievement.rewards.exp || 0 },
      }
    )

    this.triggerAchievementUnlockedEvent(userId, achievement)
  }
}
```

### 3. 实时事件处理

```typescript
// achievement.events.ts

// 等级提升事件
function triggerLevelUpEvent(userId: string, newLevel: number) {
  // 1. 发送实时通知
  socketServer.to(`user_${userId}`).emit('level-up', {
    level: newLevel,
    rewards: this.getLevelRewards(newLevel),
  })

  // 2. 添加通知消息
  notificationService.create({
    userId,
    type: 'level',
    content: `恭喜！你已晋升到Lv${newLevel}`,
    rewards: this.getLevelRewards(newLevel),
  })

  // 3. 解锁等级特权
  privilegeService.unlockLevelPrivileges(userId, newLevel)
}

// 成就解锁事件
function triggerAchievementUnlockedEvent(userId: string, achievement: any) {
  // 1. 全服公告（稀有成就）
  if (achievement.rarity >= 4) {
    socketServer.emit('rare-achievement', {
      userId,
      achievement: achievement.name,
      icon: achievement.icon,
    })
  }

  // 2. 个人通知
  socketServer.to(`user_${userId}`).emit('achievement-unlocked', achievement)

  // 3. 添加成就到个人档案
  profileService.addAchievement(userId, achievement)
}
```

## 四、前端实现方案

### 1. 个人成长面板

```vue
<template>
  <div class="growth-panel">
    <!-- 等级展示 -->
    <div class="level-card">
      <div class="level-badge">Lv{{ userLevel }}</div>
      <div class="progress-bar">
        <div :style="{ width: progressPercentage + '%' }"></div>
        <span>{{ currentExp }} / {{ nextLevelExp }} EXP</span>
      </div>
    </div>

    <!-- 特权展示 -->
    <div class="privileges-section">
      <h3>当前特权</h3>
      <div class="privileges-grid">
        <div
          v-for="privilege in unlockedPrivileges"
          :key="privilege.id">
          <Icon :name="privilege.icon" />
          <span>{{ privilege.name }}</span>
        </div>
      </div>
    </div>

    <!-- 成就展示 -->
    <div class="achievements-section">
      <div class="achievements-filter">
        <button
          v-for="cat in categories"
          :key="cat"
          @click="filterByCategory(cat)">
          {{ cat }}
        </button>
      </div>

      <div class="achievements-grid">
        <AchievementBadge
          v-for="achievement in filteredAchievements"
          :key="achievement.id"
          :achievement="achievement"
          :unlocked="isUnlocked(achievement.id)"
          :progress="getProgress(achievement.id)" />
      </div>
    </div>
  </div>
</template>

<script setup>
// 成就徽章组件
const AchievementBadge = {
  props: ['achievement', 'unlocked', 'progress'],
  template: `
    <div :class="['achievement-badge', { locked: !unlocked }]">
      <div class="badge-icon">
        <img v-if="unlocked" :src="achievement.icon" alt="achievement">
        <div v-else class="mystery-icon">?</div>
      </div>
      <div class="badge-info">
        <h4>{{ unlocked ? achievement.name : '未解锁成就' }}</h4>
        <p>{{ unlocked ? achievement.description : '达成条件后解锁' }}</p>
        <div v-if="achievement.type === 'progress'" class="progress">
          <div :style="{ width: progressPercentage + '%' }"></div>
          <span>{{ progress }}/{{ achievement.criteria.target }}</span>
        </div>
      </div>
    </div>
  `,
}
</script>

<style scoped>
/* 专业级视觉设计 */
.growth-panel {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 16px;
  padding: 2rem;
  color: #fff;
}

.level-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
}

.level-badge {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #ff9a9e 0%, #fad0c4 100%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
}

.progress-bar {
  flex: 1;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.progress-bar > div {
  height: 100%;
  background: linear-gradient(90deg, #43cea2 0%, #185a9d 100%);
  transition: width 0.5s ease;
}

.privileges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.achievement-badge {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
  }

  &.locked {
    opacity: 0.6;
    .badge-icon {
      filter: grayscale(100%);
    }
  }
}

.mystery-icon {
  width: 60px;
  height: 60px;
  background: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: bold;
}
</style>
```

### 2. 实时通知组件

```vue
<template>
  <transition name="slide-up">
    <div
      v-if="notification"
      class="notification"
      :class="notification.type">
      <div class="icon">
        <img
          v-if="notification.icon"
          :src="notification.icon" />
        <Icon
          v-else
          name="ph:medal" />
      </div>
      <div class="content">
        <h3>{{ notification.title }}</h3>
        <p>{{ notification.message }}</p>
      </div>
      <button
        class="close"
        @click="notification = null">
        ×
      </button>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useSocket } from '@/composables/useSocket'

const socket = useSocket()
const notification = ref(null)

onMounted(() => {
  socket.on('level-up', (data) => {
    showNotification({
      type: 'level',
      title: `升级到Lv${data.level}!`,
      message: `你获得了新的特权：${data.rewards.join(', ')}`,
      icon: '/icons/level-up.svg',
    })
  })

  socket.on('achievement-unlocked', (data) => {
    showNotification({
      type: 'achievement',
      title: '成就解锁!',
      message: data.name,
      icon: data.icon,
    })
  })
})

function showNotification(notif) {
  notification.value = notif
  setTimeout(() => {
    notification.value = null
  }, 5000)
}
</script>

<style scoped>
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  width: 350px;
  border-left: 4px solid var(--border-color);
}

.notification.level {
  --border-color: #43cea2;
}

.notification.achievement {
  --border-color: #ff9a9e;
}

.icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.icon img {
  width: 70%;
  height: 70%;
}

.content h3 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  color: var(--border-color);
}

.close {
  background: none;
  border: none;
  color: #aaa;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  margin-left: auto;
}

/* 动画效果 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-50px);
}
</style>
```

## 五、进阶功能设计

### 1. 成就进度预测系统

```typescript
// 预测用户解锁成就所需时间
function predictAchievementTime(userId: string, achievementId: string) {
  // 1. 获取用户历史行为数据
  const userStats = getUserBehaviorStats(userId)

  // 2. 获取成就要求
  const achievement = getAchievement(achievementId)

  // 3. 计算每日平均进度
  const dailyAverage = calculateDailyAverage(userStats, achievement.criteria.actionType)

  // 4. 计算剩余天数和预估日期
  const remaining = achievement.criteria.target - getCurrentProgress(userId, achievementId)
  const daysNeeded = Math.ceil(remaining / dailyAverage)

  return {
    days: daysNeeded,
    estimatedDate: new Date(Date.now() + daysNeeded * 86400000),
  }
}
```

### 2. 动态成就生成器

```typescript
// 基于用户行为生成个性化成就
function generatePersonalizedAchievements(userId: string) {
  const userBehavior = analyzeUserBehavior(userId)

  return [
    {
      name: `${userBehavior.topColor}爱好者`,
      description: `发布10张以${userBehavior.topColor}为主色调的作品`,
      criteria: {
        actionType: 'upload',
        condition: `colorDominance('${userBehavior.topColor}') >= 0.7`,
        target: 10,
      },
    },
    {
      name: `${userBehavior.favLocation}常客`,
      description: `在${userBehavior.favLocation}拍摄5张作品`,
      criteria: {
        actionType: 'upload',
        condition: `location == '${userBehavior.favLocation}'`,
        target: 5,
      },
    },
  ]
}
```

### 3. 社交分享系统

```vue
<template>
  <div class="share-achievement">
    <div class="preview-card">
      <img
        :src="achievement.shareImage"
        alt="成就分享" />
    </div>
    <div class="share-options">
      <button @click="shareToWechat">
        <Icon name="logos:wechat" />
        微信分享
      </button>
      <button @click="shareToWeibo">
        <Icon name="logos:weibo" />
        微博分享
      </button>
      <button @click="downloadImage">
        <Icon name="ph:download-simple" />
        保存图片
      </button>
    </div>
  </div>
</template>

<script setup>
// 生成成就分享卡片
function generateShareImage(achievement) {
  // 使用Canvas生成包含用户信息、成就徽章和平台LOGO的分享图
  // 返回图片DataURL
}
</script>
```

## 六、运营与分析

### 1. 成就数据分析看板

| 指标         | 描述                         |
| ------------ | ---------------------------- |
| 成就解锁率   | 各成就解锁用户比例           |
| 平均解锁时间 | 用户达成成就的平均时间       |
| 热门成就     | 解锁人数最多的成就           |
| 稀有成就     | 解锁人数最少的成就           |
| 成就漏斗     | 用户从开始到完成成就的转化率 |

### 2. 等级分布统计

```mermaid
pie
    title 用户等级分布
    “Lv1-10” : 35
    “Lv11-30” : 25
    “Lv31-50” : 20
    “Lv51-70” : 15
    “Lv71-100” : 5
```

### 3. 激励策略优化

- **动态调整机制**：根据成就解锁率调整新成就难度
- **赛季重置**：每季度重置部分成就并提供新奖励
- **社区投票**：用户投票决定下季度的成就主题
- **成就交换**：允许用户交换重复获得的成就徽章

## 总结

该等级与成就系统设计具有以下特点：

1. **深度集成**：与现有图片分享功能无缝结合
2. **双重激励**：等级提供实用特权，成就满足收集欲望
3. **个性化体验**：基于用户行为的动态成就生成
4. **社交传播**：成就分享促进平台增长
5. **数据驱动**：完善的运营分析系统支持持续优化

系统上线后预计可提升以下核心指标：

- 用户日活提升 30%-50%
- 内容产出量提升 40%-60%
- 用户留存率提升 25%-40%
- 社交互动量提升 50%-80%

建议采用分阶段上线策略：

1. 第一阶段：基础等级系统（1-4 周）
2. 第二阶段：核心成就系统（5-8 周）
3. 第三阶段：进阶功能与个性化成就（9-12 周）
