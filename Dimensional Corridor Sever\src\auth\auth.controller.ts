import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Req,
  Res,
} from '@nestjs/common';
import { AuthService } from './auth.service'; // 引入 AuthService，用于处理认证逻辑
import { Public } from '../common/decorator/public.decorator'; // 引入 Public 装饰器，标记路由为公共，无需验证
import { Request, Response } from 'express';
import { formatResponse } from 'src/common/utils';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { LoginDto } from './dtos/login.dto';

@Controller('auth') // 定义控制器路由为 'auth'
export class AuthController {
  constructor(private authService: AuthService) {} // 注入 AuthService

  @ApiOperation({
    summary:
      '使用邮箱或手机号以及密码登录接口，邮箱与手机号二选一、密码和验证码必填-公开路由',
  })
  @ApiOkResponse({ description: '返回用户登录信息' })
  // !: 用户登录接口
  @Post('login-password')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async loginByPassword(
    @Body()
    loginDto: LoginDto,
    @Req() req: Request,
    @Res() res: Response
  ) {
    // 获取cookie中的验证码token
    const { svg_token } = req.cookies;
    // 执行登录逻辑
    const { refresh_token, access_token } =
      await this.authService.loginByPassword(
        loginDto.email || loginDto.phone || '',
        loginDto.password,
        {
          token: svg_token as string,
          code: loginDto.code,
        },
        req
      );

    // 设置刷新令牌的 Cookie
    res.cookie('refresh_token', refresh_token, {
      httpOnly: true, // 禁止 JavaScript 读取
      secure: true, // 仅通过 HTTPS 传输
      sameSite: 'none', // 跨域时允许 Cookie 被发送
      maxAge: 7 * 24 * 3600 * 1000, // 过期时间（毫秒）
      path: '/auth', // 限制 Cookie 仅对auth前缀的接口有效
    });

    res.status(200).json(formatResponse(200, '登录成功', access_token)); // 返回登录信息
  }

  @ApiOperation({ summary: '刷新令牌的接口-公开路由' })
  @ApiOkResponse({ description: '返回新的访问令牌' })
  // !: 刷新令牌接口，用于刷新用户的访问令牌
  @Get('refresh-token') // 定义 GET 请求路由为 'refresh-token'
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async refreshToken(@Req() req: Request, @Res() res: Response) {
    // 获取刷新令牌并刷新令牌
    const { refresh_token } = req.cookies;

    if (!refresh_token) {
      throw new HttpException('刷新令牌无效', HttpStatusEnum.BAD_REQUEST);
    }
    // 执行刷新逻辑
    const token = await this.authService.refreshToken(refresh_token as string, {
      ip: req.ip || '', // 获取客户端 IP 地址
      deviceInfo: req.headers['user-agent'] || '', // 获取客户端设备信息
    });
    // 设置新的访问令牌的 Cookie
    res.cookie('refresh_token', token.refresh_token, {
      httpOnly: true, // 禁止 JavaScript 读取
      secure: true, // 仅通过 HTTPS 传输
      sameSite: 'none', // 跨域时允许 Cookie 被发送
      maxAge: 7 * 24 * 3600 * 1000, // 过期时间（毫秒）
      path: '/auth', // 限制 Cookie 仅对auth前缀的接口有效
    });
    res.status(200).json(formatResponse(200, '刷新成功', token.access_token)); // 返回登录信息
  }

  @ApiOperation({ summary: '退出登录接口-用户路由' })
  @ApiOkResponse({ description: '返回退出登录状态' })
  // !: 定义POST请求处理方法，用于退出登录
  @Post('logout')
  async logout(@Req() req: Request) {
    // 获取刷新令牌并刷新令牌
    const { refresh_token } = req.cookies;

    await this.authService.logout(refresh_token as string, req.user.id);
    return formatResponse(HttpStatusEnum.OK, '退出登录成功');
  }
}
