/**
 * 基础验证参数接口
 * @property {string} [phone] - 可选，手机号码（与邮箱二选一）
 * @property {string} [email] - 可选，电子邮箱（与手机二选一）
 * @property {string} password - 用户密码
 * @property {string} code - 验证码
 */
interface ApiVerifyParams {
  phone?: string;
  email?: string;
  password: string;
  code: string;
}

/**
 * 邮箱/手机登录接口参数类型
 * 继承自 ApiVerifyParams 基础验证参数
 */
type ApiloginByEmailOrPhoneParams = ApiVerifyParams;

/**
 * 邮箱/手机注册接口参数类型
 * 继承自 ApiVerifyParams 基础验证参数
 */
type ApiregisterByEmailOrPhoneParams = ApiVerifyParams;

/**
 * 找回密码接口参数类型
 * 继承自 ApiVerifyParams 基础验证参数
 */
type ApiRetrievePasswordParams = ApiVerifyParams;
