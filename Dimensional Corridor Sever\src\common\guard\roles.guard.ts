import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorator/roles.decorator';
import { Request } from 'express';
import { UsersService } from 'src/users/service/users.service';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private usersService: UsersService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 获取路由要求的角色
    const requiredRoles =
      this.reflector.get<string[]>(ROLES_KEY, context.getHandler()) || [];

    if (requiredRoles.length === 0) {
      return true; // 未设置角色限制则直接放行
    }

    // 获取用户角色
    const request = context.switchToHttp().getRequest<Request>();
    const user = request.user;
    if (!user.role) {
      throw new HttpException('用户角色信息缺失', HttpStatusEnum.UNAUTHORIZED);
    }

    // 校验角色权限是否和数据库中一致
    const userRole = (await this.usersService.findOneById(user.id)).role;
    if (userRole !== user.role) {
      throw new HttpException('权限不足', HttpStatusEnum.UNAUTHORIZED);
    }

    // 校验角色权限
    const hasPermission = requiredRoles.includes(user.role);
    if (!hasPermission) {
      throw new HttpException('权限不足', HttpStatusEnum.UNAUTHORIZED);
    }

    return true;
  }
}

// ! 注意：在使用守卫时，需要在路由装饰器上添加 @UseGuards(RolesGuard)
// @UseGuards(AuthGuard, RolesGuard) // 组合守卫
// @Roles('admin', 'super_admin') // 限制角色为管理员
// @Get('admin/dashboard')
// getDashboard() {
//   return { message: '管理员面板' };
// }
