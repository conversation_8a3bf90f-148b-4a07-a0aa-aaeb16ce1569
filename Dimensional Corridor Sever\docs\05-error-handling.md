# WebSocket 错误处理

## 📋 概述

本文档详细介绍WebSocket系统中的错误处理机制，包括连接错误、消息错误、权限错误等各种异常情况的处理方法。

## 🚨 错误类型

### 1. 连接错误
```javascript
socket.on('connect_error', (error) => {
  console.error('连接错误:', error.message);
  
  switch (error.message) {
    case '用户未登录':
      handleAuthError();
      break;
    case 'Token已过期':
      handleTokenExpired();
      break;
    case '权限不足':
      handlePermissionDenied();
      break;
    case '服务器维护中':
      handleServerMaintenance();
      break;
    default:
      handleUnknownError(error);
  }
});

// 处理认证错误
function handleAuthError() {
  console.log('认证失败，跳转到登录页面');
  localStorage.removeItem('jwt_token');
  window.location.href = '/login';
}

// 处理Token过期
function handleTokenExpired() {
  console.log('Token过期，尝试刷新Token');
  refreshTokenAndReconnect();
}

// 处理权限不足
function handlePermissionDenied() {
  console.log('权限不足，显示错误提示');
  showErrorMessage('您没有权限访问此功能');
}

// 处理服务器维护
function handleServerMaintenance() {
  console.log('服务器维护中');
  showMaintenanceMessage();
}
```

### 2. 消息错误
```javascript
// 监听通用错误事件
socket.on('error', (error) => {
  console.error('操作错误:', error);
  
  switch (error.type) {
    case 'MESSAGE_TOO_LONG':
      showErrorMessage('消息内容过长，请缩短后重试');
      break;
    case 'INVALID_RECEIVER':
      showErrorMessage('接收者不存在或不可用');
      break;
    case 'GROUP_NOT_FOUND':
      showErrorMessage('群组不存在');
      break;
    case 'NOT_IN_GROUP':
      showErrorMessage('您不在此群组中');
      break;
    case 'RATE_LIMIT_EXCEEDED':
      showErrorMessage('发送消息过于频繁，请稍后再试');
      break;
    default:
      showErrorMessage('操作失败，请重试');
  }
});
```

### 3. 网络错误
```javascript
// 监听断开连接
socket.on('disconnect', (reason) => {
  console.log('连接断开:', reason);
  
  switch (reason) {
    case 'io server disconnect':
      // 服务器主动断开
      handleServerDisconnect();
      break;
    case 'io client disconnect':
      // 客户端主动断开
      console.log('客户端主动断开连接');
      break;
    case 'ping timeout':
      // 心跳超时
      handlePingTimeout();
      break;
    case 'transport close':
      // 传输层关闭
      handleTransportClose();
      break;
    case 'transport error':
      // 传输层错误
      handleTransportError();
      break;
    default:
      handleUnknownDisconnect(reason);
  }
});

function handleServerDisconnect() {
  showErrorMessage('服务器连接断开，正在尝试重连...');
  // 自动重连逻辑会处理
}

function handlePingTimeout() {
  showErrorMessage('网络连接不稳定，正在重连...');
}

function handleTransportClose() {
  showErrorMessage('网络连接中断');
}
```

## 🔄 错误恢复机制

### 1. 自动重连
```javascript
class ErrorRecoverySocket {
  constructor(url, options = {}) {
    this.url = url;
    this.options = options;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.backoffFactor = 2;
    this.maxReconnectDelay = 30000;
    
    this.connect();
  }

  connect() {
    this.socket = io(this.url, {
      ...this.options,
      autoConnect: false
    });

    this.bindEvents();
    this.socket.connect();
  }

  bindEvents() {
    // 连接成功
    this.socket.on('connect', () => {
      console.log('✅ 连接成功');
      this.reconnectAttempts = 0;
      this.onConnected?.();
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('❌ 连接错误:', error.message);
      this.handleConnectError(error);
    });

    // 断开连接
    this.socket.on('disconnect', (reason) => {
      console.log('🔌 连接断开:', reason);
      this.handleDisconnect(reason);
    });

    // 重连尝试
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`🔄 重连尝试 ${attemptNumber}`);
      this.onReconnectAttempt?.(attemptNumber);
    });

    // 重连失败
    this.socket.on('reconnect_failed', () => {
      console.error('🚫 重连失败');
      this.onReconnectFailed?.();
    });
  }

  handleConnectError(error) {
    if (error.message === 'Token已过期') {
      this.refreshTokenAndReconnect();
    } else if (error.message === '用户未登录') {
      this.onAuthRequired?.();
    } else {
      this.scheduleReconnect();
    }
  }

  handleDisconnect(reason) {
    if (reason === 'io server disconnect') {
      // 服务器主动断开，尝试重连
      this.scheduleReconnect();
    }
    // 其他原因的断开由Socket.IO自动处理
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🚫 达到最大重连次数');
      this.onMaxReconnectAttemptsReached?.();
      return;
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(this.backoffFactor, this.reconnectAttempts),
      this.maxReconnectDelay
    );

    this.reconnectAttempts++;
    
    console.log(`⏰ ${delay}ms后进行第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      this.socket.connect();
    }, delay);
  }

  async refreshTokenAndReconnect() {
    try {
      console.log('🔄 刷新Token...');
      const newToken = await this.refreshToken();
      
      // 更新认证信息
      this.options.auth = { token: `Bearer ${newToken}` };
      
      // 重新连接
      this.socket.disconnect();
      this.socket = io(this.url, this.options);
      this.bindEvents();
      
    } catch (error) {
      console.error('Token刷新失败:', error);
      this.onAuthRequired?.();
    }
  }

  async refreshToken() {
    // 实现Token刷新逻辑
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('refresh_token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Token刷新失败');
    }

    const data = await response.json();
    localStorage.setItem('jwt_token', data.accessToken);
    return data.accessToken;
  }

  // 事件回调
  onConnected = null;
  onReconnectAttempt = null;
  onReconnectFailed = null;
  onMaxReconnectAttemptsReached = null;
  onAuthRequired = null;
}
```

### 2. 消息重发机制
```javascript
class ReliableMessageSender {
  constructor(socket) {
    this.socket = socket;
    this.pendingMessages = new Map();
    this.messageId = 0;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
    
    this.bindEvents();
  }

  bindEvents() {
    // 监听消息确认
    this.socket.on('message_ack', (data) => {
      this.handleMessageAck(data);
    });

    // 监听消息错误
    this.socket.on('message_error', (data) => {
      this.handleMessageError(data);
    });

    // 连接恢复后重发待确认消息
    this.socket.on('connect', () => {
      this.resendPendingMessages();
    });
  }

  // 发送可靠消息
  sendReliableMessage(event, data, options = {}) {
    const messageId = ++this.messageId;
    const message = {
      id: messageId,
      event,
      data,
      timestamp: Date.now(),
      attempts: 0,
      maxAttempts: options.maxAttempts || this.retryAttempts
    };

    this.pendingMessages.set(messageId, message);
    this.sendMessage(message);

    return messageId;
  }

  sendMessage(message) {
    message.attempts++;
    
    // 发送带有消息ID的消息
    this.socket.emit(message.event, {
      ...message.data,
      _messageId: message.id
    });

    // 设置超时重发
    setTimeout(() => {
      if (this.pendingMessages.has(message.id)) {
        this.retryMessage(message);
      }
    }, this.retryDelay);
  }

  retryMessage(message) {
    if (message.attempts >= message.maxAttempts) {
      console.error(`消息发送失败，已达到最大重试次数:`, message);
      this.pendingMessages.delete(message.id);
      this.onMessageFailed?.(message);
      return;
    }

    console.log(`重试发送消息 ${message.id}, 第${message.attempts + 1}次尝试`);
    this.sendMessage(message);
  }

  handleMessageAck(data) {
    const messageId = data.messageId;
    if (this.pendingMessages.has(messageId)) {
      console.log(`消息 ${messageId} 发送成功`);
      this.pendingMessages.delete(messageId);
      this.onMessageSuccess?.(messageId);
    }
  }

  handleMessageError(data) {
    const messageId = data.messageId;
    const message = this.pendingMessages.get(messageId);
    
    if (message) {
      console.error(`消息 ${messageId} 发送错误:`, data.error);
      
      if (data.error.type === 'PERMANENT_ERROR') {
        // 永久性错误，不重试
        this.pendingMessages.delete(messageId);
        this.onMessageFailed?.(message, data.error);
      } else {
        // 临时性错误，可以重试
        this.retryMessage(message);
      }
    }
  }

  resendPendingMessages() {
    console.log(`重发 ${this.pendingMessages.size} 条待确认消息`);
    
    for (const message of this.pendingMessages.values()) {
      // 重置尝试次数
      message.attempts = 0;
      this.sendMessage(message);
    }
  }

  // 获取待确认消息数量
  getPendingMessageCount() {
    return this.pendingMessages.size;
  }

  // 清除所有待确认消息
  clearPendingMessages() {
    this.pendingMessages.clear();
  }

  // 事件回调
  onMessageSuccess = null;
  onMessageFailed = null;
}

// 使用示例
const reliableSender = new ReliableMessageSender(socket);

reliableSender.onMessageSuccess = (messageId) => {
  console.log(`消息 ${messageId} 发送成功`);
};

reliableSender.onMessageFailed = (message, error) => {
  console.error(`消息发送失败:`, message, error);
  showErrorMessage('消息发送失败，请重试');
};

// 发送可靠消息
const messageId = reliableSender.sendReliableMessage('private_message', {
  receiverId: 'user123',
  content: '重要消息'
});
```

## 🛡️ 错误预防

### 1. 输入验证
```javascript
class MessageValidator {
  static validatePrivateMessage(data) {
    const errors = [];

    if (!data.receiverId) {
      errors.push('接收者ID不能为空');
    } else if (typeof data.receiverId !== 'string') {
      errors.push('接收者ID必须是字符串');
    }

    if (!data.content) {
      errors.push('消息内容不能为空');
    } else if (typeof data.content !== 'string') {
      errors.push('消息内容必须是字符串');
    } else if (data.content.length > 1000) {
      errors.push('消息内容不能超过1000字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateGroupMessage(data) {
    const errors = [];

    if (!data.groupId) {
      errors.push('群组ID不能为空');
    } else if (typeof data.groupId !== 'string') {
      errors.push('群组ID必须是字符串');
    }

    if (!data.content) {
      errors.push('消息内容不能为空');
    } else if (typeof data.content !== 'string') {
      errors.push('消息内容必须是字符串');
    } else if (data.content.length > 1000) {
      errors.push('消息内容不能超过1000字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateUserStatus(data) {
    const errors = [];
    const validStatuses = ['online', 'away', 'busy'];

    if (!data.status) {
      errors.push('状态不能为空');
    } else if (!validStatuses.includes(data.status)) {
      errors.push(`状态必须是以下之一: ${validStatuses.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// 使用验证器
function sendPrivateMessage(receiverId, content) {
  const validation = MessageValidator.validatePrivateMessage({
    receiverId,
    content
  });

  if (!validation.isValid) {
    showErrorMessage(validation.errors.join(', '));
    return false;
  }

  socket.emit('private_message', { receiverId, content });
  return true;
}
```

### 2. 频率限制
```javascript
class RateLimiter {
  constructor(maxRequests = 10, timeWindow = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
    this.requests = [];
  }

  canMakeRequest() {
    const now = Date.now();
    
    // 清除过期的请求记录
    this.requests = this.requests.filter(
      timestamp => now - timestamp < this.timeWindow
    );

    // 检查是否超过限制
    if (this.requests.length >= this.maxRequests) {
      return false;
    }

    // 记录新请求
    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest() {
    if (this.requests.length < this.maxRequests) {
      return 0;
    }

    const oldestRequest = Math.min(...this.requests);
    const timeUntilExpiry = this.timeWindow - (Date.now() - oldestRequest);
    return Math.max(0, timeUntilExpiry);
  }
}

// 使用频率限制器
const messageLimiter = new RateLimiter(5, 10000); // 10秒内最多5条消息

function sendMessageWithRateLimit(type, data) {
  if (!messageLimiter.canMakeRequest()) {
    const waitTime = messageLimiter.getTimeUntilNextRequest();
    showErrorMessage(`发送消息过于频繁，请等待 ${Math.ceil(waitTime / 1000)} 秒`);
    return false;
  }

  socket.emit(type, data);
  return true;
}
```

## 📊 错误监控

### 1. 错误统计
```javascript
class ErrorMonitor {
  constructor() {
    this.errorCounts = new Map();
    this.errorHistory = [];
    this.maxHistorySize = 100;
  }

  recordError(type, error, context = {}) {
    // 统计错误次数
    const count = this.errorCounts.get(type) || 0;
    this.errorCounts.set(type, count + 1);

    // 记录错误历史
    const errorRecord = {
      type,
      error: error.message || error,
      context,
      timestamp: Date.now()
    };

    this.errorHistory.push(errorRecord);

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }

    // 发送错误报告 (可选)
    this.sendErrorReport(errorRecord);
  }

  getErrorStats() {
    return {
      totalErrors: this.errorHistory.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      recentErrors: this.errorHistory.slice(-10)
    };
  }

  sendErrorReport(errorRecord) {
    // 发送错误报告到监控服务
    if (this.shouldReportError(errorRecord)) {
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorRecord)
      }).catch(err => {
        console.error('发送错误报告失败:', err);
      });
    }
  }

  shouldReportError(errorRecord) {
    // 决定是否需要报告此错误
    const criticalErrors = ['connect_error', 'auth_error', 'server_error'];
    return criticalErrors.includes(errorRecord.type);
  }
}

// 使用错误监控
const errorMonitor = new ErrorMonitor();

// 在各种错误处理中记录错误
socket.on('connect_error', (error) => {
  errorMonitor.recordError('connect_error', error, {
    url: socket.io.uri,
    transport: socket.io.engine?.transport?.name
  });
});

socket.on('error', (error) => {
  errorMonitor.recordError('message_error', error);
});
```

## 🔗 相关文档

- [连接与认证](./01-connection-auth.md)
- [消息传递功能](./02-messaging.md)
- [用户状态管理](./03-user-status.md)
- [群组管理](./04-group-management.md)
