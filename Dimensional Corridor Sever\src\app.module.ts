import { Logger, Module } from '@nestjs/common'; // 导入 NestJS 的 Module 装饰器
import * as config from 'config'; // 导入 config 模块，用于读取配置文件
import { MongooseModule } from '@nestjs/mongoose'; // 导入 MongooseModule 类，用于与 MongoDB 进行交互
import { UsersModule } from './users/users.module'; // 导入 UserModule 类
import { Connection } from 'mongoose'; // 导入 Mongoose 的 Connection 类
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler'; // 导入 ThrottlerModule 和 ThrottlerGuard，用于限流
import { APP_GUARD } from '@nestjs/core'; // 导入 APP_GUARD，用于全局注册守卫
import { AuthModule } from './auth/auth.module'; // 导入 AuthModule 类
import { RedisModule } from './redis/redis.module';
import { WebsocketModule } from './websocket/websocket.module';
import { PhotosModule } from './photos/photos.module';
import { CaptchaModule } from './captcha/captcha.module';
import { ContentsModule } from './contents/contents.module';
import { AdminsModule } from './admins/admins.module';
import { AichatModule } from './aichat/aichat.module';
import { SearchModule } from './search/search.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CounterModule } from './counter/counter.module';

// 从配置文件中获取数据库的 host, port 和 name
const database = config.get<DatabaseConfig>('database');
const redis = config.get<DatabaseConfig>('redis');

@Module({
  imports: [
    // 定义模块的导入部分
    // 配置 Mongoose 连接到 MongoDB 数据库
    MongooseModule.forRoot(
      `mongodb://${database.username ? database.username + ':' + database.password + '@' : ''}${database.host}:${database.port}/${database.name}`,
      {
        onConnectionCreate: (connection: Connection) => {
          // 当连接创建时执行的回调函数
          // 连接事件监听器（当前注释掉，可以根据需要启用）
          connection.on('connected', () => {
            Logger.log('mongoDB 数据库连接成功', 'mongoDB');
          });
          // connection.on('open', () => console.log('数据库连接成功'));
          connection.on('disconnected', () => {
            Logger.error('mongoDB 数据库连接断开', 'mongoDB');
          });
          // connection.on('reconnected', () => console.log('数据库重新连接'));
          // connection.on('disconnecting', () => console.log('数据库断开连接中'));
          return connection; // 返回连接对象
        },
      }
    ),
    ScheduleModule.forRoot(), // 初始化定时任务模块
    // 配置 Redis 模块
    RedisModule.forRoot({
      host: redis.host,
      port: redis.port,
      password: redis.password,
      ttl: 60,
    }),
    // 配置限流器模块
    ThrottlerModule.forRoot({
      throttlers: [
        // 定义限流规则
        {
          name: 'short', // 规则名称
          ttl: 1000, // 时间窗口（毫秒），1秒
          limit: 3, // 在时间窗口内允许的最大请求数
        },
        {
          name: 'medium', // 规则名称
          ttl: 10000, // 时间窗口（毫秒），10秒
          limit: 20, // 在时间窗口内允许的最大请求数
        },
        {
          name: 'long', // 规则名称
          ttl: 60000, // 时间窗口（毫秒），1分钟
          limit: 100, // 在时间窗口内允许的最大请求数
        },
      ],
    }),
    WebsocketModule, // 导入 WebsocketModule
    AuthModule, // 导入 AuthModule
    UsersModule,
    PhotosModule,
    CaptchaModule,
    ContentsModule,
    CounterModule,
    AdminsModule,
    AichatModule,
    SearchModule,
  ],
  controllers: [], // 定义该模块的控制器（当前为空）
  providers: [
    // 定义该模块的服务提供者
    {
      provide: APP_GUARD, // 提供全局守卫
      useClass: ThrottlerGuard, // 使用 ThrottlerGuard 作为全局限流守卫
    },
  ],
})
export class AppModule {} // 导出 AppModule 类
