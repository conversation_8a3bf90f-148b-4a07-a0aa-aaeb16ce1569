<template>
  <div>
    <RikkaDialog
      :show="show"
      title="账号安全"
      :show-footer="false"
      :width="dialogWidth"
      @close="close"
    >
      <!-- 邮箱信息展示及操作区域 -->
      <div class="email">
        <div class="icon">
          <IconSvg name="email" size="2rem" color="#6366f1" />
        </div>
        <div class="value">
          <div class="title">电子邮箱</div>
          <div class="content">
            {{ encryptContact(authStore.authStore?.email || '') || '未绑定' }}
          </div>
        </div>
        <div class="btn">
          <RippleButton
            rippleColor="#fff"
            @click="emailChangeDialogFlag = true"
          >
            <div class="main">
              <IconSvg name="refresh" size="1rem" color="#fff" />
              <span>
                {{ authStore.authStore?.email ? '更换邮箱' : '绑定邮箱' }}</span
              >
            </div>
          </RippleButton>
        </div>
      </div>

      <!-- 手机号信息展示及操作区域 -->
      <div class="phone">
        <div class="icon">
          <IconSvg name="phone" size="2rem" color="#10b981" />
        </div>
        <div class="value">
          <div class="title">电话号码</div>
          <div class="content">
            {{ encryptContact(authStore.authStore?.phone || '') || '未绑定' }}
          </div>
        </div>
        <div class="btn">
          <RippleButton
            rippleColor="#fff"
            @click="phoneChangeDialogFlag = true"
          >
            <div class="main">
              <IconSvg name="refresh" size="1rem" color="#fff" />
              <span>
                {{ authStore.authStore?.phone ? '更换手机' : '绑定手机' }}</span
              >
            </div>
          </RippleButton>
        </div>
      </div>

      <!-- 密码信息展示及操作区域 -->
      <div class="password">
        <div class="icon">
          <IconSvg name="key" size="2rem" color="orange" />
        </div>
        <div class="value">
          <div class="title">密码</div>
          <div class="content">********</div>
        </div>
        <div class="btn">
          <RippleButton
            rippleColor="#fff"
            @click="passwordChangeDialogFlag = true"
          >
            <div class="main">
              <IconSvg name="refresh" size="1rem" color="#fff" />
              <span> 更换密码</span>
            </div>
          </RippleButton>
        </div>
      </div>

      <!-- 账户注销操作区域 -->
      <div class="delete">
        <div class="icon">
          <IconSvg name="cancellationAccount" size="2rem" color="red" />
        </div>
        <div class="value">
          <div class="title">注销账户</div>
          <div class="content">永久删除您的账户及所有数据</div>
        </div>
        <div class="btn">
          <RippleButton
            rippleColor="red"
            @click="deleteAccountDialogFlag = true"
          >
            <div class="main">
              <IconSvg name="delete" size="1rem" color="red" />
              <span>注销账户</span>
            </div>
          </RippleButton>
        </div>
      </div>
    </RikkaDialog>

    <!-- 邮箱修改子弹窗 -->
    <AppChangeEmailModal
      :show="emailChangeDialogFlag"
      @close="emailChangeDialogFlag = false"
    />

    <!-- 手机号修改子弹窗 -->
    <AppChangePhoneModal
      :show="phoneChangeDialogFlag"
      @close="phoneChangeDialogFlag = false"
    />

    <!-- 密码修改子弹窗 -->
    <AppChangePasswordModal
      :show="passwordChangeDialogFlag"
      @close="passwordChangeDialogFlag = false"
    />

    <!-- 账户注销确认弹窗 -->
    <AppDeleteAccountModal
      :show="deleteAccountDialogFlag"
      @close="deleteAccountDialogFlag = false"
    />
  </div>
</template>

<script lang="ts" setup>
// 组件属性定义
const { show } = defineProps({
  show: Boolean, // 控制模态框显示状态
});

// 事件发射器定义
const emit = defineEmits<{
  close: [value: boolean]; // 关闭事件，携带是否成功标志
}>();

// 关闭模态框处理函数
const close = (flag = false) => {
  emit('close', flag);
};

// 认证状态管理
const authStore = useAuthStore();

// 响应式设计 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '40rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 弹窗控制状态
const emailChangeDialogFlag = ref(false); // 邮箱修改弹窗显示状态
const phoneChangeDialogFlag = ref(false); // 手机号修改弹窗显示状态
const passwordChangeDialogFlag = ref(false); // 密码修改弹窗显示状态
const deleteAccountDialogFlag = ref(false); // 账户注销弹窗显示状态
</script>

<!-- 样式部分保持原有结构 -->
<style lang="scss" scoped>
.email,
.phone,
.password,
.delete {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #ffffff09;
  border-radius: 1rem;
  margin-bottom: 0.1rem;

  > .icon {
    padding: 0.5rem;
    border-radius: 1rem;
    margin-right: 1rem;
  }

  &.email > .icon {
    background-color: #6365f154;
  }

  &.phone > .icon {
    background-color: #10b98150;
  }

  &.password > .icon {
    background-color: rgba(255, 166, 0, 0.352);
  }

  &.delete > .icon {
    background-color: #ff000054;
  }

  > .value {
    flex: 1;

    > .title {
      font-size: 1.1rem;
    }

    > .content {
      font-size: 0.9rem;
    }
  }

  > .btn {
    > button {
      background-color: var(--neutral-divider);
      transition: all 0.2s ease-in-out;

      &:hover {
        background-color: var(--neutral-hover);
      }
    }

    .main {
      display: flex;
      align-items: center;

      > span {
        margin-left: 0.5rem;
        color: #fff;
      }
    }
  }
}
</style>

