/**
 * 设备重定向全局中间件
 * 根据设备类型自动重定向用户到对应的移动版/桌面版页面
 * 支持用户手动覆盖默认设备检测结果
 */

/**
 * Nuxt全局路由中间件
 * @param to - 目标路由对象，包含路径、参数等信息
 */
export default defineNuxtRouteMiddleware((to) => {
  // 获取Nuxt应用实例中的设备检测工具
  const { $device } = useNuxtApp();

  // 开发环境跳过重定向逻辑（已注释）
  // if (import.meta.dev) return;

  /**
   * 用户手动设置的视图偏好
   * @type {string|null}
   */
  let forceView = null;

  // 仅在客户端执行localStorage操作
  if (import.meta.client) {
    // 从本地存储获取用户设置的视图模式
    forceView = localStorage.getItem('device-view');
  }

  /**
   * 移动设备访问桌面版页面的处理逻辑
   * 条件：设备是手机或平板 且 当前路径不是移动版路径
   */
  if (
    ($device.isMobile || $device.isTablet) &&
    !to.path.startsWith('/mobile')
  ) {
    // 如果用户强制使用桌面视图，则不重定向
    if (forceView === 'desktop') return;

    /**
     * 构建移动版URL，保持查询参数
     * 将原路径添加'/mobile'前缀，并保留所有查询参数
     */
    const mobileUrl = `/mobile${to.path}`;

    /**
     * 重定向到移动版页面
     * 使用302临时重定向状态码，并保持查询参数
     */
    return navigateTo(
      {
        path: mobileUrl,
        query: to.query,
      },
      { redirectCode: 302 }
    );
  }

  /**
   * 桌面设备访问移动版页面的处理逻辑
   * 条件：设备是桌面设备 且 当前路径是移动版路径
   */
  if ($device.isDesktop && to.path.startsWith('/mobile')) {
    // 如果用户强制使用移动视图，则不重定向
    if (forceView === 'mobile') return;

    /**
     * 转换为桌面版路径
     * 移除'/mobile'前缀，空路径则重定向到首页
     */
    const desktopPath = to.path.replace('/mobile', '') || '/';

    /**
     * 重定向到桌面版页面
     * 使用302临时重定向状态码，并保持查询参数
     */
    return navigateTo(
      {
        path: desktopPath,
        query: to.query,
      },
      {
        redirectCode: 302,
      }
    );
  }
});

