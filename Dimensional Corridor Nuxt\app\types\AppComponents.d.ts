/**
 * 验证码组件暴露的公共方法接口
 * @method refresh - 刷新验证码，生成新的验证码图片
 */
interface CaptchaExpose {
  refresh: () => void;
}

/**
 * 图片上传组件暴露的公共方法接口
 * @method removeFile - 根据索引移除已上传的文件
 * @param {number} idx - 要移除的文件索引位置
 */
interface ImageUploadExpose {
  removeFile: (idx: number) => void;
}

/**
 * 进度遮罩层组件暴露的公共方法接口
 * @method start - 启动加载状态
 * @param {Object} option - 加载状态配置
 * @param {string} [option.title] - 可选，加载状态显示的标题
 * @param {string} [option.description] - 可选，加载状态的详细描述
 * @method stop - 停止加载状态
 */
interface LoadingExpose {
  start: (option: { title?: string; description?: string }) => void;
  stop: () => void;
}

/**
 * 瀑布流组件展示图片项的类型定义
 * 继承自 PhotosList 类型
 */
type PhotosWaterfallGalleryItem = PhotosList & {
  status?: string;
  category?: PhotoCategory;
  viewCount?: number;
};

/**
 * 帖子瀑布流组件展示帖子项的类型定义
 * 继承自 PostsList 类型，并增加 photoInfo 字段
 */
type PostsWaterfallGalleryItem = PostsList & {
  cover?: {
    filename: string;
    url: string;
    width: number;
    height: number;
  };
};

/**
 * 移动端抽屉导航组件暴露的公共方法接口
 */
interface MobileDrawerExpose {
  openDrawer: () => void;
  closeDrawer: () => void;
  toggleDrawer: () => void;
  isOpen: boolean;
}
