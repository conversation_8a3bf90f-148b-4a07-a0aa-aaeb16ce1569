<template>
  <div class="page-container">
    <Head>
      <Title>{{ userDetail?.nickname || '用户' }}主页</Title>
    </Head>

    <!-- 导航栏 -->
    <MoblieNavBar>
      {{ userDetail?.nickname || '用户信息' }}
      <template #right><div></div></template>
    </MoblieNavBar>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <p>加载失败，请稍后重试</p>
      <RippleButton @click="loadUserDetail">重新加载</RippleButton>
    </div>

    <!-- 用户详情内容 -->
    <main v-else-if="userDetail" class="user-detail-content">
      <!-- 用户信息头部区域 -->
      <div class="user-header">
        <!-- 背景图片 -->
        <div
          class="background-image"
          :style="{
            backgroundImage: `url(${userDetail.background + '?width=800'})`,
          }"
        >
          <!-- 用户信息内容 -->
          <div class="user-content">
            <!-- 用户头像 -->
            <div class="avatar-container">
              <img
                :src="userDetail.avatar || '/default-avatar.png'"
                :alt="userDetail.nickname"
                class="user-avatar"
              />
            </div>

            <!-- 用户信息 -->
            <div class="user-info">
              <!-- 用户名 -->
              <div class="username-row">
                <h2 class="username">{{ userDetail.nickname }}</h2>
              </div>

              <!-- 用户ID和性别年龄 -->
              <div class="user-details">
                <div class="user-id-gender-row">
                  <span class="uid-text">UID: {{ userDetail.uid }}</span>
                  <div class="gender-age" v-if="userDetail.gender || userAge">
                    <IconSvg
                      :name="userGender.name"
                      size="1rem"
                      :color="userGender.color"
                    />
                    <span v-if="userAge">{{ userAge }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计数据区域 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-number">{{ userDetail.followingsCount || 0 }}</div>
          <div class="stat-label">关注</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ userDetail.followersCount || 0 }}</div>
          <div class="stat-label">粉丝</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">
            {{ formatNumber(userDetail.totalDownloads || 0) }}
          </div>
          <div class="stat-label">热度</div>
        </div>

        <!-- 关注按钮 -->
        <div
          class="follow-action"
          v-if="authStore.getIsLogin() && !isCurrentUser"
        >
          <RippleButton
            class="follow-btn"
            :class="{ following: userDetail.isFollowing }"
            :ripple-color="userDetail.isFollowing ? '#fff9' : '#4285F49'"
            :disabled="followLoading"
            @click="toggleFollow"
          >
            {{
              followLoading
                ? '处理中...'
                : userDetail.isFollowing
                  ? '已关注'
                  : '关注'
            }}
          </RippleButton>
        </div>
      </div>

      <!-- 个人简介 -->
      <div class="bio-section" v-if="userDetail.bio">
        <p>{{ userDetail.bio }}</p>
      </div>

      <!-- 标签页区域 -->
      <div class="tabs">
        <RikkaTabs
          :tabs="tabs"
          :initial-tab="activeTabIndex"
          @active-tab="handleTabChange"
        >
          <template #user-images>
            <RikkaImagesWaterfallGallery
              :items="userImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="userImagesLoading"
              :has-more="hasMoreUserImages"
              @load-more="handleLoadMoreUserImages"
            >
              <!-- 使用具名插槽自定义项目渲染 -->
              <template #item="{ items, columnWidth }">
                <div v-for="item in items" :key="item.id" class="custom-item">
                  <img
                    :src="item.url"
                    :alt="item.filename"
                    :style="{ width: columnWidth + 'px' }"
                    loading="lazy"
                    @click="
                      $router.push({
                        path: '/mobile/photos/details',
                        query: { id: item.id },
                      })
                    "
                  />
                </div>
              </template>
            </RikkaImagesWaterfallGallery>
          </template>
          <template #user-posts>
            <RikkaPostsWaterfallGallery
              :posts="userPostsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="userPostsLoading"
              :has-more="hasMoreUserPosts"
              @load-more="handleLoadMoreUserPosts"
            >
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </template>
          <template #liked-images v-if="settings.showLikePhotoList">
            <RikkaImagesWaterfallGallery
              :items="likedImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="likedImagesLoading"
              :has-more="hasMoreLikedImages"
              @load-more="handleLoadMoreLikedImages"
            >
              <template #item="{ items, columnWidth }">
                <div v-for="item in items" :key="item.id" class="custom-item">
                  <img
                    :src="item.url"
                    :alt="item.filename"
                    :style="{ width: columnWidth + 'px' }"
                    loading="lazy"
                    @click="
                      $router.push({
                        path: '/mobile/photos/details',
                        query: { id: item.id },
                      })
                    "
                  />
                </div>
              </template>
            </RikkaImagesWaterfallGallery>
          </template>
        </RikkaTabs>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// 获取路由参数
const uid = route.query.uid as string;

// 响应式数据
const loading = ref(true);
const error = ref(false);
const followLoading = ref(false);
const userDetail = ref<OtherUserInfo>();
const settings = ref<UserSettings>({
  showMyPhotoList: false,
  showMyContentList: false,
  showFollowList: false,
  showFansList: false,
  showLikePhotoList: false,
  showFavoritePhotoList: false,
  showLikeContentList: false,
  showFavoriteContentList: false,
});

// 检查是否是当前用户
const isCurrentUser = computed(() => {
  return uid === authStore.authStore?.uid;
});

// 如果是当前用户，重定向到mine页面
if (isCurrentUser.value) {
  router.replace('/mobile/mine');
}

// 计算用户性别图标和颜色
const userGender = computed<{
  color: string;
  name: 'male' | 'female' | 'hidden';
}>(() => {
  switch (userDetail.value?.gender) {
    case 'male':
      return {
        color: '#4285F4',
        name: 'male',
      };
    case 'female':
      return {
        color: '#DB4437',
        name: 'female',
      };
    default:
      return {
        color: '#fff',
        name: 'hidden',
      };
  }
});

// 计算用户年龄
const userAge = computed(() => {
  return calculateAge(userDetail.value?.birthday);
});

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

// 获取用户详情
const loadUserDetail = async () => {
  if (!uid) {
    error.value = true;
    loading.value = false;
    useMessage({
      name: '参数错误',
      description: '缺少用户ID',
      type: 'error',
    });
    return;
  }

  loading.value = true;
  error.value = false;

  try {
    const data = await useApi().getOtherUserInfo(uid);
    userDetail.value = data;
    await getSetting(data.uid);

    // 清空所有数据数组，避免重复数据
    userImagesArray.value = [];
    userPostsArray.value = [];
    likedImagesArray.value = [];

    // 重置分页信息
    userImagesPaginated.value = {
      page: 0,
      pageSize: 20,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'desc',
    };
    userPostsPaginated.value = {
      page: 0,
      pageSize: 20,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'desc',
    };
    likedImagesPaginated.value = {
      page: 0,
      pageSize: 20,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'desc',
    };

    // 根据当前活跃的tab加载对应数据
    await loadTabData(activeTabIndex.value);
  } catch (err) {
    console.error('用户详情', '获取用户详情失败', err);
    error.value = true;
    useMessage({
      name: '加载失败',
      description: '无法获取用户详情',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 获取用户设置
const getSetting = async (uid: string) => {
  try {
    const data = await useApi().getUserSetting(uid);
    settings.value = data;
  } catch (err) {
    // 设置默认值
    settings.value = {
      showMyPhotoList: false,
      showMyContentList: false,
      showFollowList: false,
      showFansList: false,
      showLikePhotoList: false,
      showFavoritePhotoList: false,
      showLikeContentList: false,
      showFavoriteContentList: false,
    };
  }
};

// 关注/取消关注
const toggleFollow = async () => {
  if (!userDetail.value || !authStore.isAuthenticated) return;

  followLoading.value = true;
  try {
    await useApi().followUser(userDetail.value.uid);
    userDetail.value.isFollowing = !userDetail.value.isFollowing;

    // 更新关注者数量
    if (userDetail.value.isFollowing) {
      userDetail.value.followersCount++;
    } else {
      userDetail.value.followersCount = Math.max(
        0,
        userDetail.value.followersCount - 1
      );
    }

    useMessage({
      name: userDetail.value.isFollowing ? '关注成功' : '已取消关注',
      description: '',
      type: 'success',
    });
  } catch (err) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    followLoading.value = false;
  }
};

// 标签页配置
const tabs = computed(() => {
  if (!userDetail.value) return [];

  const tabList = [];

  // 用户图片 - 始终显示
  tabList.push({
    title: '图片 ' + (userDetail.value.photosCount || 0),
    slotName: 'user-images',
  });

  // 用户帖子 - 始终显示
  tabList.push({
    title: '帖子 ' + (userDetail.value.contentsCount || 0),
    slotName: 'user-posts',
  });

  // 点赞图片 - 根据隐私设置显示
  if (settings.value.showLikePhotoList) {
    tabList.push({
      title: '点赞图片 ' + (userDetail.value.likePhotosCount || 0),
      slotName: 'liked-images',
    });
  }

  return tabList;
});

// 当前活跃的tab索引
const activeTabIndex = ref(0);

// 用户图片列表
const userImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const userImagesLoading = ref(false);
const userImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

// 用户帖子列表
const userPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const userPostsLoading = ref(false);
const userPostsPaginated = ref<Paginated<PostsSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

// 点赞图片列表
const likedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const likedImagesLoading = ref(false);
const likedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

// 计算是否有更多数据
const hasMoreUserImages = computed(() => {
  return userImagesPaginated.value.page < userImagesPaginated.value.totalPage;
});

const hasMoreUserPosts = computed(() => {
  return userPostsPaginated.value.page < userPostsPaginated.value.totalPage;
});

const hasMoreLikedImages = computed(() => {
  return likedImagesPaginated.value.page < likedImagesPaginated.value.totalPage;
});

// 标签页切换处理
const handleTabChange = async (index: number) => {
  activeTabIndex.value = index;
  await loadTabData(index);
};

// 根据标签页索引加载对应数据
const loadTabData = async (tabIndex: number) => {
  const currentTab = tabs.value[tabIndex];
  if (!currentTab) return;

  switch (currentTab.slotName) {
    case 'user-images':
      if (userImagesArray.value.length === 0) {
        userImagesLoading.value = true;
        await getUserImagesList({
          page: 1,
          pageSize: 20,
        });
        userImagesLoading.value = false;
      }
      break;
    case 'user-posts':
      if (userPostsArray.value.length === 0) {
        userPostsLoading.value = true;
        await getUserPostsList({
          page: 1,
          pageSize: 20,
        });
        userPostsLoading.value = false;
      }
      break;
    case 'liked-images':
      if (likedImagesArray.value.length === 0) {
        likedImagesLoading.value = true;
        await getLikedImagesList({
          page: 1,
          pageSize: 20,
        });
        likedImagesLoading.value = false;
      }
      break;
  }
};

// 获取用户图片列表
const getUserImagesList = async (query?: PhotosListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPhotosList(
      userDetail.value.uid,
      query
    );
    userImagesArray.value.push(...list);
    userImagesPaginated.value = res;
  } catch (err) {
    userImagesLoading.value = false;
    userImagesPaginated.value.totalPage = 0;
    return;
  }
};

// 加载更多用户图片
const handleLoadMoreUserImages = async () => {
  if (userImagesLoading.value || !hasMoreUserImages.value) return;

  // 如果用户图片数量为0，跳过API请求
  if (userDetail.value?.photosCount === 0) {
    userImagesPaginated.value.totalPage = 0;
    return;
  }

  userImagesLoading.value = true;
  await getUserImagesList({
    page: userImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  userImagesLoading.value = false;
};

// 获取用户帖子列表
const getUserPostsList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPostList(
      userDetail.value.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    userPostsArray.value.push(...newData);
    userPostsPaginated.value = res;
  } catch (err) {
    userPostsLoading.value = false;
    userPostsPaginated.value.totalPage = 0;
    return;
  }
};

// 加载更多用户帖子
const handleLoadMoreUserPosts = async () => {
  if (userPostsLoading.value || !hasMoreUserPosts.value) return;

  // 如果用户帖子数量为0，跳过API请求
  if (userDetail.value?.contentsCount === 0) {
    userPostsPaginated.value.totalPage = 0;
    return;
  }

  userPostsLoading.value = true;
  await getUserPostsList({
    page: userPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  userPostsLoading.value = false;
};

// 获取点赞图片列表
const getLikedImagesList = async (query?: PhotosListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikePhotoList(
      userDetail.value.uid,
      query
    );
    likedImagesArray.value.push(...list);
    likedImagesPaginated.value = res;
  } catch (err) {
    likedImagesLoading.value = false;
    likedImagesPaginated.value.totalPage = 0;
    return;
  }
};

// 加载更多点赞图片
const handleLoadMoreLikedImages = async () => {
  if (likedImagesLoading.value || !hasMoreLikedImages.value) return;

  // 如果用户点赞图片数量为0，跳过API请求
  if (userDetail.value?.likePhotosCount === 0) {
    likedImagesPaginated.value.totalPage = 0;
    return;
  }

  likedImagesLoading.value = true;
  await getLikedImagesList({
    page: likedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  likedImagesLoading.value = false;
};

// 监听路由变化
watch(
  () => route.query.uid,
  (newUid) => {
    if (newUid && newUid !== uid) {
      window.location.reload(); // 重新加载页面以获取新的用户详情
    }
  }
);

// 页面挂载时加载数据
onMounted(() => {
  loadUserDetail();
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-base);
  color: var(--text-primary);
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 2rem;
  text-align: center;

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: var(--text-secondary);
    margin: 1rem 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.user-detail-content {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-header {
  position: relative;
  margin-bottom: 1rem;

  .background-image {
    position: relative;
    height: 12rem;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: var(--background-secondary);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );
    }

    .user-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: flex-end;
      height: 100%;
      padding: 1rem;

      .avatar-container {
        margin-right: 1rem;

        .user-avatar {
          width: 4rem;
          height: 4rem;
          border-radius: 50%;
          border: 3px solid rgba(255, 255, 255, 0.9);
          object-fit: cover;
          background: var(--background-secondary);
        }
      }

      .user-info {
        flex: 1;
        color: white;

        .username-row {
          .username {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 0.25rem 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }
        }

        .user-details {
          .user-id-gender-row {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.875rem;
            opacity: 0.9;

            .uid-text {
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            }

            .gender-age {
              display: flex;
              align-items: center;
              gap: 0.25rem;
            }
          }
        }
      }
    }
  }
}

.stats-section {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: var(--background-elevated);
  margin: 0 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .stat-item {
    flex: 1;
    text-align: center;

    .stat-number {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--text-secondary);
    }
  }

  .follow-action {
    margin-left: 1rem;

    .follow-btn {
      padding: 0.6rem 1.8rem;
      font-size: 0.875rem;
      font-weight: 600;
      border-radius: 2rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;
      min-width: 5rem;
      text-align: center;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      &:hover::before {
        left: 100%;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }

      &.following {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 4px 12px rgba(245, 87, 108, 0.3);

        &:hover {
          box-shadow: 0 6px 20px rgba(245, 87, 108, 0.4);
        }

        &:active {
          box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

        &::before {
          display: none;
        }

        &:hover {
          transform: none;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

.bio-section {
  padding: 1rem;
  margin: 0 1rem;
  background: var(--background-elevated);
  border-radius: 0.75rem;
  margin-top: 1rem;

  p {
    color: var(--text-primary);
    line-height: 1.5;
    margin: 0;
  }
}

.tabs {
  flex: 1;
  min-height: 20rem;
  margin-top: 1rem;

  .tabs-content {
    height: 100%;
  }

  .custom-item {
    img {
      width: 100%;
      border-radius: 0.5rem;
    }
  }
}
</style>

