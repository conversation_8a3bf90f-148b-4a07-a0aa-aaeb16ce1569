<template>
  <div class="mobile-drawer">
    <!-- 导航栏 -->
    <div class="nav" v-if="showNav">
      <MoblieNavBar>
        <template #left>
          <div class="menu-button" @click="toggleDrawer">
            <IconSvg name="menu" size="2rem" color="var(--text-secondary)" />
          </div>
        </template>
      </MoblieNavBar>
    </div>

    <!-- 抽屉遮罩层 -->
    <Transition name="overlay">
      <div v-if="isOpen" class="drawer-overlay" @click="closeDrawer"></div>
    </Transition>

    <!-- 抽屉内容 -->
    <Transition name="drawer">
      <div v-if="isOpen" class="drawer-content">
        <div class="drawer-header">
          <h1>菜单</h1>
          <button class="close-btn" @click="closeDrawer">
            <IconSvg
              name="leftArrows"
              size="1.5rem"
              color="var(--text-secondary)"
            />
          </button>
        </div>

        <div class="drawer-body">
          <!-- 用户信息区域 -->
          <div class="user-section" v-if="authStore.getIsLogin()">
            <div class="user-avatar">
              <img
                :src="authStore.getAuth()?.avatar || '/default-avatar.png'"
                :alt="authStore.getAuth()?.nickname"
              />
            </div>
            <div class="user-info">
              <div class="username">{{ authStore.getAuth()?.nickname }}</div>
              <div class="user-id">UID: {{ authStore.getAuth()?.uid }}</div>
            </div>
          </div>

          <!-- 未登录状态 -->
          <div class="login-section" v-else>
            <div class="login-prompt">
              <IconSvg name="mine" size="4rem" color="var(--text-secondary)" />
              <p>登录后享受更多功能</p>
              <RippleButton class="login-btn" @click="handleLogin">
                立即登录
              </RippleButton>
            </div>
          </div>

          <!-- 主要导航菜单 -->
          <div class="nav-section">
            <div class="section-title">导航</div>
            <ul class="nav-list">
              <li
                v-for="(item, index) in navItems"
                :key="index"
                :class="{ active: isActiveRoute(item.path) }"
                @click="handleNavClick(item.path)"
              >
                <IconSvg
                  :name="item.icon"
                  size="2rem"
                  :color="
                    isActiveRoute(item.path)
                      ? 'var(--text-primary)'
                      : 'var(--text-secondary)'
                  "
                />
                <span>{{ item.name }}</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
            </ul>
          </div>

          <!-- 设置菜单 -->
          <div class="settings-section" v-if="authStore.getIsLogin()">
            <div class="section-title">设置</div>
            <ul class="settings-list">
              <li @click="handleEditProfile">
                <IconSvg
                  name="edit"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>编辑资料</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
              <li @click="privacyDialogFlag = true">
                <IconSvg
                  name="privacy"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>隐私设置</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
              <li @click="accountSafeDialogFlag = true">
                <IconSvg
                  name="safe"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>账号安全</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
              <li @click="skinDialogFlag = true">
                <IconSvg
                  name="skin"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>皮肤选择</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
            </ul>
          </div>

          <!-- 帮助与反馈 -->
          <div class="help-section">
            <div class="section-title">帮助</div>
            <ul class="help-list">
              <li @click="handleFeedback">
                <IconSvg
                  name="feedback"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>反馈建议</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
              <li @click="handleQuestion">
                <IconSvg
                  name="question"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>常见问题</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
              <li @click="handleImageTools">
                <IconSvg
                  name="imgTools"
                  size="2rem"
                  color="var(--text-secondary)"
                />
                <span>图片工具</span>
                <IconSvg
                  name="rightArrows"
                  size="1.2rem"
                  color="var(--text-secondary)"
                />
              </li>
            </ul>
          </div>

          <!-- 退出登录 -->
          <div class="logout-section" v-if="authStore.getIsLogin()">
            <div class="logout-item" @click="handleLogout">
              <IconSvg name="quit" size="2rem" color="red" />
              <span>退出登录</span>
              <IconSvg
                name="rightArrows"
                size="1.2rem"
                color="var(--text-secondary)"
              />
            </div>
          </div>

          <!-- 统一的响应式弹窗组件 -->
          <AppSettingModal
            :show="privacyDialogFlag"
            @close="privacyDialogFlag = false"
          />
          <AppSkinModal
            :show="skinDialogFlag"
            @close="skinDialogFlag = false"
          />
          <AppAccountSafeModal
            :show="accountSafeDialogFlag"
            @close="accountSafeDialogFlag = false"
          />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import type { NavItem } from '~/components/AppSideMenu.vue';

defineProps({
  showNav: {
    type: Boolean,
    default: true,
  },
});

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const isOpen = ref(false);

// 导航菜单数据
const navItems: NavItem[] = [
  {
    name: '图片',
    path: '/mobile',
    icon: 'image',
  },
  {
    name: '帖子',
    path: '/mobile/posts',
    icon: 'posts',
  },
  {
    name: '消息',
    path: '/mobile/message',
    icon: 'mine',
  },
  {
    name: '我的',
    path: '/mobile/mine',
    icon: 'mine',
  },
];

// 弹窗控制
const privacyDialogFlag = ref(false);
const skinDialogFlag = ref(false);
const accountSafeDialogFlag = ref(false);

const toggleDrawer = () => {
  isOpen.value = !isOpen.value;
};

const openDrawer = () => {
  isOpen.value = true;
};

const closeDrawer = () => {
  isOpen.value = false;
};

// 判断当前路由是否激活
const isActiveRoute = (path: string) => {
  const currentPath = route.path.split('?')[0];
  return (
    currentPath === path || (path === '/mobile' && currentPath === '/mobile')
  );
};

// 处理导航点击
const handleNavClick = (path: string) => {
  router.push(path);
  closeDrawer();
};

// 处理登录
const handleLogin = () => {
  router.push('/mobile/auth/login');
  closeDrawer();
};

// 处理编辑资料
const handleEditProfile = () => {
  router.push('/mobile/mine/edit');
  closeDrawer();
};

// 处理反馈建议
const handleFeedback = () => {
  // TODO: 实现反馈功能
  console.log('反馈建议');
  closeDrawer();
};

// 处理常见问题
const handleQuestion = () => {
  // TODO: 实现常见问题功能
  console.log('常见问题');
  closeDrawer();
};

// 处理图片工具
const handleImageTools = () => {
  window.open('https://imageutils.sixflower.top/', '_blank');
  closeDrawer();
};

// 处理退出登录
const handleLogout = async () => {
  try {
    await useApi().logout();
    closeDrawer();
    router.push('/mobile');
  } catch (error) {
    console.error('退出登录失败:', error);
  }
};

defineExpose({
  openDrawer,
  closeDrawer,
  toggleDrawer,
  isOpen: readonly(isOpen),
});

watch(isOpen, (newValue) => {
  document.body.style.overflow = newValue ? 'hidden' : '';
});

onUnmounted(() => {
  document.body.style.overflow = '';
});
</script>
<style lang="scss" scoped>
.mobile-drawer {
  .nav {
    background-color: var(--background-base);
    border-bottom: 0.0625rem solid var(--border-color);
  }

  .menu-button {
    background-color: var(--background-elevated);
    padding: 0.7rem;
    border-radius: 1rem;
    cursor: pointer;

    &:hover {
      background-color: var(--background-hover);
    }
  }
}

.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.drawer-content {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 70vw;
  max-width: 80vw;
  background-color: var(--background-elevated);
  z-index: 1002;
  display: flex;
  flex-direction: column;

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    border-bottom: 0.0625rem solid var(--border-color);
    background-color: var(--background-base);

    h1 {
      font-size: 2rem;
      margin: 0;
      color: var(--text-primary);
    }

    .close-btn {
      background-color: var(--background-elevated);
      padding: 0.7rem;
      border-radius: 1rem;
      cursor: pointer;

      &:hover {
        background-color: var(--background-hover);
      }
    }
  }

  .drawer-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    // 用户信息区域
    .user-section {
      display: flex;
      align-items: center;
      padding: 2rem 1.5rem;
      background: linear-gradient(
        135deg,
        var(--background-floating) 0%,
        var(--background-elevated) 100%
      );
      border-bottom: 0.0625rem solid var(--border-color);

      .user-avatar {
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 1rem;
        border: 0.125rem solid var(--border-color);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }

      .user-info {
        flex: 1;

        .username {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 0.25rem;
        }

        .user-id {
          font-size: 0.875rem;
          color: var(--text-secondary);
        }
      }
    }

    // 未登录状态
    .login-section {
      padding: 2rem 1.5rem;
      text-align: center;
      border-bottom: 0.0625rem solid var(--border-color);

      .login-prompt {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        p {
          margin: 1rem 0;
          color: var(--text-secondary);
          font-size: 1rem;
        }

        .login-btn {
          background-color: var(--interactive-primary);
          color: white;
          padding: 0.75rem 2rem;
          border-radius: 2rem;
          font-weight: 500;

          &:hover {
            background-color: var(--interactive-primary-hover);
          }
        }
      }
    }

    // 分组标题
    .section-title {
      padding: 1rem 1.5rem 0.5rem;
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    // 通用列表样式
    .nav-list,
    .settings-list,
    .help-list {
      padding: 0 1rem;
      margin-bottom: 1rem;

      li {
        display: flex;
        align-items: center;
        padding: 1rem 0.75rem;
        margin-bottom: 0.25rem;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--background-floating);
        }

        &.active {
          background-color: var(--interactive-primary);
          color: white;

          span {
            color: white;
          }
        }

        span {
          flex: 1;
          margin-left: 1rem;
          font-size: 1rem;
          color: var(--text-primary);
        }

        // 右箭头图标
        > svg:last-child {
          margin-left: auto;
        }
      }
    }

    // 退出登录区域
    .logout-section {
      padding: 0 1rem 2rem;

      .logout-item {
        display: flex;
        align-items: center;
        padding: 1rem 0.75rem;
        margin-bottom: 0.25rem;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(var(--text-danger-rgb), 0.1);
        }

        span {
          flex: 1;
          margin-left: 1rem;
          font-size: 1rem;
          color: var(--text-danger);
          font-weight: 500;
        }

        // 右箭头图标
        > svg:last-child {
          margin-left: auto;
        }
      }
    }
  }
}

.overlay-enter-active,
.overlay-leave-active {
  transition: opacity 0.3s ease;
}

.overlay-enter-from,
.overlay-leave-to {
  opacity: 0;
}

.drawer-enter-active,
.drawer-leave-active {
  transition: transform 0.3s ease;
}

.drawer-enter-from,
.drawer-leave-to {
  transform: translateX(-100%);
}
</style>

