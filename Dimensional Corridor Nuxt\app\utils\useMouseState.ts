/**
 * 鼠标状态管理组合式函数
 * 提供鼠标进入/离开状态的响应式管理和操作方法
 *
 * 功能说明：
 * 1. 维护一个响应式的鼠标进入状态(isMouseEntered)
 * 2. 提供修改状态的方法(setMouseEntered)
 * 3. 返回只读的状态引用，防止外部直接修改
 *
 * 返回值说明：
 * {
 *   isMouseEntered: Readonly<Ref<boolean>>, // 只读的鼠标进入状态
 *   setMouseEntered: (value: boolean) => void // 设置鼠标状态的方法
 * }
 *
 * 使用场景：
 * 1. 按钮悬停效果
 * 2. 下拉菜单显示/隐藏控制
 * 3. 工具提示显示控制
 *
 * 使用示例：
 * const { isMouseEntered, setMouseEntered } = useMouseState()
 *
 * // 在模板中使用
 * <div @mouseenter="setMouseEntered(true)"
 *      @mouseleave="setMouseEntered(false)">
 *   {{ isMouseEntered ? '鼠标在元素内' : '鼠标在元素外' }}
 * </div>
 */
import { ref, readonly } from 'vue';

export function useMouseState() {
  // 创建响应式变量，存储鼠标是否进入的状态
  const isMouseEntered = ref(false);

  // 设置鼠标状态的方法
  function setMouseEntered(value: boolean) {
    isMouseEntered.value = value;
  }

  // 返回状态和方法
  return {
    isMouseEntered: readonly(isMouseEntered), // 返回只读引用
    setMouseEntered, // 暴露设置方法
  };
}

