<template>
  <div class="mobile-layout">
    <div class="main"><slot /></div>
    <div class="tabbar">
      <NuxtLink
        to="/mobile"
        :class="{ active: routerStore.get()?.split('?')[0] === '/mobile' }"
        >图片</NuxtLink
      >
      <NuxtLink
        to="/mobile/posts"
        :class="{
          active: routerStore.get()?.split('?')[0] === '/mobile/posts',
        }"
        >帖子</NuxtLink
      >
      <div @click="publishHandle">
        <IconSvg name="publish" size="3rem" color="#fff" />
      </div>
      <NuxtLink
        to="/mobile/message"
        :class="{
          active: routerStore.get()?.split('?')[0] === '/mobile/message',
        }"
        >消息</NuxtLink
      >
      <NuxtLink
        to="/mobile/mine"
        :class="{ active: routerStore.get()?.split('?')[0] === '/mobile/mine' }"
        >我</NuxtLink
      >
    </div>

    <!-- 发布选择遮罩层 -->
    <Transition name="publish-overlay">
      <div
        v-if="showPublishModal"
        class="publish-overlay"
        @click="closePublishModal"
      ></div>
    </Transition>

    <!-- 发布选择面板 -->
    <Transition name="publish-panel">
      <div v-if="showPublishModal" class="publish-panel">
        <button class="publish-option" @click="goToPublishPosts">
          <IconSvg name="edit" size="3rem" color="var(--text-primary)" />
          <span>发布帖子</span>
        </button>
        <button class="publish-option" @click="goToPublishPhotos">
          <IconSvg name="image" size="3rem" color="var(--text-primary)" />
          <span>发布图片</span>
        </button>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
const routerStore = useRouterStore();
const router = useRouter();

// 发布模态框状态
const showPublishModal = ref(false);

const publishHandle = () => {
  const authStore = useAuthStore();
  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '发布功能需要登录后使用',
      type: 'info',
    });
    return;
  }
  // 显示发布选择面板
  showPublishModal.value = true;
};

// 关闭发布模态框
const closePublishModal = () => {
  showPublishModal.value = false;
};

// 跳转到发布帖子页面
const goToPublishPosts = () => {
  closePublishModal();
  router.push('/mobile/publish/posts');
};

// 跳转到发布图片页面
const goToPublishPhotos = () => {
  closePublishModal();
  router.push('/mobile/publish/photos');
};

// 监听路由变化，关闭模态框
watch(
  () => router.currentRoute.value.path,
  () => {
    showPublishModal.value = false;
  }
);
</script>

<style lang="scss" scoped>
.mobile-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--background-base);
  color: var(--text-primary);

  > .main {
    width: 100%;
    overflow: auto;
    flex: 1;
    background: var(--background-elevated);
  }

  > .tabbar {
    width: 100%;
    display: flex;
    font-size: 1.7rem;
    border-top: 0.1px solid #000;

    > * {
      flex: 1;
      padding: 1rem 0;
      line-height: 3rem;
      text-align: center;
      opacity: 0.5;
      transition: opacity 0.3s ease;

      &.active {
        opacity: 1;
      }
    }

    > div {
      opacity: 1;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
    }
  }
}

/* 发布遮罩层样式 */
.publish-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
}

/* 发布面板样式 */
.publish-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  display: flex;
  gap: 2rem;

  .publish-option {
    background: var(--background-elevated);
    border: none;
    border-radius: 1.5rem;
    padding: 2.5rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.2);
    min-width: 8rem;

    span {
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--text-primary);
      white-space: nowrap;
    }
  }
}

/* 遮罩层动画 */
.publish-overlay-enter-active,
.publish-overlay-leave-active {
  transition: opacity 0.3s ease;
}

.publish-overlay-enter-from,
.publish-overlay-leave-to {
  opacity: 0;
}

/* 面板滑动动画 */
.publish-panel-enter-active,
.publish-panel-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.publish-panel-enter-from,
.publish-panel-leave-to {
  transform: translate(-50%, calc(-50% + 100vh));
  opacity: 0;
}
</style>

