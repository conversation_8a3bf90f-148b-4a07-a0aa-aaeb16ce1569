<template>
  <div class="rikka-cropper-container">
    <div class="card">
      <div class="card-body">
        <div v-if="imageUrl" class="image-container">
          <div class="cropper-wrapper">
            <img
              ref="imageRef"
              :src="imageUrl"
              alt="裁剪图像"
              @load="onImageLoaded"
            />
          </div>

          <!-- 右侧操作面板 -->
          <div class="control-panel">
            <!-- 比例选择器 -->
            <div v-if="enableAspectRatio" class="control-group">
              <div class="ratio-selector">
                <select v-model="currentRatio" @change="onRatioChange">
                  <option
                    v-for="ratio in aspectRatios"
                    :key="ratio.value"
                    :value="ratio.value"
                  >
                    {{ ratio.label }}
                  </option>
                </select>
              </div>
            </div>

            <!-- 旋转和翻转操作 -->
            <div class="control-group operation-buttons">
              <RippleButton class="operation-btn" @click="rotateLeft">
                左转 90°
              </RippleButton>
              <RippleButton class="operation-btn" @click="rotateRight">
                右转 90°
              </RippleButton>
              <RippleButton class="operation-btn" @click="flipHorizontal">
                水平翻转
              </RippleButton>
              <RippleButton class="operation-btn" @click="flipVertical">
                垂直翻转
              </RippleButton>
            </div>

            <!-- 按钮组 -->
            <div class="control-group">
              <RippleButton
                class="reset-btn"
                rippleColor="#fff"
                @click="resetCropper"
              >
                重置操作
              </RippleButton>
              <RippleButton
                v-if="!forceCrop"
                class="cancel-btn"
                rippleColor="#fff"
                @click="cropCancel"
              >
                不裁剪
              </RippleButton>
              <RippleButton
                class="crop-btn"
                rippleColor="#fff"
                @click="cropImage"
              >
                应用裁剪
              </RippleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 导入裁剪库
import Cropper from 'cropperjs';

// 组件属性定义
const props = defineProps({
  /** 图片文件 */
  imageFile: {
    type: [File, null],
    default: null,
  },
  /** 是否启用比例选择 */
  enableAspectRatio: {
    type: Boolean,
    default: true,
  },
  /** 默认裁剪区域比例 */
  defaultAspectRatio: {
    type: [String, Number],
    default: 'free',
  },
  /** 强制裁剪 */
  forceCrop: {
    type: Boolean,
    default: false,
  },
  /** 默认裁剪区域大小 */
  autoCropArea: {
    type: Number,
    default: 0.5,
  },
});

// 组件事件定义
const emit = defineEmits(['crop-complete', 'crop-cancel']);

// DOM 元素引用
const imageRef = ref<HTMLImageElement | null>(null);
// 裁剪器实例
const cropper = ref<Cropper | null>(null);
// 图片临时 URL
const imageUrl = ref<string>('');
// 当前选中比例
const currentRatio = ref<string | number>(props.defaultAspectRatio || 'free');

// 可用比例配置
const aspectRatios = [
  { label: '自由比例', value: 'free' },
  { label: '正方形 (1:1)', value: 1 },
  { label: '标准照片 (4:3)', value: 4 / 3 },
  { label: '宽屏 (16:9)', value: 16 / 9 },
  { label: '海报 (3:4)', value: 3 / 4 },
  { label: '电影宽屏 (21:9)', value: 21 / 9 },
];

// 监听图片文件变化
watch(
  () => props.imageFile,
  (newFile, oldFile) => {
    if (oldFile && oldFile !== newFile) {
      cleanupResources();
    }

    if (newFile) {
      if (!newFile.type.startsWith('image/')) {
        console.error('仅限图像文件');
        return;
      }
      imageUrl.value = URL.createObjectURL(newFile);
    } else {
      imageUrl.value = '';
    }
  },
  { immediate: true }
);

// 图片加载完成回调
const onImageLoaded = () => {
  initCropper();
};

// 初始化裁剪器
const initCropper = () => {
  if (!imageRef.value || !imageUrl.value) return;

  // 清理旧实例
  if (cropper.value) {
    cropper.value.destroy();
    cropper.value = null;
  }

  // 创建新实例
  cropper.value = new Cropper(imageRef.value, {
    aspectRatio:
      currentRatio.value === 'free' ? NaN : Number(currentRatio.value),
    viewMode: 1,
    dragMode: 'move',
    autoCropArea: props.autoCropArea,
    restore: false,
    guides: true,
    center: true,
    highlight: false,
    cropBoxMovable: true,
    cropBoxResizable: true,
    toggleDragModeOnDblclick: true,
    minContainerWidth: 800,
    minContainerHeight: 500,
  });
};

// 处理比例切换
const onRatioChange = () => {
  if (cropper.value) {
    cropper.value.setAspectRatio(
      currentRatio.value === 'free' ? NaN : Number(currentRatio.value)
    );
  }
};

// 旋转操作（逆时针90度）
const rotateLeft = () => {
  cropper.value?.rotate(-90);
};

// 旋转操作（顺时针90度）
const rotateRight = () => {
  cropper.value?.rotate(90);
};

// 水平翻转
const flipHorizontal = () => {
  if (!cropper.value) return;
  const data = cropper.value.getData();
  cropper.value.scaleX(data.scaleX * -1);
};

// 垂直翻转
const flipVertical = () => {
  if (!cropper.value) return;
  const data = cropper.value.getData();
  cropper.value.scaleY(data.scaleY * -1);
};

// 重置裁剪器状态
const resetCropper = () => {
  cropper.value?.reset();
  currentRatio.value = props.defaultAspectRatio || 'free';
};

// 取消裁剪
const cropCancel = () => {
  emit('crop-cancel');
};

// 执行裁剪操作
const cropImage = () => {
  if (!cropper.value) return;

  try {
    const canvas = cropper.value.getCroppedCanvas({
      maxWidth: 4096,
      maxHeight: 4096,
      fillColor: '#fff',
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
    });

    canvas.toBlob(
      (blob) => {
        if (!blob) {
          console.error('裁剪失败');
          return;
        }

        const croppedFile = new File(
          [blob],
          `cropped-${props.imageFile?.name || 'image'}`,
          {
            type: blob.type || 'image/jpeg',
            lastModified: Date.now(),
          }
        );

        emit('crop-complete', croppedFile);
      },
      'image/jpeg',
      1
    );
  } catch (error) {
    console.error('裁剪异常:', error);
  }
};

// 清理资源
const cleanupResources = () => {
  if (cropper.value) {
    cropper.value.destroy();
    cropper.value = null;
  }

  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value);
    imageUrl.value = '';
  }
};

// 组件卸载前清理
onBeforeUnmount(() => {
  cleanupResources();
});
</script>

<style scoped lang="scss">
.rikka-cropper-container {
  background: var(--cropper-card-bg);
  border-radius: var(--cropper-radius);
  border: 1px solid var(--cropper-secondary);
  box-shadow: var(--cropper-shadow);
  transition: var(--cropper-transition);

  .card {
    background: var(--cropper-card-bg);
    border-radius: var(--cropper-radius);
    overflow: hidden;

    .card-body {
      padding: 1rem;

      .image-container {
        display: flex;
        gap: 1rem;

        .cropper-wrapper {
          flex: 1;
          position: relative;
          height: 30rem;
          overflow: hidden;
          border: 1px solid var(--cropper-border);
          border-radius: calc(var(--cropper-radius) / 2);
          background: var(--cropper-bg);
          box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
          }
        }

        .control-panel {
          width: 280px;
          background: var(--cropper-panel-bg);
          border-radius: calc(var(--cropper-radius) / 2);
          padding: 1rem;
          border: 1px solid var(--cropper-border);
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 1.2rem;
          box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);

          .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;

            &.operation-buttons {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 0.7rem;
            }

            .ratio-selector {
              select {
                width: 100%;
                padding: 0.7rem;
                border-radius: 6px;
                border: 1px solid var(--cropper-border);
                background: var(--cropper-panel-bg);
                color: var(--cropper-text);
                font-size: 0.95rem;
                cursor: pointer;
                transition: var(--cropper-transition);

                &:focus {
                  outline: none;
                  border-color: var(--cropper-primary);
                  box-shadow: 0 0 0 2px var(--cropper-primary-light);
                }
              }
            }

            .operation-btn {
              padding: 1.5rem 1rem;
              background: rgba(var(--cropper-primary-rgb), 0.2);
              border: 1px solid var(--cropper-border);
              border-radius: 6px;
              color: var(--cropper-text);
              font-size: 0.95rem;
              cursor: pointer;
              transition: var(--cropper-transition);
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;

              &:hover {
                background: rgba(var(--cropper-primary-rgb), 0.4);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }

              &:active {
                transform: translateY(0);
              }
            }

            .reset-btn,
            .cancel-btn,
            .crop-btn {
              padding: 1.5rem 1rem;
              font-size: 1rem;
              font-weight: 500;
              border-radius: 6px;
              cursor: pointer;
              transition: var(--cropper-transition);
              text-align: center;
              border: 1px solid transparent;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .reset-btn {
              background: rgba(var(--cropper-secondary-rgb), 0.5);
              color: var(--cropper-text);

              &:hover {
                background: rgba(var(--cropper-secondary-rgb), 0.7);
                box-shadow: 0 0 10px var(--cropper-secondary);
              }
            }

            .cancel-btn {
              background: rgba(var(--cropper-danger-rgb), 0.5);
              color: var(--cropper-text);

              &:hover {
                background: rgba(var(--cropper-danger-rgb), 0.7);
                box-shadow: 0 0 10px var(--cropper-danger);
              }
            }

            .crop-btn {
              background: rgba(var(--cropper-success-rgb), 0.5);
              color: var(--cropper-text);

              &:hover {
                background: rgba(var(--cropper-success-rgb), 0.7);
                box-shadow: 0 0 10px var(--cropper-success);
              }
            }
          }
        }
      }
    }
  }
}
</style>

