/**
 * 加载状态配置选项
 * @property {string} [title] - 可选，加载状态显示的标题
 * @property {string} [description] - 可选，加载状态的详细描述
 */
interface LoadingOptions {
  title?: string;
  description?: string;
}

/**
 * 加载状态管理接口
 * @property {boolean} visible - 是否显示加载状态
 * @property {string} title - 当前加载状态显示的标题
 * @property {string} description - 当前加载状态的详细描述
 * @method start - 启动加载状态，可传入配置选项
 * @method stop - 停止加载状态
 */
interface loadingState {
  visible: boolean;
  title: string;
  description: string;
  start: (options?: LoadingOptions) => void;
  stop: () => void;
}
