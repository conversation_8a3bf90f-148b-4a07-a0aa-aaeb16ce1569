import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { ContentsStatusEnum } from 'src/shared/enums/Content.enum';

export type ContentsDocument = HydratedDocument<Contents>;

@Schema({
  timestamps: { createdAt: 'createTime', updatedAt: 'updateTime' },
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: ContentsDocument) => {
      if (ret.isDeleted) {
        return null;
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, ...rest } = ret;
      return {
        id: _id.toString(),
        ...rest,
      };
    },
  },
})
export class Contents {
  /** 帖子唯一标识符 */
  @Prop({})
  id: string;

  /** 帖子上传者ID */
  @Prop({
    type: String,
    ref: 'Users',
    required: true,
    index: true, // 建立索引
  })
  user: string;

  /** 标题 */
  @Prop({
    type: String,
    required: true,
    maxlength: 50,
  })
  title: string;

  /** 内容 */
  @Prop({
    type: String,
    required: true,
  })
  content: string;

  /** 分类 */
  @Prop({
    type: String,
    required: true,
  })
  category: string;

  /** 标签 */
  @Prop({
    type: [String],
    required: true,
  })
  tags: string[];

  /** 关联的图片 */
  @Prop({
    type: [
      {
        type: String,
        ref: 'Photos',
        autopopulate: true, // 启动自动填充
      },
    ],
    default: [],
  })
  photos: string[];

  /** 访问量 */
  @Prop({
    type: Number,
    default: 0,
  })
  viewCount: number;

  /** 收藏量 */
  @Prop({
    type: Number,
    default: 0,
  })
  favoriteCount: number;

  /** 点赞量 */
  @Prop({
    type: Number,
    default: 0,
  })
  likeCount: number;

  /** 发布状态 */
  @Prop({
    enum: ContentsStatusEnum,
    default: ContentsStatusEnum.Draft,
  })
  status: string;

  /** 是否允许他人查看 */
  @Prop({
    type: Boolean,
    default: true,
  })
  isPublic: boolean;

  /** 删除状态 */
  @Prop({
    type: Boolean,
    default: false,
  })
  isDeleted: boolean;

  /** 上传时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  createTime: Date;

  /** 更新时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  updateTime: Date;
}

export const ContentsSchema = SchemaFactory.createForClass(Contents);
