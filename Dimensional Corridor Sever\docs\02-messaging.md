# WebSocket 消息传递功能

## 📋 概述

本文档详细介绍WebSocket系统中的消息传递功能，包括私聊、群聊和系统通知。

## 💬 私聊消息

### 发送私聊消息
```javascript
// 发送私聊消息
function sendPrivateMessage(receiverId, content) {
  socket.emit('private_message', {
    receiverId: receiverId,  // 接收者用户ID
    content: content         // 消息内容
  });
}

// 使用示例
sendPrivateMessage('user123', '你好，最近怎么样？');
```

### 接收私聊消息
```javascript
// 监听私聊消息
socket.on('private_message', (data) => {
  console.log('收到私聊消息:', {
    from: data.from,           // 发送者ID
    content: data.content,     // 消息内容
    timestamp: data.timestamp  // 发送时间戳
  });
  
  // 在UI中显示消息
  displayPrivateMessage(data);
});

function displayPrivateMessage(data) {
  const messageElement = document.createElement('div');
  messageElement.className = 'private-message';
  messageElement.innerHTML = `
    <div class="message-header">
      <span class="sender">${data.from}</span>
      <span class="time">${new Date(data.timestamp).toLocaleTimeString()}</span>
    </div>
    <div class="message-content">${data.content}</div>
  `;
  
  document.getElementById('chat-messages').appendChild(messageElement);
}
```

### 私聊消息数据格式

#### 发送格式 (PrivateMessageDto)
```typescript
{
  receiverId: string;  // 接收者用户ID
  content: string;     // 消息内容 (最大1000字符)
}
```

#### 接收格式
```typescript
{
  from: string;        // 发送者用户ID
  content: string;     // 消息内容
  timestamp: number;   // 时间戳
}
```

## 👥 群聊消息

### 加入群组
```javascript
// 加入群组
function joinGroup(groupId) {
  socket.emit('join_group', groupId);
}

// 监听加入群组响应
socket.on('group_status', (data) => {
  if (data.action === 'joined') {
    console.log(`成功加入群组: ${data.groupId}`);
    console.log('群组成员:', data.members);
  }
});
```

### 发送群聊消息
```javascript
// 发送群聊消息
function sendGroupMessage(groupId, content) {
  socket.emit('group_message', {
    groupId: groupId,    // 群组ID
    content: content     // 消息内容
  });
}

// 使用示例
joinGroup('group456');  // 先加入群组
sendGroupMessage('group456', '大家好！');
```

### 接收群聊消息
```javascript
// 监听群聊消息
socket.on('group_message', (data) => {
  console.log('收到群聊消息:', {
    groupId: data.groupId,     // 群组ID
    from: data.from,           // 发送者ID
    content: data.content,     // 消息内容
    timestamp: data.timestamp  // 发送时间戳
  });
  
  // 在UI中显示群聊消息
  displayGroupMessage(data);
});

function displayGroupMessage(data) {
  const messageElement = document.createElement('div');
  messageElement.className = 'group-message';
  messageElement.innerHTML = `
    <div class="message-header">
      <span class="group">[${data.groupId}]</span>
      <span class="sender">${data.from}</span>
      <span class="time">${new Date(data.timestamp).toLocaleTimeString()}</span>
    </div>
    <div class="message-content">${data.content}</div>
  `;
  
  document.getElementById('chat-messages').appendChild(messageElement);
}
```

### 离开群组
```javascript
// 离开群组
function leaveGroup(groupId) {
  socket.emit('leave_group', groupId);
}

// 监听离开群组响应
socket.on('group_status', (data) => {
  if (data.action === 'left') {
    console.log(`已离开群组: ${data.groupId}`);
  }
});
```

### 群聊消息数据格式

#### 发送格式 (GroupMessageDto)
```typescript
{
  groupId: string;     // 群组ID
  content: string;     // 消息内容 (最大1000字符)
}
```

#### 接收格式
```typescript
{
  groupId: string;     // 群组ID
  from: string;        // 发送者用户ID
  content: string;     // 消息内容
  timestamp: number;   // 时间戳
}
```

## 📢 系统通知

### 发送系统通知 (仅管理员)
```javascript
// 发送系统通知 (需要管理员权限)
function sendSystemNotice(type, content) {
  socket.emit('system_notice', {
    type: type,          // 'alert' 或 'announcement'
    content: content     // 通知内容
  });
}

// 使用示例
sendSystemNotice('announcement', '系统将于今晚22:00进行维护');
sendSystemNotice('alert', '发现安全漏洞，请立即更新密码');
```

### 接收系统通知
```javascript
// 监听系统通知
socket.on('system_notice', (data) => {
  console.log('收到系统通知:', {
    type: data.type,           // 通知类型
    content: data.content,     // 通知内容
    timestamp: data.timestamp  // 时间戳
  });
  
  // 根据类型显示不同样式的通知
  displaySystemNotice(data);
});

function displaySystemNotice(data) {
  const noticeElement = document.createElement('div');
  noticeElement.className = `system-notice ${data.type}`;
  
  // 根据类型设置不同的图标和样式
  const icon = data.type === 'alert' ? '⚠️' : '📢';
  
  noticeElement.innerHTML = `
    <div class="notice-header">
      <span class="icon">${icon}</span>
      <span class="type">${data.type.toUpperCase()}</span>
      <span class="time">${new Date(data.timestamp).toLocaleString()}</span>
    </div>
    <div class="notice-content">${data.content}</div>
  `;
  
  // 添加到通知区域
  document.getElementById('system-notices').appendChild(noticeElement);
  
  // 自动消失 (可选)
  if (data.type === 'announcement') {
    setTimeout(() => {
      noticeElement.remove();
    }, 10000); // 10秒后自动消失
  }
}
```

### 系统通知数据格式

#### 发送格式 (SystemNoticeDto)
```typescript
{
  type: 'alert' | 'announcement';  // 通知类型
  content: string;                 // 通知内容 (最大500字符)
}
```

#### 接收格式
```typescript
{
  type: 'alert' | 'announcement'; // 通知类型
  content: string;                // 通知内容
  timestamp: number;              // 时间戳
}
```

## 🎯 完整的消息处理类

### 消息管理器
```javascript
class MessageManager {
  constructor(socket) {
    this.socket = socket;
    this.messageHistory = [];
    this.currentGroup = null;
    
    this.initEventListeners();
  }

  // 初始化事件监听
  initEventListeners() {
    // 私聊消息
    this.socket.on('private_message', (data) => {
      this.handlePrivateMessage(data);
    });

    // 群聊消息
    this.socket.on('group_message', (data) => {
      this.handleGroupMessage(data);
    });

    // 系统通知
    this.socket.on('system_notice', (data) => {
      this.handleSystemNotice(data);
    });

    // 群组状态变化
    this.socket.on('group_status', (data) => {
      this.handleGroupStatus(data);
    });
  }

  // 发送私聊消息
  sendPrivateMessage(receiverId, content) {
    if (!content.trim()) {
      console.warn('消息内容不能为空');
      return false;
    }

    if (content.length > 1000) {
      console.warn('消息内容过长，最大1000字符');
      return false;
    }

    this.socket.emit('private_message', {
      receiverId,
      content: content.trim()
    });

    // 添加到本地历史记录
    this.addToHistory({
      type: 'private',
      direction: 'sent',
      receiverId,
      content,
      timestamp: Date.now()
    });

    return true;
  }

  // 发送群聊消息
  sendGroupMessage(groupId, content) {
    if (!this.currentGroup || this.currentGroup !== groupId) {
      console.warn('请先加入群组');
      return false;
    }

    if (!content.trim()) {
      console.warn('消息内容不能为空');
      return false;
    }

    this.socket.emit('group_message', {
      groupId,
      content: content.trim()
    });

    // 添加到本地历史记录
    this.addToHistory({
      type: 'group',
      direction: 'sent',
      groupId,
      content,
      timestamp: Date.now()
    });

    return true;
  }

  // 加入群组
  joinGroup(groupId) {
    this.socket.emit('join_group', groupId);
  }

  // 离开群组
  leaveGroup(groupId) {
    this.socket.emit('leave_group', groupId);
    if (this.currentGroup === groupId) {
      this.currentGroup = null;
    }
  }

  // 处理私聊消息
  handlePrivateMessage(data) {
    this.addToHistory({
      type: 'private',
      direction: 'received',
      from: data.from,
      content: data.content,
      timestamp: data.timestamp
    });

    this.displayMessage(data, 'private');
  }

  // 处理群聊消息
  handleGroupMessage(data) {
    this.addToHistory({
      type: 'group',
      direction: 'received',
      groupId: data.groupId,
      from: data.from,
      content: data.content,
      timestamp: data.timestamp
    });

    this.displayMessage(data, 'group');
  }

  // 处理系统通知
  handleSystemNotice(data) {
    this.addToHistory({
      type: 'system',
      noticeType: data.type,
      content: data.content,
      timestamp: data.timestamp
    });

    this.displaySystemNotice(data);
  }

  // 处理群组状态
  handleGroupStatus(data) {
    if (data.action === 'joined') {
      this.currentGroup = data.groupId;
      console.log(`成功加入群组: ${data.groupId}`);
    } else if (data.action === 'left') {
      if (this.currentGroup === data.groupId) {
        this.currentGroup = null;
      }
      console.log(`已离开群组: ${data.groupId}`);
    }
  }

  // 添加到历史记录
  addToHistory(message) {
    this.messageHistory.push(message);
    
    // 限制历史记录数量
    if (this.messageHistory.length > 1000) {
      this.messageHistory = this.messageHistory.slice(-1000);
    }
  }

  // 获取消息历史
  getMessageHistory(filter = {}) {
    return this.messageHistory.filter(message => {
      if (filter.type && message.type !== filter.type) return false;
      if (filter.from && message.from !== filter.from) return false;
      if (filter.groupId && message.groupId !== filter.groupId) return false;
      return true;
    });
  }

  // 显示消息 (需要根据实际UI实现)
  displayMessage(data, type) {
    // 这里应该根据你的UI框架实现
    console.log(`显示${type}消息:`, data);
  }

  // 显示系统通知
  displaySystemNotice(data) {
    // 这里应该根据你的UI框架实现
    console.log('显示系统通知:', data);
  }
}

// 使用示例
const messageManager = new MessageManager(socket);

// 发送消息
messageManager.sendPrivateMessage('user123', '你好！');
messageManager.joinGroup('group456');
messageManager.sendGroupMessage('group456', '大家好！');
```

## ⚠️ 注意事项

### 1. 消息长度限制
- 私聊和群聊消息：最大1000字符
- 系统通知：最大500字符

### 2. 权限要求
- 私聊消息：需要认证用户
- 群聊消息：需要先加入群组
- 系统通知：仅管理员可发送

### 3. 内容过滤
- 系统会自动过滤恶意内容
- 敏感词汇会被替换或拒绝

### 4. 频率限制
- 建议实现客户端消息发送频率限制
- 避免短时间内大量发送消息

## 🔗 相关文档

- [连接与认证](./01-connection-auth.md)
- [用户状态管理](./03-user-status.md)
- [群组管理](./04-group-management.md)
- [错误处理](./05-error-handling.md)
