import {
  CanActivate, // 定义守卫接口
  ExecutionContext,
  HttpException, // 提供有关当前执行上下文的信息
  Injectable, // 标记该类可以被 NestJS 容器注入
} from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // 反射器，用于获取控制器或方法的元数据
import { JwtService } from '@nestjs/jwt'; // JWT 服务，用于解析和验证 JWT
import { Request } from 'express'; // Express 的请求对象
import { IS_PUBLIC_KEY } from '../decorator/public.decorator'; // 自定义装饰器，标记路由为公共，无需验证

import * as config from 'config'; // 配置模块，用于读取配置文件
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { UsersService } from 'src/users/service/users.service';
import { RedisService } from 'src/redis/redis.service';

const jwtConfig = config.get<JwtConfig>('jwt'); // 获取 JWT 相关配置

@Injectable()
export class AuthGuard implements CanActivate {
  // 实现 CanActivate 接口
  constructor(
    private jwtService: JwtService, // 注入 JWT 服务
    private reflector: Reflector, // 注入反射器
    private usersService: UsersService, // 注入 UserService
    private redisService: RedisService // 注入 RedisService
  ) {}

  /**
   * ?: 实现 canActivate 方法，用于决定请求是否可以被处理
   * @param context 控制器或方法的执行上下文
   * @returns 是否可以激活
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 实现 canActivate 方法，用于决定请求是否可以被处理
    // 从控制器或方法中获取是否为公共路由的元数据
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(), // 获取处理请求的方法
      context.getClass(), // 获取处理请求的控制器类
    ]);

    // 获取请求对象
    const request = context.switchToHttp().getRequest<Request>();
    // 从请求头中提取 JWT 令牌
    const token = this.extractToken(request);
    if (!token) {
      // 如果没有找到令牌，抛出未授权异常
      if (isPublic) {
        // 如果是公共路由，直接返回 true，表示可以激活
        return true;
      }
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }
    try {
      // 验证令牌，并获取负载信息
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: jwtConfig.secret, // 使用配置文件中的密钥进行验证
      });
      // 这里应该调用服务，查询用户信息
      const user = await this.usersService.findOneById(payload.id); // 查询用户信息
      if (!user || new Date(user.lockUntil) > new Date()) {
        if (isPublic) {
          // 如果是公共路由，直接返回 true，表示可以激活
          return true;
        }
        // 如果用户不存在，抛出未授权异常
        throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
      }
      // 将负载信息附加到请求对象上，以便后续的处理程序可以使用

      // 在redis中记录用户的活跃状态
      this.redisService.set(`online:${user.id}`, 'active', 300);

      request['user'] = { ...payload, role: user.role };
    } catch {
      // 如果验证失败，抛出未授权异常
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }
    // 如果令牌验证成功，返回 true，表示可以激活
    return true;
  }

  /**
   * ?: 私有方法，用于从请求头中提取令牌
   * @param request 请求对象
   * @returns 令牌值
   */
  private extractToken(request: Request): string | undefined {
    // 私有方法，用于从请求头中提取令牌
    // 从 Authorization 头中分割出令牌类型和令牌值
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    // 只有当令牌类型为 Bearer 时才返回令牌值，否则返回 undefined
    return type === 'Bearer' ? token : undefined;
  }
}
