/**
 * 加密敏感联系方式（手机号/电子邮箱）以保护用户隐私
 * @param {string} contact - 待加密的联系方式字符串
 * @returns {string} - 返回加密后的联系方式，格式如下：
 *                     - 手机号: 138****1234
 *                     - 邮箱: ab****@example.com
 *                     - 其他: 原样返回
 *
 * 加密规则说明：
 * 1. 手机号处理：
 *    - 验证是否为11位中国大陆手机号(1开头，第二位3-9)
 *    - 保留前3位和后4位，中间用4个星号代替
 *    - 自动过滤非数字字符
 *
 * 2. 邮箱处理：
 *    - 验证是否符合标准邮箱格式
 *    - 保留@符号前的前2个字符，后面用4个星号代替
 *    - 完整保留@符号及后面的域名部分
 *
 * 3. 其他情况：
 *    - 不符合手机号或邮箱格式的输入将原样返回
 *
 * 使用示例：
 * encryptContact('13812345678') => '138****5678'
 * encryptContact('<EMAIL>') => 'ab****@example.com'
 * encryptContact('其他内容') => '其他内容'
 */
export const encryptContact = (contact: string) => {
  // 处理手机号（11位数字）
  if (/^1[3-9]\d{9}$/.test(contact.replace(/\D/g, ''))) {
    // 去除所有非数字字符
    const cleaned = contact.replace(/\D/g, '');
    // 格式化为：前3位 + **** + 后4位
    return `${cleaned.substring(0, 3)} **** ${cleaned.substring(7)}`;
  }

  // 处理电子邮箱（保留前2个字符）
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact)) {
    // 分割用户名和域名部分
    const [username, domain] = contact.split('@');
    // 获取用户名前2个字符（防止用户名不足2个字符的情况）
    const prefix = username?.substring(0, Math.min(2, username.length));
    // 格式化为：前2字符 + **** + @ + 域名
    return `${prefix}****@${domain}`;
  }

  // 非手机号/邮箱的返回原值
  return contact;
};

