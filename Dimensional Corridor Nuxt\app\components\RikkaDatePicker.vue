<template>
  <div class="rikka-datepicker-container">
    <div class="rune-decoration">✪</div>
    <div class="date-input" @click="toggleCalendar">
      <div class="selected-date">
        {{ formattedDate || placeholder }}
      </div>
      <div class="calendar-icon">
        <IconSvg name="calendar" size="1.5rem" color="#fff" />
      </div>
    </div>

    <transition name="calendar">
      <div v-if="isOpen" class="calendar-overlay" @click.self="closeCalendar">
        <div class="calendar-popup">
          <div class="calendar-header">
            <button
              class="nav-button"
              @click="prevUnit"
              :disabled="isPrevNavDisabled"
            >
              <IconSvg name="leftArrows" size="1rem" color="#fff" />
            </button>

            <div class="header-title" @click="switchView">
              <span v-if="currentView === 'date'">
                {{ currentYear }}年{{ currentMonth + 1 }}月
              </span>
              <span v-if="currentView === 'year'">
                {{ yearRange.start }} - {{ yearRange.end }}
              </span>
            </div>

            <button
              class="nav-button"
              @click="nextUnit"
              :disabled="isNextNavDisabled"
            >
              <IconSvg name="rightArrows" size="1rem" color="#fff" />
            </button>
          </div>

          <div v-if="currentView === 'date'">
            <div class="weekdays">
              <div v-for="day in weekDays" :key="day" class="weekday">
                {{ day }}
              </div>
            </div>

            <div class="days-grid">
              <div
                v-for="day in days"
                :key="day.timestamp"
                :class="[
                  'day',
                  {
                    'prev-month': !day.isCurrentMonth,
                    today: day.isToday,
                    selected: day.isSelected,
                    'pre-selected': day.isPreSelected,
                    disabled: day.isDisabled,
                    blank: day.isBlank,
                  },
                ]"
                @click="!day.isDisabled && !day.isBlank && selectDate(day)"
              >
                <template v-if="!day.isBlank">
                  {{ day.date }}
                  <div v-if="day.isToday" class="today-marker"></div>
                  <div v-if="day.isSelected" class="selected-rune">✦</div>
                  <div v-if="day.isPreSelected" class="pre-selected-indicator">
                    ❖
                  </div>
                  <div v-if="day.isDisabled" class="disabled-overlay">⛔</div>
                </template>
              </div>
            </div>
          </div>

          <div v-else-if="currentView === 'year'" class="years-grid">
            <div
              v-for="year in visibleYears"
              :key="year"
              :class="[
                'year-item',
                {
                  current: year === currentYear,
                  selected: year === selectedYear,
                  disabled: isYearDisabled(year),
                },
              ]"
              @click="!isYearDisabled(year) && selectYear(year)"
            >
              {{ year }}
            </div>
          </div>

          <div class="calendar-footer">
            <button
              class="action-button"
              @click="selectToday"
              :disabled="isTodayDisabled"
            >
              选择今天
            </button>
            <button class="action-button" @click="clearDate">清除日期</button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

// 定义日历项接口
interface DayItem {
  date: number;
  timestamp: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isPreSelected: boolean;
  isDisabled: boolean;
  isBlank: boolean;
}

// 定义组件的props
const props = defineProps({
  modelValue: {
    type: [Date, String],
    default: null,
  },
  placeholder: {
    type: String,
    default: '选择日期...',
  },
  minDate: {
    type: [Date, String],
    default: '1900-01-01',
  },
  maxDate: {
    type: [Date, String],
    default: new Date(),
  },
});

const emit = defineEmits(['update:modelValue']);

const isOpen = ref(false);
const today = new Date();
const currentMonth = ref(today.getMonth());
const currentYear = ref(today.getFullYear());
const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
const preSelectedDate = ref<Date | null>(null);

// 新增状态管理
const currentView = ref<'date' | 'year'>('date');
const selectedYear = ref(today.getFullYear());
const yearRange = ref({ start: 0, end: 0, years: [] as number[] });

// 初始化年份范围 - 改为12年
const initYearRange = () => {
  const baseYear = currentYear.value;
  const start = Math.floor(baseYear / 12) * 12;
  const end = start + 11;

  yearRange.value = {
    start,
    end,
    years: Array.from({ length: 12 }, (_, i) => start + i),
  };
};

// 仅显示有效年份
const visibleYears = computed(() => {
  return yearRange.value.years.filter((year) => !isYearDisabled(year));
});

// 视图切换
const switchView = () => {
  currentView.value = currentView.value === 'date' ? 'year' : 'date';
  if (currentView.value === 'year') {
    initYearRange();
  }
};

// 检查日期是否在允许范围内
const isDateInRange = (date: Date): boolean => {
  const minDate = props.minDate
    ? props.minDate instanceof Date
      ? props.minDate
      : new Date(props.minDate)
    : null;

  const maxDate = props.maxDate
    ? props.maxDate instanceof Date
      ? props.maxDate
      : new Date(props.maxDate)
    : null;

  // 清除时间部分，只比较日期
  const checkDate = new Date(date);
  checkDate.setHours(0, 0, 0, 0);

  if (minDate) {
    const min = new Date(minDate);
    min.setHours(0, 0, 0, 0);
    if (checkDate < min) return false;
  }

  if (maxDate) {
    const max = new Date(maxDate);
    max.setHours(0, 0, 0, 0);
    if (checkDate > max) return false;
  }

  return true;
};

// 检查年份是否在允许范围内
const isYearDisabled = (year: number): boolean => {
  const minYear = props.minDate ? new Date(props.minDate).getFullYear() : null;
  const maxYear = props.maxDate ? new Date(props.maxDate).getFullYear() : null;

  return (
    (minYear !== null && year < minYear) || (maxYear !== null && year > maxYear)
  );
};

// 检查上个月按钮是否禁用
const isPrevButtonDisabled = computed(() => {
  if (currentView.value === 'date') {
    const prevMonthDate = new Date(
      currentYear.value,
      currentMonth.value - 1,
      1
    );
    return !isDateInRange(prevMonthDate);
  }
  return false;
});

// 检查下个月按钮是否禁用
const isNextButtonDisabled = computed(() => {
  if (currentView.value === 'date') {
    const nextMonthDate = new Date(
      currentYear.value,
      currentMonth.value + 1,
      1
    );
    return !isDateInRange(nextMonthDate);
  }
  return false;
});

// 检查年份视图的上一个范围按钮是否禁用
const isPrevYearsDisabled = computed(() => {
  if (currentView.value !== 'year') return false;

  const minYear = props.minDate ? new Date(props.minDate).getFullYear() : null;
  // 如果无最小年份限制，则不禁用
  if (minYear === null) return false;

  // 检查前一个范围是否有有效年份
  const prevRangeStart = yearRange.value.start - 12;
  const prevRangeEnd = yearRange.value.start - 1;

  // 只要前一个范围内有任何年份大于等于最小年份，就不禁用
  return prevRangeEnd < minYear;
});

// 检查年份视图的下一个范围按钮是否禁用
const isNextYearsDisabled = computed(() => {
  if (currentView.value !== 'year') return false;

  const maxYear = props.maxDate ? new Date(props.maxDate).getFullYear() : null;
  // 如果无最大年份限制，则不禁用
  if (maxYear === null) return false;

  // 检查下一个范围是否有有效年份
  const nextRangeStart = yearRange.value.end + 1;
  const nextRangeEnd = yearRange.value.end + 12;

  // 只要下一个范围内有任何年份小于等于最大年份，就不禁用
  return nextRangeStart > maxYear;
});

// 导航按钮禁用状态综合
const isPrevNavDisabled = computed(() => {
  return currentView.value === 'date'
    ? isPrevButtonDisabled.value
    : isPrevYearsDisabled.value;
});

const isNextNavDisabled = computed(() => {
  return currentView.value === 'date'
    ? isNextButtonDisabled.value
    : isNextYearsDisabled.value;
});

// 检查"今天"按钮是否禁用
const isTodayDisabled = computed(() => {
  return !isDateInRange(today);
});

// 智能调整当前月份到可用范围
const adjustMonthToRange = () => {
  if (!props.minDate && !props.maxDate) return;

  const minDate = props.minDate
    ? props.minDate instanceof Date
      ? props.minDate
      : new Date(props.minDate)
    : null;

  const maxDate = props.maxDate
    ? props.maxDate instanceof Date
      ? props.maxDate
      : new Date(props.maxDate)
    : null;

  // 如果当前年月早于最小日期
  if (minDate) {
    const minYear = minDate.getFullYear();
    const minMonth = minDate.getMonth();

    if (
      currentYear.value < minYear ||
      (currentYear.value === minYear && currentMonth.value < minMonth)
    ) {
      currentYear.value = minYear;
      currentMonth.value = minMonth;
    }
  }

  // 如果当前年月晚于最大日期
  if (maxDate) {
    const maxYear = maxDate.getFullYear();
    const maxMonth = maxDate.getMonth();

    if (
      currentYear.value > maxYear ||
      (currentYear.value === maxYear && currentMonth.value > maxMonth)
    ) {
      currentYear.value = maxYear;
      currentMonth.value = maxMonth;
    }
  }
};

// 选择年份时智能调整月份
const selectYear = (year: number) => {
  currentYear.value = year;
  selectedYear.value = year;

  // 智能调整月份到可用范围
  adjustMonthToRange();

  currentView.value = 'date';
};

// 单位切换（月份或12年）
const prevUnit = () => {
  if (currentView.value === 'date') {
    prevMonth();
  } else {
    // 年份范围改为12年
    yearRange.value.start -= 12;
    yearRange.value.end -= 12;
    yearRange.value.years = Array.from(
      { length: 12 },
      (_, i) => yearRange.value.start + i
    );
  }
};

const nextUnit = () => {
  if (currentView.value === 'date') {
    nextMonth();
  } else {
    // 年份范围改为12年
    yearRange.value.start += 12;
    yearRange.value.end += 12;
    yearRange.value.years = Array.from(
      { length: 12 },
      (_, i) => yearRange.value.start + i
    );
  }
};

const formattedDate = computed(() => {
  if (!props.modelValue) return '';
  const date =
    props.modelValue instanceof Date
      ? props.modelValue
      : new Date(props.modelValue);
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
});

// 创建日期项的工具函数
const createDayItem = (date: Date, isCurrentMonth: boolean): DayItem => {
  const modelDate = props.modelValue
    ? props.modelValue instanceof Date
      ? props.modelValue
      : new Date(props.modelValue)
    : null;

  // 检查是否在允许范围内
  const isInRange = isDateInRange(date);

  return {
    date: date.getDate(),
    timestamp: date.getTime(),
    isCurrentMonth,
    isToday: isSameDay(date, today),
    isSelected: !!(modelDate && isSameDay(date, modelDate)),
    isPreSelected: !!(
      preSelectedDate.value && isSameDay(date, preSelectedDate.value)
    ),
    isDisabled: !isInRange,
    isBlank: false,
  };
};

// 生成日历日期数组（固定42天）
const days = computed(() => {
  const daysArray: DayItem[] = [];
  const firstDayOfMonth = new Date(currentYear.value, currentMonth.value, 1);
  const lastDayOfMonth = new Date(currentYear.value, currentMonth.value + 1, 0);

  // 获取当前月第一天是周几 (0 = 周日, 1 = 周一, ...)
  const firstDayOfWeek = firstDayOfMonth.getDay();

  // 添加上个月最后几天（只添加可见部分）
  const prevMonthDays = firstDayOfWeek;
  for (let i = prevMonthDays; i > 0; i--) {
    const dayDate = new Date(currentYear.value, currentMonth.value, -i + 1);
    daysArray.push(createDayItem(dayDate, false));
  }

  // 添加本月所有天
  for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
    const dayDate = new Date(currentYear.value, currentMonth.value, i);
    daysArray.push(createDayItem(dayDate, true));
  }

  // 添加下个月前几天（固定到42天）
  const totalDays = 42; // 6行 * 7天
  const nextMonthDays = totalDays - daysArray.length;
  for (let i = 1; i <= nextMonthDays; i++) {
    const dayDate = new Date(currentYear.value, currentMonth.value + 1, i);
    daysArray.push(createDayItem(dayDate, false));
  }

  return daysArray;
});

// 辅助函数：判断是否是同一天
const isSameDay = (date1: Date, date2: Date) => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

const toggleCalendar = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    preSelectedDate.value = null;
    currentView.value = 'date';

    // 初始化当前选中年份
    if (props.modelValue) {
      const date =
        props.modelValue instanceof Date
          ? props.modelValue
          : new Date(props.modelValue);
      currentYear.value = date.getFullYear();
      currentMonth.value = date.getMonth();
    } else {
      currentYear.value = today.getFullYear();
      currentMonth.value = today.getMonth();
    }

    // 智能调整月份到可用范围
    adjustMonthToRange();

    selectedYear.value = currentYear.value;
  }
};

const closeCalendar = () => {
  isOpen.value = false;
  preSelectedDate.value = null;
};

const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
  preSelectedDate.value = null;
};

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
  preSelectedDate.value = null;
};

// 修改后的selectDate方法
const selectDate = (day: DayItem) => {
  if (day.isCurrentMonth) {
    // 当前月日期：选中并关闭日历
    const selectedDate = new Date(
      currentYear.value,
      currentMonth.value,
      day.date
    );
    emit('update:modelValue', selectedDate);
    isOpen.value = false;
    preSelectedDate.value = null;
  } else {
    // 非当前月日期：只切换月份视图，不选中日期
    const selectedDate = new Date(day.timestamp);
    currentYear.value = selectedDate.getFullYear();
    currentMonth.value = selectedDate.getMonth();

    // 设置预选中日期
    preSelectedDate.value = selectedDate;

    // 智能调整月份到可用范围
    adjustMonthToRange();
  }
};

const selectToday = () => {
  emit('update:modelValue', today);
  isOpen.value = false;
  preSelectedDate.value = null;
};

const clearDate = () => {
  emit('update:modelValue', null);
  isOpen.value = false;
  preSelectedDate.value = null;
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    closeCalendar();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style lang="scss" scoped>
.rikka-datepicker-container {
  position: relative;

  .rune-decoration {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -0.7rem;
    color: var(--datepicker-rune-color);
    font-size: 1.8rem;
    opacity: 0.7;
    text-shadow: var(--datepicker-rune-glow);
    transition: all 0.3s ease;
    z-index: 1;
  }

  .date-input {
    position: relative;
    width: 100%;
    padding: 0.7rem 1rem;
    background: var(--datepicker-input-bg);
    border: var(--datepicker-input-border);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--datepicker-input-shadow);
    cursor: pointer;
    backdrop-filter: blur(0.125rem);
    text-shadow: 0 0 0.1875rem rgba(138, 43, 226, 0.3);

    &:hover {
      border: var(--datepicker-input-hover-border);
      box-shadow: var(--datepicker-input-hover-shadow);
    }

    .calendar-icon {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &:hover .rune-decoration {
    opacity: 1;
    text-shadow: 0 0 0.9375rem var(--magic-pink);
  }
}

.calendar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(0.3125rem);
  z-index: 1000;

  .calendar-popup {
    position: relative;
    background: var(--datepicker-calendar-bg);
    border: var(--datepicker-calendar-border);
    border-radius: var(--border-radius-md);
    box-shadow: var(--datepicker-calendar-shadow);
    width: 25rem;
    max-width: 90vw;
    padding: 1.5rem;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.25rem;
      background: var(--datepicker-header-accent);
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.2rem;
      position: relative;

      .header-title {
        cursor: pointer;
        padding: 0.3rem 0.8rem;
        border-radius: var(--border-radius-sm);
        transition: all 0.2s;

        &:hover {
          background: rgba(var(--interactive-primary-rgb), 0.2);
          box-shadow: var(--shadow-neon-primary);
        }
      }

      .current-month {
        color: var(--text-primary);
        font-weight: bold;
        text-shadow: var(--shadow-neon-primary);
        font-size: 1.3rem;
        letter-spacing: 0.0625rem;
      }

      .nav-button {
        background: var(--datepicker-button-bg);
        border: var(--datepicker-button-border);
        color: var(--text-primary);
        width: 2.2rem;
        height: 2.2rem;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        text-shadow: var(--shadow-neon-primary);

        &:hover:not(:disabled) {
          background: rgba(var(--interactive-primary-rgb), 0.4);
          box-shadow: var(--shadow-neon-primary);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          filter: grayscale(0.8);
        }
      }
    }

    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      text-align: center;
      margin-bottom: 0.8rem;
      background: rgba(var(--interactive-primary-rgb), 0.2);
      border-radius: var(--border-radius-sm);
      padding: 0.1875rem 0;

      .weekday {
        color: var(--datepicker-weekday-color);
        font-size: 0.95rem;
        padding: 0.1875rem;
        font-weight: bold;
        text-shadow: var(--shadow-neon-primary);
      }

      .weekday:first-child,
      .weekday:last-child {
        color: var(--datepicker-weekend-color);
      }
    }

    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 0.1875rem;

      .day {
        aspect-ratio: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--text-primary);
        cursor: pointer;
        border-radius: var(--border-radius-sm);
        position: relative;
        transition: all 0.2s;
        font-weight: 500;
        background: var(--datepicker-day-bg);
        border: 0.0625rem solid rgba(var(--text-secondary-rgb), 0.3);

        &:hover:not(.disabled):not(.blank) {
          background: var(--datepicker-day-hover-bg);
          transform: translateY(-0.125rem);
          box-shadow: var(--shadow-dimension-sm);
        }

        &.prev-month {
          color: var(--text-secondary);
          opacity: 0.6;
        }

        &.today {
          color: var(--datepicker-today-color);
          font-weight: bold;
          border: var(--datepicker-today-border);

          .today-marker {
            position: absolute;
            bottom: 0.125rem;
            width: 0.25rem;
            height: 0.25rem;
            background: var(--datepicker-today-color);
            border-radius: 50%;
            box-shadow: var(--shadow-neon-glow);
          }
        }

        &.selected {
          background: var(--datepicker-selected-bg);
          color: var(--text-primary);
          font-weight: bold;
          border-color: var(--magic-pink);
          box-shadow: var(--datepicker-selected-shadow);
          transform: scale(1.05);

          .selected-rune {
            position: absolute;
            top: 0.0625rem;
            right: 0.0625rem;
            color: var(--magic-pink);
            text-shadow: var(--shadow-neon-danger);
            font-size: 0.8rem;
          }
        }

        &.pre-selected {
          background: var(--datepicker-preselected-bg);
          border: var(--datepicker-preselected-border);
          box-shadow: 0 0 0.625rem rgba(var(--magic-blue-rgb), 0.5);
          animation: pulse 1s infinite;

          .pre-selected-indicator {
            position: absolute;
            top: 0.0625rem;
            right: 0.0625rem;
            color: var(--magic-blue);
            text-shadow: 0 0 0.5rem var(--magic-blue);
            font-size: 0.8rem;
          }
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.4;
          filter: grayscale(0.8);
          pointer-events: none;
        }

        &.blank {
          background: transparent;
          border: none;
          pointer-events: none;
          visibility: hidden;
        }

        .disabled-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 1.2rem;
          opacity: 0.7;
        }
      }
    }

    // 年份选择器样式 - 改为3行×4列
    .years-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 0.625rem;
      margin-top: 1rem;

      .year-item {
        padding: 0.8rem 0.5rem;
        text-align: center;
        border-radius: var(--border-radius-sm);
        background: rgba(var(--interactive-primary-rgb), 0.1);
        border: 0.0625rem solid rgba(var(--text-secondary-rgb), 0.3);
        cursor: pointer;
        transition: all 0.2s;
        font-weight: 500;

        &:hover:not(.disabled) {
          background: rgba(var(--interactive-primary-rgb), 0.3);
          transform: translateY(-0.125rem);
          box-shadow: var(--shadow-dimension-sm);
        }

        &.current {
          color: var(--datepicker-today-color);
          font-weight: bold;
          border: var(--datepicker-today-border);
          animation: glow 2s infinite;
        }

        &.selected {
          background: var(--datepicker-selected-bg);
          color: var(--text-primary);
          font-weight: bold;
          border-color: var(--magic-pink);
          box-shadow: var(--datepicker-selected-shadow);
          transform: scale(1.05);
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.4;
          filter: grayscale(0.8);
          pointer-events: none;
        }
      }
    }

    .calendar-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 1.5rem;

      .action-button {
        padding: 0.7rem 1.5rem;
        background: var(--datepicker-button-bg);
        border: var(--datepicker-button-border);
        color: var(--text-primary);
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        transition: all 0.2s;
        font-weight: bold;
        letter-spacing: 0.0625rem;
        text-shadow: var(--shadow-neon-primary);
        box-shadow: var(--shadow-dimension-sm);

        &:hover:not(:disabled) {
          background: rgba(var(--interactive-primary-rgb), 0.4);
          box-shadow: var(--shadow-neon-primary);
        }

        &:last-child {
          background: var(--datepicker-clear-button-bg);
          border: var(--datepicker-clear-button-border);

          &:hover:not(:disabled) {
            background: rgba(var(--interactive-danger-rgb), 0.4);
            box-shadow: var(--shadow-neon-danger);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          filter: grayscale(0.8);
        }
      }
    }
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px #8a2be2;
    box-shadow: 0 0 5px #8a2be2;
  }
  50% {
    text-shadow: 0 0 15px #ff00ff;
    box-shadow: 0 0 15px #ff00ff;
  }
  100% {
    text-shadow: 0 0 5px #8a2be2;
    box-shadow: 0 0 5px #8a2be2;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 5px #00bfff;
  }
  50% {
    box-shadow: 0 0 15px #00bfff;
  }
  100% {
    box-shadow: 0 0 5px #00bfff;
  }
}

.calendar-enter-active,
.calendar-leave-active {
  transition: opacity 0.3s ease;
}

.calendar-enter-from,
.calendar-leave-to {
  opacity: 0;
}

.calendar-enter-active .calendar-popup,
.calendar-leave-active .calendar-popup {
  transition:
    transform 0.4s ease,
    opacity 0.4s ease;
}

.calendar-enter-from .calendar-popup,
.calendar-leave-to .calendar-popup {
  opacity: 0;
  transform: translateY(2rem) scale(0.95);
}
</style>

