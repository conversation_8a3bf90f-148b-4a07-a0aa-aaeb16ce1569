export const useAuthStore = defineStore(
  'auth',
  () => {
    const authStore: Ref<Auth | undefined> = ref();
    const token = ref('');
    const isAuthenticated = ref(false);
    const setAuth = (auth: Auth) => {
      authStore.value = auth;
    };
    const getAuth = () => {
      return authStore.value;
    };
    const setToken = (str: string) => {
      token.value = str;
      isAuthenticated.value = true;
    };
    const getToken = () => {
      return token.value;
    };
    const clear = () => {
      authStore.value = undefined;
      isAuthenticated.value = false;
      token.value = '';
    };
    const getIsLogin = () => {
      return isAuthenticated.value;
    };
    return {
      authStore,
      token,
      isAuthenticated,
      setAuth,
      getAuth,
      setToken,
      getToken,
      clear,
      getIsLogin,
    };
  },
  {
    // nuxt项目中将cookie作为持久化存储，刷新页面后依然存在
    persist: {
      storage: piniaPluginPersistedstate.cookies({
        maxAge: 60 * 60, // 1 小时
      }),
    },
  }
);
