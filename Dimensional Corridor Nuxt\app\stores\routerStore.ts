export type DeviceType =
  | 'mobile'
  | 'tablet'
  | 'console'
  | 'smarttv'
  | 'wearable'
  | 'xr'
  | 'embedded'
  | 'desktop';

export const useRouterStore = defineStore(
  'router',
  () => {
    const routerHistory = ref<string[]>(['/']);
    const device = ref<DeviceType>('desktop');

    const set = (path: string) => {
      // 确保路径以/开头
      const normalizedPath = path.startsWith('/') ? path : `/${path}`;

      // 如果当前路径与历史最后一个路径相同，不添加
      if (
        routerHistory.value.length > 0 &&
        routerHistory.value[routerHistory.value.length - 1] === normalizedPath
      ) {
        return;
      }

      // 查找路径是否已存在于历史记录中
      const existingIndex = routerHistory.value.indexOf(normalizedPath);
      if (existingIndex !== -1) {
        // 如果存在，先从历史记录中删除
        routerHistory.value.splice(existingIndex, 1);
      }

      // 添加新路径到历史记录末尾
      routerHistory.value.push(normalizedPath);
    };

    const get = () => {
      // 返回当前路径（历史记录的最后一项）
      return routerHistory.value.length > 0
        ? routerHistory.value[routerHistory.value.length - 1]
        : '/';
    };

    const goBack = () => {
      // 如果历史记录大于1，则移除最后一项
      if (routerHistory.value.length > 1) {
        routerHistory.value.pop();
        return routerHistory.value[routerHistory.value.length - 1];
      }
      return '/';
    };

    const setDevice = (type: DeviceType) => {
      device.value = type;
    };

    const getDevice = () => {
      return device.value;
    };

    return {
      routerHistory,
      set,
      get,
      goBack,
      device,
      setDevice,
      getDevice,
    };
  },
  {
    // nuxt项目中将cookie作为持久化存储，刷新页面后依然存在
    persist: {
      storage: piniaPluginPersistedstate.cookies({
        maxAge: 60 * 60 * 24 * 7, // 1 周
      }),
    },
  }
);
