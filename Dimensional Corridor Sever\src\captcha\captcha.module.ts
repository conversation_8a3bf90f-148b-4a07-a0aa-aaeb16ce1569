import { Module } from '@nestjs/common';
import { CaptchaService } from './captcha.service';
import { CaptchaController } from './captcha.controller';
import { RedisModule } from 'src/redis/redis.module';
import * as config from 'config'; // 引入配置模块
import { MailerModule } from '@nestjs-modules/mailer';
import { join } from 'path';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UnsubscribeEmail,
  UnsubscribeEmailSchema,
} from './schemas/unsubscribeEmail.schemas';

const emailConfig = config.get<EmailConfig>('email'); // 获取邮箱相关配置
@Module({
  imports: [
    RedisModule, // 导入 RedisModule，用于 Redis 相关的操作
    MongooseModule.forFeature([
      { name: UnsubscribeEmail.name, schema: UnsubscribeEmailSchema },
    ]),
    MailerModule.forRoot({
      transport: {
        host: emailConfig.host, // SMTP 服务器地址
        port: emailConfig.port,
        secure: emailConfig.secure, // 使用 TLS 协议
        auth: {
          user: emailConfig.user, // 发件人邮箱
          pass: emailConfig.pass, // 邮箱密码或应用专用密码（Gmail需配置）
        },
      },
      defaults: {
        from: emailConfig.from, // 默认发件人
      },
      template: {
        dir: join(__dirname, '../shared', 'templates'), // 模板文件目录
        adapter: new HandlebarsAdapter(), // 使用 Handlebars
        options: {
          strict: true, // 严格模式
        },
      },
    }),
  ],
  providers: [CaptchaService],
  controllers: [CaptchaController],
  exports: [CaptchaService],
})
export class CaptchaModule {}
