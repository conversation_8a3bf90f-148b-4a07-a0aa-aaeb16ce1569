import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsNumber,
  Min,
  ValidateNested,
  IsNotEmpty,
  IsUrl,
  Length,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PhotoStatusEnum } from 'src/shared/enums/Photo.enum';

export class PhotoAttributesDto {
  /** 图片大小 */
  @ApiProperty({ description: '图片大小' })
  @IsNumber({}, { message: '图片大小必须是数字' })
  @Min(0, { message: '图片大小不能小于0' })
  size: number;

  /** 图片宽度 */
  @ApiProperty({ description: '图片宽度' })
  @IsNumber({}, { message: '图片宽度必须是数字' })
  @Min(0, { message: '图片宽度不能小于0' })
  width: number;

  /** 图片高度 */
  @ApiProperty({ description: '图片高度' })
  @IsNumber({}, { message: '图片高度必须是数字' })
  @Min(0, { message: '图片高度不能小于0' })
  height: number;

  /** 图片格式 */
  @ApiProperty({ description: '图片格式' })
  @IsString({ message: '图片格式必须是字符串' })
  format: string;
}

export class CreatePhotoDto {
  /** 图片名 */
  @ApiProperty({ description: '图片名' })
  @IsNotEmpty({ message: '图片名不能为空' })
  @IsString({ message: '图片名必须是字符串' })
  @Length(1, 30, { message: '图片名长度不能超过30' })
  filename: string;

  /** 下载图片名 */
  @ApiProperty({ description: '下载图片名' })
  @IsNotEmpty({ message: '下载图片名不能为空' })
  @IsString({ message: '下载图片名必须是字符串' })
  @Length(1, 100, { message: '下载图片名长度不能超过100' })
  downloadFilename: string;

  /** 图片链接 */
  @ApiProperty({ description: '图片链接' })
  @IsNotEmpty({ message: '图片链接不能为空' })
  @IsString({ message: '图片链接必须是字符串' })
  @IsUrl({}, { message: '图片链接格式不正确' })
  url: string;

  /** 分类 */
  @ApiProperty({ description: '图片分类' })
  @IsNotEmpty({ message: '分类不能为空' })
  @IsString({ message: '分类必须是字符串' })
  @Length(1, 10, { message: '分类长度不能超过10' })
  category: string;

  /** 标签 */
  @ApiProperty({ description: '图片标签', type: [String] })
  @IsNotEmpty({ message: '标签不能为空' })
  @IsArray({ message: '标签必须是数组' })
  @IsString({ each: true, message: '每个标签必须是字符串' })
  @Length(1, 10, { each: true, message: '每个标签长度不能超过10' })
  tags: string[];

  /** 图片属性 */
  @ApiProperty({
    description: '图片属性',
    type: PhotoAttributesDto,
  })
  @IsNotEmpty({ message: '图片属性不能为空' })
  @ValidateNested()
  @Type(() => PhotoAttributesDto)
  attributes: PhotoAttributesDto;

  /** 图片状态 */
  @ApiProperty({ description: '图片状态', enum: PhotoStatusEnum })
  @IsNotEmpty({ message: '图片状态不能为空' })
  @IsString({ message: '图片状态必须是字符串' })
  status: PhotoStatusEnum;
}
