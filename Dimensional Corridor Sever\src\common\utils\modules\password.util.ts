import * as bcrypt from 'bcryptjs';

export class PasswordUtil {
  /**
   * 生成盐值
   * @param rounds 盐值轮数 (默认10)
   */
  static async genSalt(rounds = 10): Promise<string> {
    return bcrypt.genSalt(rounds);
  }

  /**
   * 哈希密码
   * @param password 明文密码
   * @param salt 盐值（可选）
   */
  static async hash(password: string, salt?: string): Promise<string> {
    return salt
      ? bcrypt.hash(password, salt)
      : bcrypt.hash(password, await this.genSalt());
  }

  /**
   * 验证密码
   * @param plainPassword 用户输入的密码
   * @param hashedPassword 数据库存储的哈希值
   */
  static async compare(
    plainPassword: string,
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
