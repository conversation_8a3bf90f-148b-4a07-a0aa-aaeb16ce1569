import { ApiProperty } from '@nestjs/swagger';
import { IsString, Length, IsNotEmpty, IsEmail } from 'class-validator';

export class sendVerificationEmailDto {
  @ApiProperty({
    description: '邮箱',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: '邮箱格式不正确' }) // 确保字段是邮箱类型
  @IsNotEmpty({ message: '邮箱不能为空' }) // 字段不能为空
  readonly email: string;

  @ApiProperty({
    description: '图像验证码',
    required: true,
    example: '12ab',
  })
  @IsString({ message: '验证码必须是字符串类型' }) // 确保字段是字符串类型
  @Length(4, 4, { message: '验证码长度必须为4位' }) // 确保字段长度为4位
  @IsNotEmpty({ message: '验证码不能为空' }) // 字段不能为空
  readonly code: string;
}
