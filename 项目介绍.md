# 次元回廊 (Dimensional Corridor)

一款基于现代技术栈的图片分享交友网站，为用户提供优质的社交体验和图片分享平台。

## 🚀 技术栈

### 后端 (NestJS)

- **框架**: NestJS 11.x - 企业级 Node.js 框架
- **数据库**: MongoDB + Mongoose - 文档型数据库
- **缓存**: Redis - 高性能缓存和会话存储
- **认证**: JWT + Passport - 安全的用户认证体系
- **API 文档**: Swagger - 自动生成 API 文档
- **实时通信**: Socket.IO - WebSocket 实时消息推送
- **安全防护**: Helmet + Throttler - 安全头和接口限流
- **任务调度**: @nestjs/schedule - 定时任务管理

### 前端 (Nuxt.js)

- **框架**: Nuxt.js 3.x - Vue.js 全栈框架
- **状态管理**: Pinia + 持久化存储
- **UI 框架**: TailwindCSS + 自定义组件库
- **工具库**: VueUse - Vue 组合式 API 工具集
- **图片处理**: CropperJS - 图片裁剪功能
- **动画**: Motion-V - 流畅的页面动画
- **主题**: Color Mode - 深色/浅色主题切换
- **开发工具**: ESLint + Prettier - 代码规范

## 📱 核心功能

### 用户系统

- **多种登录方式**: 邮箱、手机号、微信登录
- **验证码服务**: 集成阿里云短信服务和邮件服务
- **用户资料**: 完整的个人信息管理
- **账户安全**: 密码修改、手机/邮箱绑定、账户注销

### 内容管理

- **图片上传**: 支持多图上传和图片裁剪
- **内容发布**: 图文混合内容发布系统
- **瀑布流展示**: 响应式图片瀑布流布局
- **内容搜索**: 全文搜索和标签筛选

### 社交功能

- **实时聊天**: WebSocket 实时消息系统
- **用户互动**: 点赞、评论、关注功能
- **AI 聊天**: 集成 AI 对话功能
- **通知系统**: 实时消息通知

### 管理后台

- **用户管理**: 用户信息管理和权限控制
- **内容审核**: 图片和文本内容审核
- **数据统计**: 用户活跃度和内容统计

## 🛠 开发特性

### 安全性

- **图形验证码**: SVG 验证码防机器人
- **接口限流**: 防止恶意请求
- **数据加密**: 敏感信息加密存储
- **HTTPS 支持**: 全站 HTTPS 加密传输

### 性能优化

- **Redis 缓存**: 热点数据缓存
- **图片优化**: 图片压缩和 CDN 加速
- **SSR 渲染**: 服务端渲染提升 SEO
- **懒加载**: 图片和组件懒加载

### 移动端适配

- **响应式设计**: 完美适配各种设备
- **移动端优化**: 专门的移动端布局
- **触摸手势**: 支持移动端手势操作
- **PWA 支持**: 渐进式 Web 应用

## 🌟 项目亮点

1. **现代化架构**: 采用最新的前后端分离架构
2. **类型安全**: 全栈 TypeScript 开发
3. **实时交互**: WebSocket 实时通信
4. **多端适配**: 桌面端和移动端完美适配
5. **可扩展性**: 模块化设计，易于扩展新功能
6. **开发体验**: 完善的开发工具链和代码规范

## 📦 部署信息

- **前端域名**: web.sixflower.love (HTTPS)
- **后端 API**: api.sixflower.love:3000 (HTTPS)
- **开发端口**: 前端 1314，后端 3000
- **SSL 证书**: 自签名证书支持 HTTPS 开发
