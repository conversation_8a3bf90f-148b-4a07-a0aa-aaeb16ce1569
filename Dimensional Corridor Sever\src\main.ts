import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as path from 'path';
import { logger } from './common/middlewares';
import * as config from 'config';
import * as cookieParser from 'cookie-parser';
import helmet from 'helmet';
import { apiKeyAuth } from './common/middlewares/apiKeyAuth.middleware';
import { appUrl } from './common/utils';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { readFileSync } from 'fs';

const { port } = config.get<ServerConfig>('server');
const swaggerConfig = config.get<SwaggerConfig>('swagger');

async function bootstrap() {
  const httpsOptions = {
    key: readFileSync('C:/myOpenSSL/api.sixflower.love.key').toString(),
    cert: readFileSync('C:/myOpenSSL/api.sixflower.love.crt').toString(),
  };

  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    httpsOptions,
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // 全局前缀
  // app.setGlobalPrefix('v1');
  // 静态资源目录
  app.useStaticAssets(path.join(__dirname, 'public'));
  // 全局使用请求日志中间件
  app.use(logger({ console: false }));
  // 启用cookie解析
  app.use(cookieParser());
  // 允许跨域
  app.enableCors({
    origin: ['https://web.sixflower.love:1314', 'https://www.sixflower.top'], // 允许所有域名访问，生产环境建议配置具体的域名
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // 允许的请求方法
    allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key'], // 允许的请求头
    exposedHeaders: ['Content-Range', 'X-Content-Range'], // 允许的响应头
    credentials: true, // 允许携带cookie
    maxAge: 86400, // 预检请求缓存时间
  });
  // 请求头密钥验证
  app.use(
    apiKeyAuth([
      swaggerConfig.path,
      '/captcha/unsubscribe',
      '/captcha/subscribe',
    ])
  );
  // 启用安全防护
  app.use(helmet());
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true, // 自动类型转换
      whitelist: true, // 去除未定义属性
      transformOptions: {
        enableImplicitConversion: true, // 启用隐式类型转换
      },
      forbidNonWhitelisted: true, // 拒绝非法属性
    })
  );
  // Swagger 接口文档自动生成配置
  const config = new DocumentBuilder()
    .setTitle(swaggerConfig.title)
    .setDescription(swaggerConfig.description)
    .setVersion(swaggerConfig.version)
    .addBearerAuth(
      { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
      'JWT 令牌'
    ) // JWT 认证
    .addApiKey({ type: 'apiKey', name: 'x-api-key' }, '请求密钥') // API Key 认证
    .addOAuth2() // OAuth2 认证
    .addTag('Auth', '用户认证相关接口')
    .addTag('Captcha', '验证码相关接口')
    .addTag('Users', '用户管理相关接口')
    .addTag('Photos', '用户上传图片相关接口')
    .addTag('Contents', '内容管理相关接口')
    .addTag('Search', '搜索相关接口')
    .addTag('Admins', '管理员管理相关接口')
    .addTag('Aichat', 'AI聊天相关接口')
    .build();
  const document = SwaggerModule.createDocument(app, config);

  // 新增：统计接口数量
  let endpointCount = 0;
  for (const path in document.paths) {
    endpointCount += Object.keys(document.paths[path]).length;
  }

  if (process.env.NODE_ENV != 'production') {
    SwaggerModule.setup(swaggerConfig.path, app, document); // 文档路径
  } else {
    SwaggerModule.setup(swaggerConfig.path, app, document, {
      swaggerOptions: {
        operationsSorter: (a: SwaggerOperation, b: SwaggerOperation) => {
          // 类型声明
          type OperationTagGetter = (op: SwaggerOperation) => string;
          type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

          // 处理未分配标签的接口（带类型断言）
          const getFirstTag: OperationTagGetter = (operation) => {
            const tags = operation.get('tags') as string[] | undefined;
            return tags?.[0] || '未分类'; // 使用合法操作符
          };
          // 获取带类型的标签值
          const tagA: string = getFirstTag(a);
          const tagB: string = getFirstTag(b);
          // 标签排序（修正方法名大小写）
          const tagCompare = tagA.localeCompare(tagB);
          if (tagCompare !== 0) return tagCompare;
          // HTTP 方法类型断言
          const methodOrder: HttpMethod[] = ['get', 'post', 'put', 'delete'];
          const getMethod = (op: SwaggerOperation): HttpMethod =>
            String(op.get('method')).toLowerCase() as HttpMethod;
          // 安全排序
          return (
            methodOrder.indexOf(getMethod(a)) -
            methodOrder.indexOf(getMethod(b))
          );
        },
      },
    });
  }

  // 监听端口
  await app.listen(port);

  // 打印启动信息
  console.log(
    `服务器已启动，访问地址：${appUrl.formatUrl(await app.getUrl(), 'api.sixflower.love')}
Swagger 文档地址：${appUrl.formatUrl(await app.getUrl(), 'api.sixflower.love') + swaggerConfig.path}
API 接口数量：${endpointCount}`
  );
}

bootstrap();
