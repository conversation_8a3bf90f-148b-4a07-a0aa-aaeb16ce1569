export default defineNuxtPlugin((nuxtApp) => {
  const visible = ref(false);
  const title = ref('魔法执行中...');
  const description = ref('正在召唤不可视境界线');

  const start = (options?: LoadingOptions) => {
    visible.value = true;
    title.value = options?.title || '魔法执行中...';
    description.value = options?.description || '正在召唤不可视境界线';
  };

  const stop = () => {
    visible.value = false;
  };

  const loadingState = reactive({
    visible,
    title,
    description,
    start,
    stop,
  });

  return {
    provide: {
      loading: loadingState,
    },
  };
});
