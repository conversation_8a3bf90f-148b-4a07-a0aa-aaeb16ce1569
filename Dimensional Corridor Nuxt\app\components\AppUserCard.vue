<template>
  <ClientOnly>
    <CardContainer class="user-card">
      <CardBody
        @click="$router.push({ path: `/user`, query: { uid: users.uid } })"
        :style="bodyStyle"
      >
        <CardItem :translate-z="40" style="width: 100%">
          <div class="user-content">
            <img :src="users.avatar" class="user-avatar" />
            <h3 class="user-nickname">{{ users.nickname }}</h3>
            <p class="user-bio">{{ users.bio }}</p>

            <div class="user-stats">
              <div class="stat-item">
                <IconSvg name="follow" :size="14" color="#CCCCCC" />
                <span>{{ users.isFans ? '粉丝' : '未关注' }}</span>
              </div>
              <div class="stat-item">
                <IconSvg name="clock" :size="14" color="#CCCCCC" />
                <span>{{ lastLoginText }}</span>
              </div>
            </div>
            <RippleButton
              class="follow-button"
              :class="{ active: users.isFollowing }"
              :disabled="!authStore.isAuthenticated"
              @click.stop="toggleFollow"
            >
              {{ users.isFollowing ? '已关注' : '关注' }}</RippleButton
            >
          </div>
        </CardItem>
      </CardBody>
    </CardContainer>
  </ClientOnly>
</template>

<script setup lang="ts">
const props = defineProps<{
  users: UsersList;
  fixedWidth: number;
  fixedHeight: number;
}>();

const authStore = useAuthStore();

const emit = defineEmits(['toggle-follow']);

// 计算属性，生成卡片主体样式
const bodyStyle = computed(() => ({
  width: `${props.fixedWidth}px`,
  cursor: 'pointer',
  backgroundImage: `url(${props.users.background})`,
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'contain',
  height: `${props.fixedHeight * 0.8}px`,
  borderRadius: '1rem 1rem 0 0',
}));

// 格式化最后登录时间
const lastLoginText = computed(() => {
  const now = new Date();
  const loginTime = new Date(props.users.lastLoginTime);
  const diffDays = Math.floor(
    (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffDays === 0) return '今日活跃';
  if (diffDays === 1) return '昨日活跃';
  return `${diffDays}天前活跃`;
});

// 关注按钮点击事件
const toggleFollow = async () => {
  await useApi().followUser(props.users.uid);
  props.users.isFollowing = !props.users.isFollowing;
  emit('toggle-follow', props.users.uid);
};
</script>

<style scoped lang="scss">
.user-card {
  position: relative;

  .user-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-size: cover;
    background-position: center;
    filter: blur(2px);
    opacity: 0.8;
    border-radius: 1rem 1rem 0 0;
  }

  .user-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: linear-gradient(
      to bottom,
      rgba(53, 0, 71, 0.7) 0%,
      rgba(41, 0, 90, 0.9) 100%
    );
    color: #f0e6ff;
    border-radius: 1rem;

    .user-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 3px solid rgba(255, 255, 255, 0.3);
      margin-top: 20px;
      object-fit: cover;
      object-position: center;
      box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
      transition: var(--transition);

      &:hover {
        transform: scale(1.05);
      }
    }

    .user-nickname {
      margin: 15px 0 8px;
      font-size: 1.2rem;
      text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }

    .user-bio {
      text-align: center;
      font-size: 0.85rem;
      margin-bottom: 20px;
      height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .user-stats {
      display: flex;
      justify-content: space-around;
      width: 100%;
      margin-bottom: 20px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.8rem;

        span {
          margin-top: 5px;
        }
      }
    }

    .follow-button {
      width: 8rem;
      background-color: var(--button-primary);
      color: var(--text-primary);
      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: var(--button-primary-hover);
      }
    }
  }
}
</style>
