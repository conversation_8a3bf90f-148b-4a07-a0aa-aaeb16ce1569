{"apikeys": ["123456"], "database": {"host": "127.0.0.1", "port": 27017, "name": "dimensional", "username": "", "password": ""}, "redis": {"host": "***************", "port": 26739, "password": "wxYN8WtehRMxWPxS"}, "jwt": {"secret": "652ebc195a3b2157f281606eaa737fdd0b8239b919c1cf0a58a33f64aaa0d150", "expiresIn": "1h"}, "email": {"appName": "次元回廊", "supportEmail": "<EMAIL>", "unsubscribeUrl": "http://localhost:3000/users/unsubscribe", "host": "smtp.ethereal.email", "port": 587, "secure": false, "user": "<EMAIL>", "pass": "kc1wvzQMfPgkCZvcdR", "from": "<EMAIL>"}, "twilio": {"accountSid": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", "authToken": "your_auth_token", "phone": "+**********"}, "dysmsapi": {"accessKeyId": "your_access_key_id", "accessKeySecret": "your_access_key_secret", "signName": "your_sign_name", "templateCode": "your_template_code"}, "wechat": {"appID": "your_app_id", "appSecret": "your_app_secret", "callbackUrl": "your_callback_url"}, "swagger": {"title": "次元回廊 服务器 API", "description": "小鸟游六花风格的次元回廊服务器 API 文档 模拟邮件接收地址:https://ethereal.email/messages", "version": "1.0.0", "path": "/api-docs"}, "deepSeekChat": {"apiKey": "9d40c2f759389137621462e8688057ce03c85ac6", "baseUrl": "https://api-d7i005u44728z7x4.aistudio-app.com/v1", "aiPrompt": "中二病女友身份枷锁"}, "defaultAdmin": {"uid": "000000", "nickname": "六六六花花", "bio": "小鸟游六花风格的次元回廊模拟邮件接收地址: https://ethereal.email/messages", "email": "<EMAIL>", "phone": "***********", "password": "Zc@3176154067", "registerIP": "127.0.0.1"}}