在 NestJS 中实现手机验证码发送功能需要以下步骤：

---

### **1. 选择短信服务商**

国内常用短信平台（任选其一）：

- **阿里云短信服务**（推荐，稳定且文档齐全）
- **腾讯云短信**
- **云片网**
- **容联云通讯**

以 **阿里云短信服务** 为例，其他平台流程类似。

---

### **2. 注册并配置短信服务**

1. **开通阿里云短信服务**：
   - 登录 [阿里云控制台](https://www.aliyun.com/)。
   - 进入「短信服务」>「快速学习」完成资质申请和签名/模板审核。
2. **获取关键信息**：
   - `AccessKey ID` 和 `AccessKey Secret`（API 密钥）。
   - **短信签名**（如「XX 科技」）。
   - **模板 CODE**（如 `SMS_123456789`，模板内容示例：`您的验证码是：${code}，5分钟内有效！`）。

---

### **3. 安装依赖**

```bash
npm install @alicloud/sms-sdk    # 阿里云短信SDK
npm install redis               # 存储验证码（推荐使用Redis）
npm install @nestjs/cache-manager cache-manager-redis-store  # NestJS Redis集成
```

---

### **4. 配置 Redis 存储验证码**

```typescript
// src/redis/redis.module.ts
import { CacheModule } from '@nestjs/cache-manager'
import { Module } from '@nestjs/common'
import * as redisStore from 'cache-manager-redis-store'

@Module({
  imports: [
    CacheModule.register({
      store: redisStore,
      host: 'localhost',
      port: 6379,
      ttl: 300, // 默认过期时间（秒）
    }),
  ],
  exports: [CacheModule],
})
export class RedisModule {}
```

---

### **5. 创建短信服务**

```typescript
// src/sms/sms.service.ts
import { Injectable } from '@nestjs/common'
import { Cache } from 'cache-manager'
import { Inject } from '@nestjs/common'
import * as SMS from '@alicloud/sms-sdk'

@Injectable()
export class SmsService {
  private smsClient: SMS

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {
    // 初始化阿里云短信客户端
    this.smsClient = new SMS({
      accessKeyId: 'your-access-key-id',
      secretAccessKey: 'your-access-key-secret',
    })
  }

  // 生成随机6位验证码
  private generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  // 发送验证码
  async sendCode(phone: string): Promise<boolean> {
    const code = this.generateCode()

    // 检查是否频繁发送（如60秒内只能发送一次）
    const lastSent = await this.cacheManager.get(`sms_cooldown:${phone}`)
    if (lastSent) {
      throw new Error('请求过于频繁，请稍后再试')
    }

    try {
      // 调用阿里云短信API
      const response = await this.smsClient.sendSMS({
        PhoneNumbers: phone,
        SignName: 'XX科技',
        TemplateCode: 'SMS_123456789',
        TemplateParam: `{"code":"${code}"}`,
      })

      if (response.Code === 'OK') {
        // 存储验证码到Redis（5分钟过期）
        await this.cacheManager.set(`sms_code:${phone}`, code, { ttl: 300 })
        // 设置冷却时间（60秒）
        await this.cacheManager.set(`sms_cooldown:${phone}`, '1', { ttl: 60 })
        return true
      } else {
        throw new Error(`短信发送失败: ${response.Message}`)
      }
    } catch (error) {
      throw new Error(`短信服务异常: ${error.message}`)
    }
  }

  // 验证验证码
  async verifyCode(phone: string, code: string): Promise<boolean> {
    const storedCode = await this.cacheManager.get(`sms_code:${phone}`)
    if (storedCode === code) {
      // 验证成功后删除验证码
      await this.cacheManager.del(`sms_code:${phone}`)
      return true
    }
    return false
  }
}
```

---

### **6. 创建控制器**

```typescript
// src/sms/sms.controller.ts
import { Controller, Post, Body } from '@nestjs/common'
import { SmsService } from './sms.service'

@Controller('sms')
export class SmsController {
  constructor(private readonly smsService: SmsService) {}

  @Post('send-code')
  async sendCode(@Body('phone') phone: string) {
    await this.smsService.sendCode(phone)
    return { success: true, message: '验证码已发送' }
  }

  @Post('verify-code')
  async verifyCode(@Body('phone') phone: string, @Body('code') code: string) {
    const isValid = await this.smsService.verifyCode(phone, code)
    return { success: isValid, message: isValid ? '验证成功' : '验证码错误' }
  }
}
```

---

### **7. 注册模块**

```typescript
// src/sms/sms.module.ts
import { Module } from '@nestjs/common'
import { SmsService } from './sms.service'
import { SmsController } from './sms.controller'
import { RedisModule } from '../redis/redis.module'

@Module({
  imports: [RedisModule],
  providers: [SmsService],
  controllers: [SmsController],
})
export class SmsModule {}
```

---

### **8. 配置环境变量**

通过 `.env` 管理敏感信息：

```env
# .env
REDIS_HOST=localhost
REDIS_PORT=6379
ALIYUN_ACCESS_KEY_ID=your-key-id
ALIYUN_ACCESS_KEY_SECRET=your-secret
```

修改 `RedisModule` 和 `SmsService` 读取环境变量：

```typescript
// redis.module.ts
CacheModule.register({
  store: redisStore,
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT),
})

// sms.service.ts
this.smsClient = new SMS({
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
  secretAccessKey: process.env.ALIYUN_ACCESS_KEY_SECRET,
})
```

---

### **9. 测试验证码流程**

#### **发送验证码**

```bash
curl -X POST http://localhost:3000/sms/send-code \
  -H "Content-Type: application/json" \
  -d '{"phone": "13812345678"}'
```

#### **验证验证码**

```bash
curl -X POST http://localhost:3000/sms/verify-code \
  -H "Content-Type: application/json" \
  -d '{"phone": "13812345678", "code": "123456"}'
```

---

### **注意事项**

1. **安全防护**：
   - 限制同一 IP/手机号的发送频率（防短信轰炸）。
   - 验证码有效期建议设为 5 分钟。
2. **生产环境建议**：
   - 使用异步队列（如 RabbitMQ）处理短信发送，避免接口阻塞。
   - 集成 Sentinel 或 Hystrix 实现熔断机制。
3. **短信模板规范**：
   - 模板内容需符合服务商审核要求（如包含「验证码」字样）。
4. **兜底方案**：
   - 准备多个短信服务商，在主服务商不可用时自动切换。

---

通过以上步骤，你可以在 NestJS 中实现完整的手机验证码发送和验证功能。
