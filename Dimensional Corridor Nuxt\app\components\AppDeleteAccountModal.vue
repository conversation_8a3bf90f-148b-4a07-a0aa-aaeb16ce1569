<template>
  <RikkaDialog
    :show="show"
    title="注销账户确认"
    :width="dialogWidth"
    :mask-closable="false"
    @close="close"
  >
    <!-- 主要内容区域 -->
    <div class="dialog-content">
      <!-- 警示文本 -->
      <div class="warning-text">此操作不可撤销，请谨慎操作！</div>

      <!-- 后果说明区块 -->
      <div class="consequences-title">请仔细阅读以下后果：</div>
      <ul class="consequences-list">
        <li>所有个人数据将被永久删除且无法恢复</li>
        <li>您的所有内容和历史记录将被清除</li>
        <li>账户关联的服务将立即停止</li>
        <li>您将无法再登录或使用相关服务</li>
        <li>订阅服务不会自动退款</li>
        <li>账户名将释放并可被其他人注册</li>
      </ul>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <RippleButton
          class="btn-confirm"
          @click="confirmDelete"
          ripple-color="red"
          :disabled="!authStore.getIsLogin()"
          >确认注销</RippleButton
        >
      </div>
    </template>
  </RikkaDialog>
</template>

<script lang="ts" setup>
// 组件属性定义
const { show } = defineProps({
  show: Boolean, // 控制模态框显示状态
});

// 认证状态管理
const authStore = useAuthStore();

// 移动端适配 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '30rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 事件发射器定义
const emit = defineEmits<{
  close: [value: boolean]; // 关闭事件，携带是否成功标志
}>();

// 关闭处理函数
const close = (flag = false) => {
  emit('close', flag);
};

// 账户注销确认处理
const confirmDelete = async () => {
  try {
    await useApi().deleteMyAccount();
    authStore.clear();
    useMessage({
      name: '注销成功',
      description: '您的账户已成功注销，感谢您的使用！',
      type: 'info',
    });
    close(true);
  } catch (err) {}
};
</script>

<style lang="scss" scoped>
.dialog-content {
  color: #e0e0e0;

  .warning-text {
    font-weight: 600;
    color: #ff6b6b;
    padding: 0.8rem;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 8px;
    margin-bottom: 1.2rem;
    text-align: center;
    border: 1px solid rgba(255, 107, 107, 0.3);
    font-size: 1.2rem;
  }

  .consequences-title {
    font-weight: 600;
    color: #94a3b8;
    margin-bottom: 1rem;
    font-size: 1.05rem;
    padding-left: 0.5rem;
  }

  .consequences-list {
    list-style: none;
    padding: 0;

    li {
      padding: 0.4rem 1rem 0.4rem 2.5rem;
      margin-bottom: 0.7rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      position: relative;
      transition: all 0.2s ease;
      border: 1px solid rgba(255, 255, 255, 0.08);

      &:before {
        content: '⚠️';
        position: absolute;
        left: 0.8rem;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
      }

      &:hover {
        background: rgba(255, 107, 107, 0.1);
        border-color: rgba(255, 107, 107, 0.3);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;
  padding: 1rem 0;
  background: #1a2435;
  border-top: 1px solid rgba(255, 255, 255, 0.08);

  > .btn-cancel {
    width: 8rem;
    background-color: rgba(255, 255, 255, 0.08);
    color: #cbd5e1;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
      color: #fff;
    }
  }

  > .btn-confirm {
    width: 8rem;
    background: linear-gradient(
      135deg,
      rgba(255, 107, 107, 0.8) 0%,
      rgba(255, 77, 77, 0.9) 100%
    );
    color: white;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
    font-weight: 500;
    border: none;

    &:hover:not(:disabled) {
      background: linear-gradient(
        135deg,
        rgba(255, 77, 77, 0.9) 0%,
        rgba(255, 51, 51, 1) 100%
      );
      box-shadow: 0 0 15px rgba(255, 77, 77, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: #4b5563;
    }
  }
}
</style>

