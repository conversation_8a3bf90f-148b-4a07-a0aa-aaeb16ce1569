export const useTabStore = defineStore(
  'tab',
  () => {
    const mineTab = ref(0);
    const userTab = ref(0);

    const setMineTab = (index: number) => {
      mineTab.value = index;
    };
    const getMineTab = () => {
      return mineTab.value;
    };

    const setUserTab = (index: number) => {
      userTab.value = index;
    };
    const getUserTab = () => {
      return userTab.value;
    };

    return {
      mineTab,
      setMineTab,
      getMineTab,
      userTab,
      setUserTab,
      getUserTab,
    };
  },
  {
    // 添加持久化存储，保存tab状态
    persist: {
      storage: piniaPluginPersistedstate.cookies({
        maxAge: 60 * 60 * 24, // 1 天
      }),
    },
  }
);

