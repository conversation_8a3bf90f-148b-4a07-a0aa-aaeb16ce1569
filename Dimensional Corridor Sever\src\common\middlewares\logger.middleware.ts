import * as fs from 'fs/promises'; // 导入文件系统模块的Promise版本
import * as path from 'path'; // 导入路径模块
import { createWriteStream, WriteStream } from 'fs'; // 导入创建写入流的方法
import * as os from 'os'; // 导入操作系统模块
import { NextFunction, Request, Response } from 'express'; // 导入Express框架的相关类型

// 定义日志配置类型
type LoggerConfig = {
  logDirectory: string; // 日志存储目录
  format: 'json' | 'text'; // 日志格式，可以是json或text
  level: 'info' | 'debug'; // 日志级别，可以是info或debug
  rotation: RotationConfig; // 日志轮转配置
  console: boolean; // 是否在控制台输出日志
};

// 定义日志轮转配置类型
type RotationConfig = {
  size: number; // 单个日志文件的最大大小，单位为字节
  keepDays: number; // 保留日志的天数
  archive: boolean; // 是否归档日志
};

// 定义日志数据类型
type LogData = {
  timestamp: string; // 日志时间戳
  method: string; // 请求方法
  url: string; // 请求URL
  ip: string; // 请求IP地址
  userAgent: string; // 用户代理
  status: number; // 响应状态码
  responseTime: number; // 响应时间
  referrer: string; // 引用页
};

// 默认日志配置
const defaultOptions: LoggerConfig = {
  logDirectory: path.join(process.cwd(), 'logs'), // 日志存储在当前工作目录下的logs文件夹
  format: 'json', // 默认日志格式为json
  level: 'info', // 默认日志级别为info
  rotation: {
    size: 500 * 1024 * 1024, // 单个日志文件的最大大小为500MB
    keepDays: 7, // 保留日志7天
    archive: true, // 归档日志
  },
  console: process.env.NODE_ENV !== 'production', // 在非生产环境下输出到控制台
};

// 增强请求日志记录类
class EnhancedRequestLogger {
  private config: LoggerConfig; // 日志配置
  private writeStreams: Map<string, WriteStream>; // 写入流映射
  private pendingWrites: Map<string, Promise<void>>; // 挂起的写入操作

  constructor(options: Partial<LoggerConfig> = {}) {
    // 合并默认配置和传入配置
    this.config = { ...defaultOptions, ...options };
    this.writeStreams = new Map(); // 初始化写入流映射
    this.pendingWrites = new Map(); // 初始化挂起的写入操作映射
    // 初始化，若失败则输出错误信息
    this.init().catch((err) => console.error('[Logger] 初始化失败:', err));
  }

  // 初始化方法
  private async init() {
    try {
      // 创建日志目录，若目录已存在则不报错
      await fs.mkdir(this.config.logDirectory, { recursive: true });
      // 每天清理一次旧日志
      setInterval(() => {
        this.cleanOldLogs();
      }, 86400000);
    } catch (err) {
      console.error('[Logger] 目录创建失败:', err);
    }
  }

  // 中间件方法，返回一个中间件函数
  public middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now(); // 请求开始时间

      // 创建日志并写入的方法
      const log = async () => {
        try {
          const logData = this.createLogData(req, res, startTime); // 创建日志数据
          await this.writeLog(logData); // 写入日志
        } catch (err) {
          console.error('[Logger] 日志记录失败:', err);
        }
      };

      // 当响应结束时执行日志记录
      res.on('finish', () => {
        log();
      });

      next(); // 执行下一个中间件
    };
  }

  // 创建日志数据的方法
  private createLogData(
    req: Request,
    res: Response,
    startTime: number
  ): LogData {
    return {
      timestamp: new Date().toISOString(), // 当前时间的时间戳
      method: req.method, // 请求方法
      url: req.originalUrl, // 请求的原始URL
      ip: this.sanitizeIp(
        req.ip || req.connection.remoteAddress?.toString() || ''
      ), // 获取并清理请求的IP地址
      userAgent: req.headers['user-agent'] || '', // 用户代理
      status: res.statusCode, // 响应状态码
      responseTime: Date.now() - startTime, // 响应时间
      referrer: req.headers.referer || '', // 引用页
    };
  }

  // 写入日志的方法
  private async writeLog(data: LogData) {
    const content = this.formatContent(data); // 格式化日志内容
    if (this.config.console) {
      // 如果配置中允许在控制台输出
      console.log(
        this.config.format === 'json' ? JSON.stringify(data, null, 2) : content // 输出格式化的日志内容
      );
    }

    try {
      const filePath = this.getLogFilePath(); // 获取日志文件路径
      await this.handleLogRotation(filePath); // 处理日志轮转
      await this.queueWriteOperation(filePath, content); // 排队写入日志
    } catch (err) {
      console.error('[Logger] 日志写入错误:', err);
    }
  }

  // 格式化日志内容的方法
  private formatContent(data: LogData): string {
    return this.config.format === 'json'
      ? JSON.stringify(data) + os.EOL // 若格式为json，则将日志数据转换为JSON字符串并添加换行符
      : [
          `[${data.timestamp}]`, // 时间戳
          `${data.method} ${data.url}`, // 请求方法和URL
          `IP: ${data.ip}`, // IP地址
          `Status: ${data.status}`, // 状态码
          `Response: ${data.responseTime}ms`, // 响应时间
          `Referrer: ${data.referrer}`, // 引用页
          `UA: ${data.userAgent}`, // 用户代理
        ].join(' | ') + os.EOL; // 若格式为text，则将各个日志信息用' | '连接并添加换行符
  }

  // 获取日志文件路径的方法
  private getLogFilePath(): string {
    const date = new Date().toISOString().slice(0, 10); // 获取当前日期的字符串
    const filename = `access_${date}.log`; // 构建日志文件名
    return path.join(this.config.logDirectory, filename); // 返回日志文件的完整路径
  }

  // 处理日志轮转的方法
  private async handleLogRotation(filePath: string) {
    try {
      const stats = await fs.stat(filePath); // 获取文件状态
      // 如果当前日志文件的大小超过配置中定义的大小，则进行日志轮转
      if (stats.size > this.config.rotation.size) {
        await this.rotateLogFile(filePath);
      }
    } catch (err) {
      // 如果文件不存在，不抛出错误，否则抛出错误
      if ((err as NodeJS.ErrnoException).code !== 'ENOENT') throw err;
    }
  }

  // 日志轮转的具体实现方法
  private async rotateLogFile(filePath: string) {
    const archivePath = `${filePath}.${Date.now()}.bak`; // 构建归档日志文件名
    await fs.rename(filePath, archivePath); // 将当前日志文件重命名为归档文件名
    this.closeStream(filePath); // 关闭当前日志文件的写入流
  }

  // 排队写入日志的方法
  private async queueWriteOperation(filePath: string, content: string) {
    // 如果存在对该文件的写入操作，则等待操作完成
    while (this.pendingWrites.has(filePath)) {
      await this.pendingWrites.get(filePath);
    }

    // 创建写入操作的Promise
    const writePromise = this.performWrite(filePath, content);
    this.pendingWrites.set(filePath, writePromise); // 将写入操作存入映射中
    await writePromise; // 等待写入操作完成
    this.pendingWrites.delete(filePath); // 从映射中删除已完成的写入操作
  }

  // 执行写入日志的具体方法
  private async performWrite(filePath: string, content: string) {
    const writer = this.getWriteStream(filePath); // 获取写入流
    // 将内容写入文件，若写入失败则reject，否则resolve
    await new Promise<void>((resolve, reject) => {
      writer.write(content, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  // 获取写入流的方法
  private getWriteStream(filePath: string): WriteStream {
    if (!this.writeStreams.has(filePath)) {
      // 如果不存在该文件的写入流
      const writer = createWriteStream(filePath, {
        flags: 'a', // 追加模式
        encoding: 'utf8', // 编码格式为utf8
        autoClose: true, // 自动关闭流
      });
      writer.on('error', (err) => {
        // 监听写入流的错误事件
        console.error('[Logger] 流写入错误:', err);
        this.closeStream(filePath); // 关闭写入流
      });
      this.writeStreams.set(filePath, writer); // 将写入流存入映射中
    }
    return this.writeStreams.get(filePath)!; // 返回写入流
  }

  // 关闭写入流的方法
  private closeStream(filePath: string) {
    if (this.writeStreams.has(filePath)) {
      // 如果存在该文件的写入流
      const stream = this.writeStreams.get(filePath)!; // 获取写入流
      stream.end(); // 关闭写入流
      this.writeStreams.delete(filePath); // 从映射中删除该写入流
    }
  }

  // 清理旧日志的方法
  private async cleanOldLogs() {
    try {
      const now = Date.now(); // 获取当前时间
      const files = await fs.readdir(this.config.logDirectory); // 获取日志目录下的所有文件名

      for (const file of files) {
        const filePath = path.join(this.config.logDirectory, file); // 构建文件路径
        const isArchive = /\.bak$/.test(file); // 判断是否为归档文件

        if (isArchive) {
          // 如果是归档文件
          // 从文件名中提取时间戳
          const timestamp = parseInt(file.split('.').slice(-2, -1)[0], 10);
          // 如果当前时间减去文件时间戳大于保留天数，则删除该文件
          if (now - timestamp > this.config.rotation.keepDays * 86400000) {
            await fs.unlink(filePath);
          }
        }
      }
    } catch (err) {
      console.error('[Logger] 日志清理失败:', err);
    }
  }

  // 清理IP地址的方法
  private sanitizeIp(ip: string): string {
    return ip
      .replace(/^::ffff:/, '') // 移除IPv4映射的前缀
      .replace(/[^a-fA-F0-9.:]/g, '') // 移除非IP字符
      .replace(/(?::):+/g, '_') // 替换连续的冒号为下划线
      .replace(/(?::)$/, '_'); // 替换末尾的冒号为下划线
  }
}

/**
 * ?: 该模块导出一个函数，返回一个网络请求日志记录中间件函数
 * @param options 日志配置
 * @returns 一个中间件函数
 */
export function logger(options: Partial<LoggerConfig> = {}) {
  const logger = new EnhancedRequestLogger(options); // 创建日志记录器实例
  return logger.middleware(); // 返回中间件函数
}
