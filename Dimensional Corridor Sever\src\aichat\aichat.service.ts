import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import axios from 'axios';
import * as config from 'config';
import { Readable } from 'stream'; // 添加Node.js流类型
import { readFile } from 'fs';
import { join } from 'path';
import { Cron } from '@nestjs/schedule';

const chatConfig = config.get<DeepSeekChatConfig>('deepSeekChat');

let aiPrompt = '';

@Injectable()
export class AichatService {
  // 使用预定义表达式（每分钟执行）
  // @Cron(CronExpression.EVERY_MINUTE)
  // handleCron() {
  //   this.logger.debug('每分钟执行的任务');
  // }

  // 自定义 Cron 表达式（每天 8:30 AM）
  // @Cron('30 8 * * *')
  // dailyTask() {
  //   Logger.debug('每日 8:30 执行的任务');
  // }

  // 每 10 秒执行（单位：毫秒）
  // @Interval(10000)
  // handleInterval() {
  //   this.logger.debug('每 10 秒执行的任务');
  // }

  // 应用启动后 5 秒执行
  // @Timeout(5000)
  // handleTimeout() {
  //   this.logger.debug('应用启动 5 秒后执行');
  // }

  // 每个整点执行（例如: 08:00, 09:00, 10:00...）
  @Cron('0 0 * * * *', {
    name: 'hourly-task',
    timeZone: 'Asia/Shanghai', // 设置你的时区
  })
  handleHourlyTask() {
    Logger.log('⏰ 整点任务执行');
    this.handleChatStream('你好，欢迎来到次元回廊！', '默认身份');
  }

  /**
   * 处理流式聊天请求
   * @param message 用户输入的消息
   * @param res Express响应对象
   */
  async handleChatStream(
    message: string,
    identity: string = '默认身份',
    res?: Response
  ): Promise<void> {
    try {
      readFile(
        join(__dirname, `../shared/aiPrompt/${identity}.txt`),
        'utf8',
        (err, data) => {
          if (err) throw err;
          aiPrompt = data;
        }
      );

      // 发起流式API请求
      const apiRes = await axios.post<Readable>(
        `${chatConfig.baseUrl}/chat/completions`,
        {
          model: 'deepseek-r1:70b', // 使用的AI模型
          temperature: 0.6, // 生成文本的随机性控制（0-1）
          messages: [
            {
              role: 'system',
              content: aiPrompt,
            },
            {
              // 对话消息历史
              role: 'user',
              content: message,
            },
          ],
          stream: true, // 启用流式响应
        },
        {
          headers: {
            Authorization: `Bearer ${chatConfig.apiKey}`, // API密钥认证
            'Content-Type': 'application/json', // 请求格式
          },
          responseType: 'stream', // 声明需要流式响应
        }
      );

      // 因为api两小时不调用会关闭，所以定时发送空消息保持连接，此时没有传入res，所以不处理响应
      if (!res) return;

      // 设置心跳机制（每30秒发送空消息保持连接）
      const heartbeat = setInterval(() => res.write(': keep-alive\n\n'), 30000);
      const stream = apiRes.data;
      // 流事件监听
      stream
        .on(
          'data',
          (
            chunk: Buffer // 收到数据块时处理
          ) => this.processChunk(chunk, res)
        )
        .on('end', () =>
          // 流结束时清理资源
          this.closeStream(res)
        )
        .on('error', (err: Error) => {
          // 错误处理
          clearInterval(heartbeat); // 清除心跳定时器
          this.handleStreamError(err, res);
        });
    } catch (err) {
      if (!res) return;

      // 捕获初始请求阶段的异常
      this.handleStreamError(err, res);
    }
  }

  /**
   * 处理流式响应中的数据块
   * @param chunk 数据块
   * @param res Express响应对象
   */
  private processChunk(chunk: Buffer, res: Response): void {
    // 将二进制数据块转换为UTF-8字符串
    chunk
      .toString('utf8')
      // 按换行符分割为独立数据包（SSE协议规范）
      .split('\n')
      // 过滤有效数据行（忽略空行和注释）
      .filter((line) => line.startsWith('data: '))
      // 处理每个有效数据包
      .forEach((line) => {
        // 提取并清理有效载荷（移除"data: "前缀和空白字符）
        const data = line.slice(6).trim();

        // 检测流结束标志
        if (data === '[DONE]') return this.closeStream(res);

        try {
          // 解析JSON数据结构
          const chunkData = JSON.parse(data) as ChatCompletionChunk;
          // 安全访问嵌套属性：choices[0].delta.content
          const content = chunkData.choices[0]?.delta?.content;

          // 转发有效内容到客户端
          if (content) this.writeStreamData(res, content);
        } catch {
          // 静默处理解析错误，避免中断整个流式响应
          // 典型错误场景：不完整的数据包或服务端格式异常
        }
      });
  }

  /**
   * 处理流错误
   * @param err 错误对象
   * @param res Express响应对象
   */
  private handleStreamError(err: unknown, res: Response): void {
    const error = err instanceof Error ? err : new Error('Unknown error');
    res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
    res.end();
  }

  /**
   * 写入流式数据到响应
   * @param res Express响应对象
   * @param content 要写入的内容
   */
  private writeStreamData(res: Response, content: string): void {
    // res.write(`data: ${JSON.stringify({ content })}\n\n`);
    res.write(`data: ${content}\n\n`);
  }

  /**
   * 关闭流式响应
   * 发送一个特殊的标记来表示流结束
   * @param res Express响应对象
   */
  private closeStream(res: Response): void {
    res.write('data: [END]\n\n');
    res.end();
  }
}
