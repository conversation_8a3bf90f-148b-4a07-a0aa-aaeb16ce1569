import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsMongoId,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';
import { XssSanitize } from 'src/common/decorator/xss-escape.decorator';
import { ContentsStatusEnum } from 'src/shared/enums/Content.enum';

export class UpdateContentDto {
  /** 标题 去除空格 防止xss攻击 */
  @ApiProperty({ description: '帖子标题' })
  @IsOptional()
  @IsString({ message: '标题必须是字符串' })
  @Length(1, 50, { message: '标题长度必须在1-50之间' })
  @XssSanitize({ message: '标题不能包含脚本' })
  title: string;

  /** 内容 */
  @ApiProperty({ description: '帖子内容' })
  @IsOptional()
  @IsString({ message: '内容必须是字符串' })
  @Length(1, 1000, { message: '内容长度必须在1-1000之间' })
  @XssSanitize({ message: '内容中不能包含脚本' })
  content: string;

  /** 分类 */
  @ApiProperty({ description: '帖子分类' })
  @IsOptional()
  @IsString({ message: '分类必须是字符串' })
  @Length(1, 10, { message: '分类长度必须在1-10之间' })
  category: string;

  /** 标签 */
  @ApiProperty({ description: '帖子标签', type: [String] })
  @IsOptional()
  @IsArray({ message: '标签必须是数组' })
  @IsString({ each: true, message: '每个标签必须是字符串' })
  tags: string[];

  /** 关联的图片 */
  @ApiProperty({
    description: '关联的图片id数组',
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '图片必须是数组' })
  @IsMongoId({ each: true, message: '每个图片ID必须是有效的MongoDB ID' })
  photos: string[];

  /** 帖子状态 */
  @ApiProperty({ description: '帖子状态', enum: ContentsStatusEnum })
  @IsOptional()
  @IsString({ message: '帖子状态必须是字符串' })
  status: ContentsStatusEnum;
}
