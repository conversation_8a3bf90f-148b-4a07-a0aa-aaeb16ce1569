/**
 * 发布帖子接口
 * @param body - 要发布的帖子数据
 * @returns 返回API响应数据
 */
const publishPost = async (body: PostsUploadInfo) => {
  try {
    const { data } = await useRequest<ApiResponse<GetSelfPostsInfo>>(
      '/contents',
      {
        method: 'POST',
        body,
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取当前用户自己发布的帖子信息
 * @returns 返回当前用户的帖子信息
 */
const getMyPostsInfo = async () => {
  try {
    const { data } =
      await useRequest<ApiResponse<GetSelfPostsInfo>>('/contents');
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 更新当前用户发布的帖子信息
 * @param body - 包含更新内容的帖子数据
 * @returns 返回更新后的帖子信息
 */
const updateMyPost = async (body: PostsUploadInfo) => {
  try {
    const { data } = await useRequest<ApiResponse<GetSelfPostsInfo>>(
      '/contents',
      {
        method: 'PUT',
        body,
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 删除当前用户发布的帖子
 * @returns 返回删除操作的结果
 */
const deleteMyPost = async () => {
  try {
    const { data } = await useRequest('/contents', {
      method: 'DELETE',
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取公开帖子列表
 * @param query - 可选查询参数，用于筛选和排序
 * @returns 返回帖子列表数据
 */
const getPostList = async (query?: PostsListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest<
      ApiResponse<GetListTemplate<PostsList, PostsSortField>>
    >('/contents/public/all', {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序排列
        ...query, // 合并用户自定义查询参数
      },
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取其他用户发布的帖子详情
 * @param id - 目标帖子的ID
 * @returns 返回指定帖子的详细信息
 */
const getOtherPostInfo = async (id: string) => {
  const authStore = useAuthStore(); // 获取认证状态
  try {
    const { data } = await useRequest<ApiResponse<GetOtherPostsInfo>>(
      '/contents/public/' + id,
      {
        token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户发布的帖子列表
 * @param uid - 目标用户的ID
 * @returns 返回该用户的帖子列表
 */
const getUserPostList = async (uid: string, query?: PostsListQuery) => {
  const authStore = useAuthStore(); // 获取认证状态
  try {
    const { data } = await useRequest<
      ApiResponse<GetListTemplate<PostsList, PostsSortField>>
    >('/contents/public/all/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 切换帖子的点赞状态
 * @param id - 目标帖子的ID
 */
const toggleLikePost = async (id: string) => {
  try {
    const { data } = await useRequest('/contents/like/' + id, {
      method: 'POST',
    });
  } catch (err) {
    throw err;
  }
};

/**
 * 切换帖子的收藏状态
 * @param id - 目标帖子的ID
 */
const toggleFavoritePost = async (id: string) => {
  try {
    const { data } = await useRequest('/contents/favorite/' + id, {
      method: 'POST',
    });
  } catch (err) {
    throw err;
  }
};

/**
 * 帖子相关API的组合函数
 * @returns 返回包含所有帖子API方法的对象，每个方法都添加了节流控制(500ms)
 */
export const usePostsApi = () => {
  return {
    /** 发布帖子 */
    publishPost: useThrottleFn(publishPost, 500),
    /** 获取自己发布的帖子信息 */
    getMyPostsInfo: useThrottleFn(getMyPostsInfo, 500),
    /** 更新自己发布的帖子信息 */
    updateMyPost: useThrottleFn(updateMyPost, 500),
    /** 删除自己发布的帖子 */
    deleteMyPost: useThrottleFn(deleteMyPost, 500),
    /** 获取帖子列表 */
    getPostList: useThrottleFn(getPostList, 500),
    /** 获取帖子详情 */
    getOtherPostInfo: useThrottleFn(getOtherPostInfo, 500),
    /** 获取指定用户的帖子列表 */
    getUserPostList: useThrottleFn(getUserPostList, 500),
    /** 切换帖子的点赞状态 */
    toggleLikePost: useThrottleFn(toggleLikePost, 500),
    /** 切换帖子的收藏状态 */
    toggleFavoritePost: useThrottleFn(toggleFavoritePost, 500),
  };
};

