import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsPhoneNumber, IsString, Length } from 'class-validator';

export class UpdatePhoneDto {
  /** 旧手机号 */
  @ApiProperty({ description: '旧手机号', example: '13800138000' })
  @IsNotEmpty({ message: '旧手机号不能为空' })
  @IsString({ message: '手机号必须为字符串' })
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  oldPhone: string;

  /** 新手机号 */
  @ApiProperty({ description: '新手机号', example: '13800138000' })
  @IsNotEmpty({ message: '手机号不能为空' })
  @IsString({ message: '手机号必须为字符串' })
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  phone: string;

  /** 验证码 */
  @ApiProperty({ description: '验证码', example: '123456' })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码必须为字符串' })
  @Length(6, 6, { message: '验证码长度必须为6位' })
  code: string;
}
