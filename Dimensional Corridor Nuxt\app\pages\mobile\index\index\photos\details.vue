<template>
  <div class="page-container">
    <Head>
      <Title>{{ photoDetail?.filename || '图片详情' }}</Title>
      <Meta
        name="keywords"
        :content="
          photoDetail?.tags.join(',') +
          ',' +
          photoDetail?.category +
          ',' +
          ',次元回廊,Dimensional Corridor,二次元,图片分享,交流平台,交友,壁纸'
        "
      />
    </Head>
    <MoblieNavBar>图片详情</MoblieNavBar>

    <main class="main-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <p>加载失败，请重试</p>
        <button @click="loadPhotoDetail" class="retry-btn">重试</button>
      </div>

      <!-- 图片详情内容 -->
      <div v-else-if="photoDetail" class="photo-detail-content">
        <!-- 图片展示区 -->
        <div class="image-section">
          <div
            class="image-container"
            @click="openFullscreen"
            @dblclick="handleDoubleTap"
            @touchstart="handleTouchStart"
            @touchend="handleTouchEnd"
          >
            <img
              :src="photoDetail.url + '?width=800'"
              :alt="photoDetail.filename"
              class="main-image"
              :class="{ loaded: imageLoaded }"
              @load="onImageLoad"
              @error="onImageError"
            />
            <!-- 双击点赞动画 -->
            <div v-if="showLikeAnimation" class="like-animation">
              <IconSvg name="like" size="3rem" :color="ICON_COLORS.like" />
            </div>

            <!-- 图片操作按钮 -->
            <div class="image-actions">
              <div
                class="action-button"
                @click.stop="downloadImage"
                :class="{ loading: downloadLoading }"
              >
                <IconSvg
                  name="download"
                  :size="20"
                  :color="ICON_COLORS.white"
                />
              </div>
              <div
                class="action-button"
                @click.stop="toggleFavorite"
                :disabled="actionLoading"
              >
                <IconSvg
                  name="favorite"
                  :size="20"
                  :color="
                    photoDetail.isFavorited
                      ? ICON_COLORS.favorite
                      : ICON_COLORS.inactive
                  "
                />
              </div>
              <div
                class="action-button"
                @click.stop="toggleLike"
                :disabled="actionLoading"
              >
                <IconSvg
                  name="like"
                  :size="20"
                  :color="
                    photoDetail.isLiked
                      ? ICON_COLORS.like
                      : ICON_COLORS.inactive
                  "
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 用户信息区 -->
        <div class="user-section">
          <div class="user-info" @click="goToUserProfile">
            <img
              :src="photoDetail.user.avatar"
              :alt="photoDetail.user.nickname"
              class="user-avatar"
            />
            <div class="user-details">
              <h3 class="user-nickname">{{ photoDetail.user.nickname }}</h3>
              <p class="user-id">ID: {{ photoDetail.user.uid }}</p>
            </div>
          </div>
        </div>

        <!-- 图片信息区 -->
        <div class="info-section">
          <div class="info-item">
            <IconSvg
              name="resolution"
              size="1rem"
              :color="ICON_COLORS.inactive"
            />
            <span
              >{{ photoDetail.attributes.width }} ×
              {{ photoDetail.attributes.height }}</span
            >
          </div>
          <div class="info-item">
            <IconSvg name="category" :size="16" :color="ICON_COLORS.inactive" />
            {{ photoDetail.category }}
          </div>
        </div>

        <!-- 统计信息区 -->
        <div class="stats-section">
          <div class="stat-item">
            <IconSvg name="like" size="1.2rem" :color="ICON_COLORS.inactive" />
            <span>{{ photoDetail.likeCount }}</span>
          </div>
          <div class="stat-item">
            <IconSvg
              name="favorite"
              size="1.2rem"
              :color="ICON_COLORS.inactive"
            />
            <span>{{ photoDetail.favoriteCount }}</span>
          </div>
          <div class="stat-item">
            <IconSvg
              name="download"
              size="1.2rem"
              :color="ICON_COLORS.inactive"
            />
            <span>{{ photoDetail.downloadCount }}</span>
          </div>
        </div>

        <!-- 标签区 -->
        <div
          class="tags-section"
          v-if="photoDetail.tags && photoDetail.tags.length > 0"
        >
          <h4>标签</h4>
          <div class="tags-container">
            <span
              v-for="(tag, index) in photoDetail.tags"
              :key="index"
              class="tag"
              @click="searchByTag(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 相关推荐区 -->
        <div class="related-section" v-if="photoDetail">
          <h4>相关推荐</h4>
          <div class="related-container">
            <RikkaImagesWaterfallGallery
              :items="relatedImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="relatedImagesLoading"
              :has-more="hasMoreRelatedImages"
              @load-more="handleLoadMoreRelatedImages"
              :height-limit="500"
            >
              <!-- 使用具名插槽自定义项目渲染 -->
              <template #item="{ items, columnWidth }">
                <!-- 这里可以完全自定义每个项目的渲染 -->
                <div v-for="item in items" :key="item.id" class="custom-item">
                  <img
                    :src="item.url"
                    :alt="item.filename"
                    :style="{ width: columnWidth + 'px' }"
                    loading="lazy"
                    @click="navigateToPhoto(item.id)"
                  />
                </div>
              </template>
            </RikkaImagesWaterfallGallery>
          </div>
        </div>
      </div>
    </main>

    <!-- 全屏预览 -->
    <div
      v-if="isFullscreen"
      class="fullscreen-overlay"
      @click="closeFullscreen"
    >
      <div class="fullscreen-container" @click.stop>
        <div class="fullscreen-header">
          <button @click="closeFullscreen" class="close-btn">×</button>
        </div>
        <div class="fullscreen-image-container">
          <img
            :src="photoDetail?.url + '?width=1600'"
            :alt="photoDetail?.filename"
            class="fullscreen-image"
          />
        </div>
        <div class="fullscreen-info">
          <p>{{ photoDetail?.filename }}</p>
          <p>
            {{ photoDetail?.attributes.width }} ×
            {{ photoDetail?.attributes.height }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 导入必要的组合式API和工具
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const { $device } = useNuxtApp();

// 获取图片ID
const id = route.query.id as string;

// 响应式数据
const photoDetail = ref<GetOtherPhotosInfo | null>(null);
const loading = ref(true);
const error = ref(false);
const imageLoaded = ref(false);
const isFullscreen = ref(false);
const actionLoading = ref(false);
const downloadLoading = ref(false);

// 双击点赞相关
const showLikeAnimation = ref(false);
const touchStartTime = ref(0);
const touchCount = ref(0);

// 相关推荐数据
const relatedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const relatedImagesLoading = ref(false);
const relatedImagesPaginated = ref<Paginated>({
  page: 0,
  pageSize: 20,
  totalPage: 0,
  totalCount: 0,
  sortField: 'downloadCount',
  sortOrder: 'desc',
});

// 统一的图标颜色常量
const ICON_COLORS = {
  like: '#FF5252',
  favorite: '#FFD700',
  inactive: 'var(--text-secondary)',
  white: '#FFFFFF',
};

// 计算属性
const hasMoreRelatedImages = computed(() => {
  return (
    relatedImagesPaginated.value.page < relatedImagesPaginated.value.totalPage
  );
});

// 生命周期钩子
onMounted(() => {
  if (id) {
    loadPhotoDetail();
  } else {
    error.value = true;
    loading.value = false;
  }
});

// 监听路由变化
watch(
  () => route.query.id,
  (newId) => {
    if (newId && newId !== id) {
      window.location.reload(); // 重新加载页面以获取新的图片详情
    }
  }
);

// 获取图片详情
const loadPhotoDetail = async () => {
  if (!id) {
    error.value = true;
    loading.value = false;
    useMessage({
      name: '参数错误',
      description: '缺少图片ID',
      type: 'error',
    });
    return;
  }

  loading.value = true;
  error.value = false;
  imageLoaded.value = false;

  try {
    const data = await useApi().getOtherPhotoDetail(id);
    photoDetail.value = data;
    // 加载相关推荐
    await loadRelatedImages();
  } catch (err) {
    console.error('图片详情', '获取图片详情失败', err);
    error.value = true;
    useMessage({
      name: '加载失败',
      description: '无法获取图片详情',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 图片加载完成
const onImageLoad = () => {
  imageLoaded.value = true;
};

// 图片加载错误
const onImageError = () => {
  useMessage({
    name: '图片加载失败',
    description: '无法显示图片',
    type: 'error',
  });
};

// 打开全屏预览
const openFullscreen = () => {
  isFullscreen.value = true;
  // 阻止背景滚动
  document.body.style.overflow = 'hidden';
};

// 关闭全屏预览
const closeFullscreen = () => {
  isFullscreen.value = false;
  // 恢复背景滚动
  document.body.style.overflow = '';
};

// 切换点赞状态
const toggleLike = async () => {
  if (!photoDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '点赞功能需要登录后使用',
      type: 'info',
    });
    router.push('/mobile/auth/login');
    return;
  }

  actionLoading.value = true;
  try {
    await useApi().toggleLikePhoto(photoDetail.value.id);
    // 更新本地状态
    photoDetail.value.isLiked = !photoDetail.value.isLiked;
    photoDetail.value.likeCount += photoDetail.value.isLiked ? 1 : -1;

    useMessage({
      name: photoDetail.value.isLiked ? '点赞成功' : '取消点赞',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    actionLoading.value = false;
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!photoDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '收藏功能需要登录后使用',
      type: 'info',
    });
    router.push('/mobile/auth/login');
    return;
  }

  actionLoading.value = true;
  try {
    await useApi().toggleFavoritePhoto(photoDetail.value.id);
    // 更新本地状态
    photoDetail.value.isFavorited = !photoDetail.value.isFavorited;
    photoDetail.value.favoriteCount += photoDetail.value.isFavorited ? 1 : -1;

    useMessage({
      name: photoDetail.value.isFavorited ? '收藏成功' : '取消收藏',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    actionLoading.value = false;
  }
};

// 下载图片
const downloadImage = async () => {
  if (!photoDetail.value) return;

  downloadLoading.value = true;
  try {
    // 开始加载动画
    useLoading().start({
      title: '下载中',
      description: '图片正在准备下载，请稍候...',
    });

    // 从服务器获取图片Blob数据
    const blob = await useApi().downloadPhoto(photoDetail.value.id);

    // 创建File对象
    const imageFile = new File(
      [blob],
      photoDetail.value.filename || 'image.png',
      { type: blob.type || 'image/png' }
    );

    // 使用工具函数下载图片到用户设备
    const { downloadImage: saveImage } = await import('@/utils/downloadImage');
    saveImage(imageFile);

    // 更新下载计数
    if (photoDetail.value.downloadCount !== undefined) {
      photoDetail.value.downloadCount += 1;
    }

    // 停止加载动画
    useLoading().stop();

    // 显示下载成功消息
    useMessage({
      name: '下载成功',
      description: '图片已保存到您的设备',
      type: 'success',
    });
  } catch (error) {
    useLoading().stop();
    useMessage({
      name: '下载失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    downloadLoading.value = false;
  }
};

// 跳转到用户主页
const goToUserProfile = () => {
  if (photoDetail.value?.user.uid) {
    router.push(`/mobile/user?uid=${photoDetail.value.user.uid}`);
  }
};

// 根据标签搜索
const searchByTag = (tag: string) => {
  router.push({
    path: '/mobile/search',
    query: { q: tag, type: 'photos' },
  });
};

/** 获取相关推荐图片列表 */
const getRelatedPhotosList = async (query?: PhotosListQuery) => {
  if (!photoDetail.value) return;

  try {
    const { list, ...res } = await useApi().getPhotosList({
      sortField: 'downloadCount',
      sortOrder: 'desc',
      tags: ['', '', ...(photoDetail.value.tags || [])],
      ...query,
    });

    // 过滤掉当前图片
    const filteredList = list.filter(
      (item) => item.id !== photoDetail.value?.id
    );

    relatedImagesArray.value.push(...filteredList);
    relatedImagesPaginated.value = res;
  } catch (err) {
    console.error('相关推荐', '获取相关推荐失败', err);
    throw err;
  }
};

/** 初始化相关推荐数据 */
const loadRelatedImages = async () => {
  if (!photoDetail.value) return;

  relatedImagesLoading.value = true;
  try {
    // 清空原有数据，重新加载
    relatedImagesArray.value = [];
    await getRelatedPhotosList({ page: 1, pageSize: 20 });
  } catch (err) {
    console.error('初始化相关推荐失败:', err);
  } finally {
    relatedImagesLoading.value = false;
  }
};

/** 加载更多相关推荐 */
const handleLoadMoreRelatedImages = async () => {
  if (relatedImagesLoading.value || !hasMoreRelatedImages.value) {
    return;
  }
  relatedImagesLoading.value = true;
  try {
    await getRelatedPhotosList({
      page: relatedImagesPaginated.value.page + 1,
      pageSize: 20,
    });
  } catch (err) {
    console.error('加载更多相关推荐失败:', err);
    useMessage({
      name: '加载失败',
      description: '无法加载更多推荐内容',
      type: 'error',
    });
  } finally {
    relatedImagesLoading.value = false;
  }
};

// 导航到其他图片
const navigateToPhoto = (photoId: string) => {
  router.push({
    path: '/mobile/photos/details',
    query: { id: photoId },
  });
};

// 双击点赞处理
const handleDoubleTap = async (event: Event) => {
  event.preventDefault();
  event.stopPropagation();

  if (!photoDetail.value) return;

  // 显示点赞动画（无论当前是否已点赞都显示动画）
  showLikeAnimation.value = true;
  setTimeout(() => {
    showLikeAnimation.value = false;
  }, 1000);

  // 只有在未点赞状态下才执行点赞API
  if (!photoDetail.value.isLiked) {
    await toggleLike();
  }
};

// 触摸开始处理
const handleTouchStart = () => {
  touchStartTime.value = Date.now();
  touchCount.value++;

  // 重置计数器
  setTimeout(() => {
    touchCount.value = 0;
  }, 300);
};

// 触摸结束处理
const handleTouchEnd = (event: TouchEvent) => {
  const touchEndTime = Date.now();
  const touchDuration = touchEndTime - touchStartTime.value;

  // 检测双击（两次快速触摸）
  if (touchCount.value >= 2 && touchDuration < 300) {
    handleDoubleTap(event);
  }
};

// 页面卸载时清理
onUnmounted(() => {
  // 确保恢复背景滚动
  document.body.style.overflow = '';
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);
}

.main-content {
  flex: 1;
  background: var(--background-elevated);
  padding: 0;
  overflow: auto;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 20rem;
  gap: 1rem;

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.2rem solid var(--background-secondary);
    border-top: 0.2rem solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 20rem;
  gap: 1rem;

  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .retry-btn {
    padding: 0.5rem 1rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
  }
}

// 图片详情内容
.photo-detail-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: linear-gradient(
    180deg,
    var(--background-elevated) 0%,
    var(--background-base) 100%
  );
}

// 图片展示区
.image-section {
  .image-container {
    position: relative;
    width: 100%;
    border-radius: 1.2rem;
    overflow: hidden;
    cursor: pointer;
    background: linear-gradient(
      135deg,
      var(--background-surface) 0%,
      var(--background-elevated) 100%
    );
    box-shadow:
      0 0.5rem 2rem rgba(0, 0, 0, 0.15),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05),
      var(--shadow-neon-primary);

    .main-image {
      width: 100%;
      height: auto;
      display: block;
      opacity: 0;
      border-radius: 0.8rem;
      transition: opacity 0.3s ease;

      &.loaded {
        opacity: 1;
      }
    }

    .image-overlay {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 1;
    }

    .like-animation {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10;
      animation: likeAnimation 1s ease-out forwards;
      pointer-events: none;
    }
  }
}

@keyframes likeAnimation {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// 用户信息区
.user-section {
  .user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1rem;
    cursor: pointer;
    box-shadow:
      0 0.25rem 1rem rgba(0, 0, 0, 0.1),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05);
    border-left: 0.25rem solid var(--interactive-primary);

    .user-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      object-fit: cover;
      border: 0.125rem solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
    }

    .user-details {
      flex: 1;

      .user-nickname {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: var(--text-primary);
      }

      .user-id {
        font-size: 0.8rem;
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }
}

// 图片信息区
.info-section {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 0.75rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
    box-shadow:
      0 0.125rem 0.5rem rgba(0, 0, 0, 0.08),
      0 0 0 0.05rem rgba(255, 255, 255, 0.05);
    border: 0.05rem solid rgba(255, 255, 255, 0.1);
  }
}

// 统计信息区
.stats-section {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1rem;
  gap: 2rem;
  box-shadow:
    0 0.25rem 1rem rgba(0, 0, 0, 0.1),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.05rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1rem 1rem 0 0;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;

    span {
      font-weight: 600;
    }
  }
}

// 图片操作按钮（参考PC端设计）
.image-actions {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
  border-radius: 2rem;
  box-shadow:
    0 0.5rem 2rem rgba(0, 0, 0, 0.4),
    0 0 0 0.1rem rgba(255, 255, 255, 0.1),
    var(--shadow-neon-primary);
  z-index: 10;
  border: 0.05rem solid rgba(255, 255, 255, 0.1);

  .action-button {
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 0.1rem solid rgba(255, 255, 255, 0.2);

    &:active {
      transform: scale(0.95);
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 标签区
.tags-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow:
    0 0.25rem 1rem rgba(0, 0, 0, 0.1),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);

  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
    padding-left: 1rem;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.25rem;
      height: 1rem;
      background: var(--interactive-primary);
      border-radius: 0.125rem;
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;

    .tag {
      padding: 0.5rem 1rem;
      background: linear-gradient(
        135deg,
        var(--background-floating) 0%,
        var(--background-surface) 100%
      );
      border-radius: 1.5rem;
      font-size: 0.8rem;
      color: var(--text-secondary);
      cursor: pointer;
      border: 0.05rem solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    }
  }
}

// 全屏预览
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .fullscreen-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    .fullscreen-header {
      position: absolute;
      top: 1rem;
      right: 1rem;
      z-index: 10;

      .close-btn {
        background: rgba(0, 0, 0, 0.5);
        border: none;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 2rem;
      }
    }

    .fullscreen-image-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 3rem 1rem 1rem;

      .fullscreen-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .fullscreen-info {
      position: absolute;
      bottom: 1rem;
      left: 1rem;
      right: 1rem;
      background: rgba(0, 0, 0, 0.7);
      padding: 0.75rem;
      border-radius: 0.5rem;
      color: white;
      font-size: 0.8rem;

      p {
        margin: 0.25rem 0;

        &:first-child {
          font-weight: 600;
        }
      }
    }
  }
}

// 相关推荐区
.related-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1.2rem;
  padding: 1.5rem;
  box-shadow:
    0 0.5rem 2rem rgba(0, 0, 0, 0.15),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.1rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1.2rem 1.2rem 0 0;
  }

  h4 {
    margin: 0 0 1.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
    padding-left: 1.5rem;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.3rem;
      height: 1.2rem;
      background: linear-gradient(
        180deg,
        var(--interactive-primary) 0%,
        var(--interactive-primary-hover) 100%
      );
      border-radius: 0.15rem;
      box-shadow: 0 0 0.5rem var(--interactive-primary-translucent);
    }

    &::after {
      content: '';
      position: absolute;
      left: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      width: 0.1rem;
      height: 0.8rem;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 0.05rem;
    }
  }

  .related-container {
    :deep(.waterfall-container) {
      padding: 0;
    }

    // 完全移植首页的样式并增强层次感
    .custom-item {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      > img {
        border-radius: 0.75rem;
        box-shadow:
          0 0.25rem 1rem rgba(0, 0, 0, 0.15),
          0 0 0 0.05rem rgba(255, 255, 255, 0.1),
          var(--shadow-neon-primary);
      }
    }
  }
}
</style>

