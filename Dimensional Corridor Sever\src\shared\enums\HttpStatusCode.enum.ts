/**
 * 常见 HTTP 状态码枚举
 * 参考 RFC 7231、RFC 7232、RFC 7233、RFC 7235 等规范
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Status MDN HTTP 状态码文档}
 */
export enum HttpStatusEnum {
  /**
   * 继续 - 服务器已接收到请求头，客户端应继续发送请求体
   * @value 100
   */
  CONTINUE = 100,

  /**
   * 切换协议 - 服务器要求客户端切换协议
   * @value 101
   */
  SWITCHING_PROTOCOLS = 101,

  /**
   * 处理中 - 服务器已收到请求正在处理，但尚未完成
   * @value 102
   */
  PROCESSING = 102,

  /**
   * 成功 - 标准成功响应
   * @value 200
   */
  OK = 200,

  /**
   * 已创建 - 请求成功并创建了新资源
   * @value 201
   */
  CREATED = 201,

  /**
   * 已接受 - 请求已被接受处理，但尚未完成
   * @value 202
   */
  ACCEPTED = 202,

  /**
   * 无内容 - 请求成功，但响应中没有内容
   * @value 204
   */
  NO_CONTENT = 204,

  /**
   * 重置内容 - 请求成功，客户端应重置文档视图
   * @value 205
   */
  RESET_CONTENT = 205,

  /**
   * 永久重定向 - 资源已永久移动到新位置
   * @value 301
   */
  MOVED_PERMANENTLY = 301,

  /**
   * 临时重定向 - 资源临时移动到新位置
   * @value 302
   */
  FOUND = 302,

  /**
   * 未修改 - 资源未更改，可使用缓存版本
   * @value 304
   */
  NOT_MODIFIED = 304,

  /**
   * 临时重定向 - 使用原有请求方法和请求体重定向
   * @value 307
   */
  TEMPORARY_REDIRECT = 307,

  /**
   * 永久重定向 - 使用新方法永久重定向
   * @value 308
   */
  PERMANENT_REDIRECT = 308,

  /**
   * 错误请求 - 请求语法无效或参数错误
   * @value 400
   */
  BAD_REQUEST = 400,

  /**
   * 未授权 - 需要身份验证
   * @value 401
   */
  UNAUTHORIZED = 401,

  /**
   * 禁止访问 - 服务器拒绝执行
   * @value 403
   */
  FORBIDDEN = 403,

  /**
   * 未找到 - 请求资源不存在
   * @value 404
   */
  NOT_FOUND = 404,

  /**
   * 方法不允许 - 请求方法不支持
   * @value 405
   */
  METHOD_NOT_ALLOWED = 405,

  /**
   * 请求超时 - 服务器等待请求超时
   * @value 408
   */
  REQUEST_TIMEOUT = 408,

  /**
   * 冲突 - 请求与当前资源状态冲突
   * @value 409
   */
  CONFLICT = 409,

  /**
   * 请求实体过大 - 请求数据超过服务器限制
   * @value 413
   */
  PAYLOAD_TOO_LARGE = 413,

  /**
   * 不支持的媒体类型 - 服务器不支持请求的格式
   * @value 415
   */
  UNSUPPORTED_MEDIA_TYPE = 415,

  /**
   * 请求次数过多 - 客户端发送过多请求
   * @value 429
   */
  TOO_MANY_REQUESTS = 429,

  /**
   * 服务器错误 - 通用服务器错误
   * @value 500
   */
  INTERNAL_SERVER_ERROR = 500,

  /**
   * 未实现 - 服务器不支持请求的功能
   * @value 501
   */
  NOT_IMPLEMENTED = 501,

  /**
   * 网关错误 - 上游服务器返回无效响应
   * @value 502
   */
  BAD_GATEWAY = 502,

  /**
   * 服务不可用 - 服务器暂时过载或维护
   * @value 503
   */
  SERVICE_UNAVAILABLE = 503,

  /**
   * 网关超时 - 上游服务器响应超时
   * @value 504
   */
  GATEWAY_TIMEOUT = 504,
}
