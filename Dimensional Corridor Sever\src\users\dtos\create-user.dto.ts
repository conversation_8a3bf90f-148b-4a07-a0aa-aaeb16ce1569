import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  Length,
  IsNotEmpty,
  IsStrongPassword,
  IsEmail,
  IsPhoneNumber,
  Validate,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  IsOptional,
} from 'class-validator';

// 自定义验证约束
@ValidatorConstraint({ name: 'atLeastOneContact', async: false })
export class AtLeastOneContactConstraint
  implements ValidatorConstraintInterface
{
  validate(value: any, args: ValidationArguments) {
    const object = args.object as CreateUserDto;
    return !!(object.email || object.phone);
  }

  defaultMessage(args: ValidationArguments) {
    return args.value || '邮箱和手机号必须至少提供一个';
  }
}

export class CreateUserDto {
  /** 邮箱 */
  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsOptional()
  @IsString({ message: '邮箱必须为字符串' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  email?: string;

  /** 手机号 */
  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsOptional()
  @IsString({ message: '手机号必须为字符串' })
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  phone?: string;

  /** 用户密码 */
  @ApiProperty({ description: '用户密码', required: true, example: '66666666' })
  @IsNotEmpty({ message: '密码不能为空' })
  @Length(8, 20, { message: '密码长度必须在8-20位之间' })
  @IsStrongPassword(
    {
      minLowercase: 1,
      minUppercase: 1,
      minNumbers: 1,
      minSymbols: 1,
    },
    {
      message: '密码必须包含大小写字母、数字和特殊符号',
    }
  )
  password: string;

  /** 验证码 */
  @ApiProperty({ description: '验证码', required: true, example: '123456' })
  @IsNotEmpty({ message: '验证码不能为空' })
  @Length(6, 6, { message: '验证码长度必须为6位' })
  code: string;

  // 应用自定义验证
  @Validate(AtLeastOneContactConstraint)
  requireAtLeastOneContact: boolean;
}
