import { HttpException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt'; // 引入 JwtService 用于 JWT 处理
import { RedisService } from 'src/redis/redis.service';
import { randomBytes } from 'crypto';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { CaptchaService } from 'src/captcha/captcha.service';
import { PasswordUtil } from 'src/common/utils';
import { Request } from 'express';
import { UsersService } from 'src/users/service/users.service';

@Injectable()
export class AuthService {
  constructor(
    private userService: UsersService, // 注入 UserService
    private jwtService: JwtService, // 注入 JwtService
    private readonly redisService: RedisService, // 注入 RedisService
    private readonly captchaService: CaptchaService // 注入验证码服务
  ) {}

  /**
   * !: 登录成功返回双token
   * @param payload JWT 令牌的负载
   * @returns {Promise<any>} 包含 refresh_token 和 access_token 的对象
   * @description 该接口用于登录成功后返回双 token，其中 refresh_token 用于刷新 JWT 令牌，access_token 用于访问受保护资源
   */
  async loginSuccess(payload: JwtPayload) {
    // 生成 Refresh Token（Redis）
    const refreshToken = randomBytes(64).toString('hex'); // 生成高强度随机字符串

    await this.userService.loginSuccess(payload.id); // 登录成功，更新用户登录信息

    // 记录用户在线信息
    this.redisService.set(`online:${payload.id}`, 'active', 300);

    // 设置 Redis 缓存，缓存令牌信息，过期时间为 7 天
    this.redisService.set(
      `refresh_token:${refreshToken}`,
      JSON.stringify(payload),
      60 * 60 * 24 * 7
    ); // 设置 Redis 缓存，过期时间为 7 天

    // 使用 JwtService 签发 JWT 令牌，并返回包含令牌的对象
    return {
      refresh_token: refreshToken,
      access_token: await this.jwtService.signAsync(payload),
    };
  }

  /**
   * !: 刷新 JWT 令牌
   * @param refresh_token 刷新令牌
   * @param options 验证所需负载
   * @returns {Promise<any>} 包含 refresh_token 和 access_token 的对象
   * @description 该接口用于刷新 JWT 令牌，包含新的 access_token 和 refresh_token
   */
  async refreshToken(
    refresh_token: string,
    options: {
      ip: string;
      deviceInfo: string;
    }
  ) {
    // 尝试从 Redis 中获取 Refresh Token 对应的 JWT 令牌的负载
    const payloadString = await this.redisService.get(
      `refresh_token:${refresh_token}`
    );
    console.log(payloadString);

    if (!payloadString) {
      // 如果 Refresh Token 无效，抛出未授权异常
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }

    // 解析 JWT 令牌的负载
    const payload = JSON.parse(payloadString) as JwtPayload;

    if (
      payload.deviceInfo !== options.deviceInfo ||
      payload.ip !== options.ip
    ) {
      // 设备信息或 IP 地址不匹配可能为 Token 盗用
      this.redisService.del(`refresh_token:${refresh_token}`); // 立即失效旧 Token
      throw new HttpException('Token 已失效', HttpStatusEnum.UNAUTHORIZED);
    }

    //  根据负载中的用户 ID 获取用户信息
    const user = await this.userService.findOneById(payload.id);
    if (!user) {
      // 用户不存在
      this.redisService.del(`refresh_token:${refresh_token}`); // 立即失效旧 Token
      throw new HttpException('用户不存在', HttpStatusEnum.UNAUTHORIZED);
    }

    // 重新生成 Refresh Token（Redis）
    const refreshToken = randomBytes(64).toString('hex'); // 生成高强度随机字符串

    // 设置 Redis 缓存，缓存令牌信息，过期时间为 7 天
    this.redisService.set(
      `refresh_token:${refreshToken}`,
      JSON.stringify(payload),
      60 * 60 * 24 * 7
    ); // 设置 Redis 缓存，过期时间为 7 天

    // 使用 JwtService 签发新的 JWT 令牌，并返回包含令牌的对象
    return {
      refresh_token: refreshToken,
      access_token: await this.jwtService.signAsync(payload),
    };
  }

  /**
   * 用户登录
   * @param identifier 用户邮箱或手机号
   * @param password 用户密码
   * @param validator 验证码信息
   * @param req 请求信息
   * @returns {Promise<any>} 包含 access_token 的对象
   * @description 该接口用于用户邮箱或手机号登录，包含 access_token
   */
  async loginByPassword(
    identifier: string,
    password: string,
    validator: { token: string; code: string },
    req: Request
  ) {
    // 验证码验证
    await this.captchaService.verifyCaptcha(validator.token, validator.code);

    // 获取指定邮箱或手机号的用户
    // 手机号格式验证
    const phoneReg = /^1[3-9]\d{9}$/;
    const user = phoneReg.test(identifier)
      ? await this.userService.findOneByPhone(identifier)
      : await this.userService.findOneByEmail(identifier);

    if (!user) {
      // 用户不存在
      throw new HttpException('用户不存在', HttpStatusEnum.UNAUTHORIZED);
    }

    // 密码验证
    if (!(await PasswordUtil.compare(password, user.password))) {
      // 密码错误
      await this.userService.increaseLoginError(user.id);
    }

    // 登录成功，生成 JWT 令牌并返回
    const payload = {
      id: user.id,
      uid: user.uid,
      role: user.role,
      deviceInfo: req.headers['user-agent'] || '',
      ip: req.ip || '',
    };

    return await this.loginSuccess(payload);
  }

  /**
   * !: 退出登录的异步方法
   * @param id 用户 ID
   * @returns {boolean} 退出登录成功返回 true
   */
  async logout(refresh_token: string, id: string) {
    // 尝试从 Redis 中获取 Refresh Token 对应的 JWT 令牌的负载
    const payloadString = await this.redisService.get(
      `refresh_token:${refresh_token}`
    );

    if (!payloadString) {
      // 如果 Refresh Token 无效，抛出未授权异常
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }

    // 清除redis缓存
    await this.redisService.del(`refresh_token:${refresh_token}`);
    this.redisService.del(`online:${id}`);
    return true;
  }

  /**
   * !: 获取微信扫码登录链接
   * @returns {string} 微信扫码登录链接
   */
  getWechatAuthUrl() {
    // TODO: 实现微信扫码登录链接获取
  }

  /**
   * !: 处理微信回调
   * @returns {Promise<void>}
   */
  async handleWechatCallback() {
    // TODO: 处理微信回调
  }
}
