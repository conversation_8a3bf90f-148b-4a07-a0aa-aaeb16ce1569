/**
 * 消息提示项的类型定义
 * @property {string} name - 消息标题
 * @property {string} description - 消息描述内容
 * @property {string} time - 消息显示时间
 * @property {string} icon - 消息图标URL或图标名称
 * @property {string} color - 消息颜色值
 */
interface Message {
  name: string;
  description: string;
  time: string;
  icon: string;
  color: string;
}

/**
 * 设置消息提示的函数参数类型
 * @property {string} name - 消息标题
 * @property {string} description - 消息详细描述
 * @property {string} [time] - 可选，消息显示时间，默认为当前时间
 * @property {'success' | 'warning' | 'info' | 'error'} type - 消息类型，决定样式和图标
 */
interface setMessage {
  name: string;
  description: string;
  time?: string;
  type: 'success' | 'warning' | 'info' | 'error';
}
