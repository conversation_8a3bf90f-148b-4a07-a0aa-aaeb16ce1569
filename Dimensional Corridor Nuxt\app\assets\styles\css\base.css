/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* a标签样式重置 */
a {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

/* 列表元素重置 */
li {
  list-style: none;
}

/* 图片元素重置 */
img {
  width: 100%;
  display: block;
}

svg {
  display: block;
}

/* 表单元素重置 */
input,
textarea,
select,
button {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  background: none;
  font: inherit;
}

/* 表格元素重置 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 去除默认的字体样式 */
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
dl,
dd,
dt {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 按钮与禁用状态按钮的样式重置 */
button {
  cursor: pointer;
}

button:disabled {
  cursor: default;
}

/* 重置表单元素的默认边框和填充 */
fieldset,
legend {
  border: 0;
  padding: 0;
  margin: 0;
}

/* 去除默认的边框和填充 */
textarea {
  resize: none; /* 禁止用户调整大小 */
}

/* 确保视频和iframe元素的默认边框被去除 */
video,
iframe {
  border: 0;
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 确保一些常用元素在不同浏览器中的一致性 */
html,
body,
#__nuxt,
.Page {
  height: 100%;
  /* 防止用户选中文本 */
  /* user-select: none; */
}
