// 导入NitroFetch相关类型
import type { NitroFetchRequest, NitroFetchOptions } from 'nitropack';

// 状态管理相关
// 标记是否正在刷新Token
let isRefreshing = false;
// 存储等待Token刷新的请求回调函数
const refreshSubscribers: ((token: string) => void)[] = [];

// 自定义请求类型定义
export type MyFetchRequest = NitroFetchRequest;

// 自定义请求选项类型，扩展NitroFetchOptions
export type MyFetchOptions = NitroFetchOptions<
  NitroFetchRequest,
  | 'get'
  | 'post'
  | 'put'
  | 'delete'
  | 'head'
  | 'patch'
  | 'connect'
  | 'options'
  | 'trace'
> & {
  token?: boolean; // 是否携带认证Token
  server?: boolean; // 是否允许在服务端执行
};

// 定义Nuxt插件
export default defineNuxtPlugin((nuxtApp) => {
  // 获取认证状态存储实例
  const authStore = useAuthStore();
  // 获取运行时配置
  const config = useRuntimeConfig();
  // 获取消息提示组件
  const { $message } = useNuxtApp();

  // 核心请求方法
  const useRequest = <T = any>(
    url: MyFetchRequest,
    options: MyFetchOptions
  ): Promise<T> => {
    // 获取基础URL
    const baseUrl = config.public.baseUrl;
    // 确定是否使用Token
    const token = options?.token === false ? false : true;
    // 确定是否允许服务端请求
    const server = options?.server === false ? false : true;

    // 服务器端请求处理
    let cookies = {};

    if (import.meta.server) {
      // 获取请求头中的cookie
      cookies = useRequestHeaders(['cookie']);
      if (!server) {
        return Promise.reject(new Error('禁止在服务端请求'));
      }
    }

    // 合并基础配置
    const mergedOptions: MyFetchOptions = {
      ...options,
      credentials: 'include', // 包含凭据
      headers: {
        'x-api-key': config.public.ApiKey, // 添加API密钥
        ...cookies, // 合并cookie
      },
    };

    // 动态注入Token
    if (token) {
      // 检查是否已登录
      if (!authStore.getIsLogin()) {
        return Promise.reject(new Error('请先登录'));
      }
      // 添加Authorization头
      mergedOptions.headers = {
        ...mergedOptions.headers,
        Authorization: `Bearer ${authStore.getToken()}`,
      };
    }

    // 统一错误处理器
    const handleError = (error: any) => {
      // 获取错误状态码和错误数据
      const status = error.response?.status;
      const errorData = error.response?._data;

      // 错误码映射表
      const errorMap: Record<number, { name: string; description: string }> = {
        400: {
          name: '请求错误',
          description: errorData?.message || '请检查请求参数',
        },
        403: {
          name: '禁止访问',
          description: errorData?.message || '权限不足，请联系管理员',
        },
        404: {
          name: '资源不存在',
          description: errorData?.message || '请求地址或资源未找到',
        },
        429: {
          name: '请求频率过高',
          description: '请稍后再试',
        },
        500: {
          name: '服务器错误',
          description: '服务器内部错误，请联系管理员',
        },
      };

      // 特殊处理401错误（Token过期）
      if (status === 401) {
        return handle401(error);
      }

      // 根据错误码显示对应错误消息
      if (errorMap[status]) {
        $message({ ...errorMap[status], type: 'error' });
      } else {
        // 未知错误处理
        $message({
          name: `未知错误 (${status || '请联系管理员'})`,
          description: errorData?.message || '发生未预期的错误',
          type: 'error',
        });
      }

      return Promise.reject(error); // 继续抛出错误供外部捕获
    };

    // 核心请求逻辑
    const makeRequest = async (): Promise<T> => {
      try {
        // 使用$fetch发起请求
        return await $fetch<T>(url, {
          baseURL: baseUrl,
          ...mergedOptions,
          retry: 0, // 禁用自动重试
          timeout: 10000, // 10秒超时
          async onRequest({ options }) {
            // GET请求添加时间戳防止缓存
            if (mergedOptions.method?.toUpperCase() === 'GET') {
              options.query = { ...options.query, _t: Date.now() };
            }
          },
        });
      } catch (error) {
        return handleError(error); // 捕获并处理错误
      }
    };

    // 处理401 Token刷新
    const handle401 = async (error: any): Promise<T> => {
      // 先检查用户是否登录
      if (!authStore.getIsLogin()) {
        return Promise.reject(error);
      }
      if (!isRefreshing) {
        isRefreshing = true;
        try {
          // 调用刷新Token接口
          const { data } = await $fetch<ApiResponse<string>>(
            '/auth/refresh-token',
            {
              method: 'GET',
              baseURL: baseUrl,
              credentials: 'include',
              headers: {
                'x-api-key': config.public.ApiKey,
                ...cookies,
              },
            }
          );

          // 更新Token状态
          authStore.setToken(data);
          isRefreshing = false;
          // 执行所有等待中的回调
          refreshSubscribers.forEach((cb) => cb(data));
          refreshSubscribers.splice(0, refreshSubscribers.length);

          // 更新Header后重试请求
          mergedOptions.headers = {
            ...mergedOptions.headers,
            Authorization: `Bearer ${data}`,
          };
          return makeRequest();
        } catch (refreshError) {
          isRefreshing = false;
          // 刷新失败清除认证状态
          authStore.clear();
          return Promise.reject(refreshError);
        }
      } else {
        // 如果已经在刷新Token，将当前请求加入等待队列
        return new Promise((resolve, reject) => {
          // 设置30秒超时
          const timeoutId = setTimeout(() => {
            reject(new Error('Token刷新超时'));
          }, 30000);
          // 添加回调函数到队列
          refreshSubscribers.push((newToken) => {
            clearTimeout(timeoutId);
            // 使用新Token更新请求头
            mergedOptions.headers = {
              ...mergedOptions.headers,
              Authorization: `Bearer ${newToken}`,
            };
            // 重试请求
            makeRequest().then(resolve).catch(reject);
          });
        });
      }
    };

    // 主请求流程
    return makeRequest()
      .then((res) => {
        if (import.meta.dev) {
          console.log('请求成功拦截', res);
        }
        return res;
      })
      .catch((error) => {
        return handleError(error); // 处理错误
      });
  };

  // 将请求方法提供给Nuxt应用
  nuxtApp.provide('request', useRequest);
});
