<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-图片发布</Title>
    </Head>
    <MoblieNavBar>
      发布图片
      <template #right>
        <RippleButton
          @click="publishPhoto"
          class="publish-btn"
          :disabled="!isPublishable"
        >
          发布
        </RippleButton>
      </template>
    </MoblieNavBar>
    <main class="main-content">
      <!-- 图片上传区域 -->
      <div class="upload-section">
        <RikkaImageUpload
          ref="imageUpload"
          :multiple="false"
          @file-upload="handleFileUpload"
          @file-removed="clearFiles"
          @file-selected="handleFileSelected"
        />
      </div>

      <!-- 发布表单区域 -->
      <div v-if="selectedFile" class="publish-form">
        <div class="form-section">
          <div class="form-item">
            <label class="form-label">分类</label>
            <RikkaSelect
              v-model="publishParams.category"
              :options="categories"
              placeholder="请选择分类"
            />
          </div>

          <div class="form-item">
            <label class="form-label">文件名</label>
            <RikkaInput
              v-model="publishParams.filename"
              :maxLength="30"
              placeholder="请输入文件名"
            />
          </div>

          <div class="form-item">
            <label class="form-label">标签</label>
            <RikkaTagInput
              v-model="publishParams.tags"
              :max-tags="10"
              :max-tag-length="10"
              placeholder="请输入标签"
            />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
// 图片上传组件实例
const imageUpload = ref<ImageUploadExpose | null>(null);

// 上传的文件
const selectedFile = ref<File | null>(null);

// 发布参数
const publishParams = ref<{
  category: PhotoCategory;
  tags: Array<string>;
  filename: string;
}>({
  category: '其他',
  tags: [],
  filename: '',
});

// 分类选项
const categories: Array<{ label: string; value: PhotoCategory }> = [
  { label: '美女', value: '美女' },
  { label: '动漫', value: '动漫' },
  { label: '城市', value: '城市' },
  { label: '风景', value: '风景' },
  { label: '二次元', value: '二次元' },
  { label: '美食', value: '美食' },
  { label: '其他', value: '其他' },
];

// 发布按钮是否可用
const isPublishable = computed(() => {
  return (
    selectedFile.value &&
    publishParams.value.category &&
    publishParams.value.tags.length > 0 &&
    publishParams.value.tags.length <= 10 &&
    publishParams.value.filename.length > 0
  );
});

/**
 * 处理上传文件变化事件
 */
const handleFileUpload = (files: File[]) => {
  if (files && files.length > 0) {
    const file = files[0];
    if (file) {
      selectedFile.value = file; // 只取第一个文件

      // 自动设置文件名（去掉扩展名）
      const filename = file.name.replace(/\.[^/.]+$/, '');
      publishParams.value.filename = filename;
    }
  }
};

/**
 * 处理文件选中事件
 */
const handleFileSelected = (file: File | null, _index: number) => {
  selectedFile.value = file;
};

/**
 * 清空文件
 */
const clearFiles = () => {
  selectedFile.value = null;
  clearPublishParams();
};

/**
 * 清空发布参数
 */
const clearPublishParams = () => {
  publishParams.value = {
    category: '其他',
    tags: [],
    filename: '',
  };
};

/**
 * 发布图片
 */
const publishPhoto = async () => {
  if (!selectedFile.value) return;

  try {
    // 开始加载动画
    useLoading().start({
      title: '发布中',
      description: '图片正在发布中，请稍候...',
    });

    // 上传图片
    const data = await usePhotosApi().uploadPhoto(selectedFile.value);

    // 如果上传失败，结束加载动画并返回
    if (!data || !data[0]) {
      useLoading().stop();
      return;
    }

    // 发布图片
    await usePhotosApi().publishPhoto({
      ...data[0],
      ...publishParams.value,
      status: 'approved',
    });

    // 结束加载动画
    useLoading().stop();

    // 显示发布成功的消息
    useMessage({
      name: '发布成功',
      description: '图片已发布',
      type: 'success',
    });

    // 清空文件和参数
    clearFiles();

    // 可选：返回上一页或跳转到其他页面
    // navigateTo('/mobile/mine');
  } catch (err) {
    // 发生错误时停止加载动画
    useLoading().stop();

    // 显示发布失败的消息
    useMessage({
      name: '发布失败',
      description: '图片发布失败，请稍后再试',
      type: 'error',
    });
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);
}

.main-content {
  flex: 1;
  background: var(--background-elevated);
  overflow: auto;
  padding: 1rem;
}

.publish-btn {
  background-color: var(--button-primary);
  color: var(--text-primary);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 1.4rem;

  &:disabled {
    background-color: var(--button-cancel);
    opacity: 0.6;
  }
}

.upload-section {
  margin-bottom: 2rem;
}

.publish-form {
  margin-top: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 1.4rem;
  font-weight: 500;
  color: var(--text-primary);
}
</style>

