在 NestJS 项目中实现邮件发送功能，可以通过 `nodemailer` 或封装好的 `@nestjs-modules/mailer` 模块来实现。以下是完整步骤：

---

### **步骤 1：安装依赖**

```bash
npm install @nestjs-modules/mailer nodemailer handlebars  # 使用 Handlebars 模板引擎
# 或
npm install @nestjs-modules/mailer nodemailer ejs         # 使用 EJS 模板引擎
```

---

### **步骤 2：配置邮件模块**

创建 `mail` 模块，并配置 SMTP 服务（以 Gmail 为例）：

```typescript
// src/mail/mail.module.ts
import { MailerModule } from '@nestjs-modules/mailer'
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter'
import { Module } from '@nestjs/common'
import { join } from 'path'

@Module({
  imports: [
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com', // SMTP 服务器地址
        port: 587,
        secure: false, // true for 465, false for other ports
        auth: {
          user: '<EMAIL>', // 发件人邮箱
          pass: 'your-app-password', // 邮箱密码或应用专用密码（Gmail需配置）
        },
      },
      defaults: {
        from: '"No Reply" <<EMAIL>>', // 默认发件人
      },
      template: {
        dir: join(__dirname, 'templates'), // 模板文件目录
        adapter: new HandlebarsAdapter(), // 使用 Handlebars
        options: {
          strict: true,
        },
      },
    }),
  ],
  providers: [],
  exports: [MailerModule],
})
export class MailModule {}
```

---

### **步骤 3：创建邮件服务**

```typescript
// src/mail/mail.service.ts
import { Injectable } from '@nestjs/common'
import { MailerService } from '@nestjs-modules/mailer'

@Injectable()
export class MailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendVerificationEmail(email: string, name: string, token: string) {
    const url = `https://your-domain.com/verify?token=${token}`

    await this.mailerService.sendMail({
      to: email,
      subject: '欢迎注册！请验证您的邮箱',
      template: 'verification', // 对应模板文件名（如 verification.hbs）
      context: {
        name,
        url,
      },
    })
  }
}
```

---

### **步骤 4：创建邮件模板**

在 `src/mail/templates` 目录下创建模板文件 `verification.hbs`：

```html
<!-- verification.hbs -->
<div>
  <p>Hi {{name}},</p>
  <p>感谢您的注册！请点击下方链接验证邮箱：</p>
  <a href="{{url}}">点击验证</a>
  <p>如果无法点击，请复制以下链接到浏览器：</p>
  <p>{{url}}</p>
</div>
```

---

### **步骤 5：在业务逻辑中调用邮件服务**

例如，在用户注册成功后发送验证邮件：

```typescript
// src/auth/auth.service.ts
import { Injectable } from '@nestjs/common'
import { MailService } from '../mail/mail.service'

@Injectable()
export class AuthService {
  constructor(private readonly mailService: MailService) {}

  async register(userData: any) {
    // 1. 创建用户逻辑...
    // 2. 发送验证邮件
    await this.mailService.sendVerificationEmail(userData.email, userData.name, verificationToken)
  }
}
```

---

### **步骤 6：配置 Gmail 安全设置（可选）**

如果使用 Gmail，需完成以下操作：

1. 启用 **两步验证**（[Google Account Settings](https://myaccount.google.com/security)）。
2. 生成 **应用专用密码**（用于 `auth.pass` 配置）。

---

### **步骤 7：测试邮件发送**

```typescript
// 直接测试邮件服务
async function testEmail() {
  await mailService.sendVerificationEmail('<EMAIL>', 'Test User', 'fake-token-123')
}
testEmail()
```

---

### **扩展：使用环境变量**

避免硬编码敏感信息，改用 `.env` 文件：

```env
# .env
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-password
```

配置模块时读取环境变量：

```typescript
// mail.module.ts
transport: {
  host: process.env.MAIL_HOST,
  port: parseInt(process.env.MAIL_PORT),
  auth: {
    user: process.env.MAIL_USER,
    pass: process.env.MAIL_PASSWORD,
  },
},
```

---

### **常见问题解决**

1. **认证失败**

   - 检查邮箱密码是否正确（Gmail 需使用应用专用密码）。
   - 确保开启 SMTP 权限。

2. **邮件进入垃圾箱**

   - 配置 SPF/DKIM 记录。
   - 使用专业邮件服务（如 SendGrid、Mailgun）。

3. **模板路径错误**
   - 确保 `dir` 路径指向正确的模板目录。

---

通过以上步骤，你可以在 NestJS 中快速集成邮件发送功能，并根据需求扩展模板和邮件类型。
