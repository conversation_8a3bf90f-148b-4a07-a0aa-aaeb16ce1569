<template>
  <!-- 消息队列 -->
  <div :class="cn('flex flex-col items-center gap-4', $props.class)">
    <transition-group
      name="list"
      tag="div"
      class="flex flex-col-reverse items-center gap-3"
      move-class="move"
    >
      <!-- Only render the items up to the current index -->
      <Motion
        v-for="data in itemsToShow"
        :key="data.id"
        as="div"
        :initial="{ scale: 0, opacity: 0 }"
        :animate="{
          scale: 1,
          opacity: 1,
          y: 0,
        }"
        :exit="{
          scale: 0,
          opacity: 0,
          y: 0,
        }"
        :transition="{
          type: 'spring',
          stiffness: 350,
          damping: 40,
        }"
        :class="cn('mx-auto w-full')"
      >
        <component :is="data.node" />
      </Motion>
    </transition-group>
  </div>
</template>

<script lang="ts" setup>
import { Motion } from 'motion-v';
import { cn } from '@/lib/utils';

interface Props {
  class?: string;
  delay?: number;
  data: Ref<number>;
}

const props = withDefaults(defineProps<Props>(), {
  delay: 1000,
});

const slots = useSlots();
const displayedItems = ref<{ node: unknown; id: string }[]>([]);
const nextIndex = ref(0);

watch(props.data, startLoop);

async function startLoop() {
  const notifications = slots.default
    ? (slots.default()[0]?.children ?? [])
    : [];

  if (!notifications.length) return;

  displayedItems.value.push({
    node: (notifications as string)[nextIndex.value],
    id: `${nextIndex.value}-${Date.now()}`,
  });
  nextIndex.value = (nextIndex.value + 1) % (notifications as string).length;

  await wait(props.delay * 2);

  displayedItems.value.shift();
}

const itemsToShow = computed(() => displayedItems.value);

async function wait(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
</script>

<style scoped>
.move {
  transition: transform 0.4s ease-out;
}
</style>

