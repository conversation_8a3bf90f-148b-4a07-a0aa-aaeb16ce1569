# 多级评论系统设计方案

基于"次元回廊"项目的技术栈，我设计了一个高性能、可扩展的多级评论系统方案，支持无限层级嵌套和高效查询。

## 数据库设计（MongoDB）

```javascript
// 评论模型 Comment
{
  _id: ObjectId,
  postId: ObjectId,       // 所属图片/帖子ID
  userId: ObjectId,       // 评论用户ID
  content: String,        // 评论内容
  parentId: ObjectId,     // 父评论ID (null表示顶级评论)
  ancestors: [ObjectId],  // 祖先评论ID数组 (从顶级到父级)
  depth: Number,          // 嵌套深度 (0=顶级)
  likes: [ObjectId],      // 点赞用户ID数组
  createdAt: Date,
  updatedAt: Date
}

// 索引优化
db.comments.createIndex({ postId: 1, depth: 1 })
db.comments.createIndex({ ancestors: 1 })
db.comments.createIndex({ parentId: 1 })
```

## 后端实现（NestJS）

### 评论服务核心方法

```typescript
// comment.service.ts
@Injectable()
export class CommentService {
  constructor(
    @InjectModel(Comment.name) private commentModel: Model<CommentDocument>,
    private userService: UserService
  ) {}

  // 创建评论
  async createComment(createCommentDto: CreateCommentDto): Promise<Comment> {
    const { postId, userId, content, parentId } = createCommentDto

    let depth = 0
    let ancestors: Types.ObjectId[] = []

    if (parentId) {
      const parentComment = await this.commentModel.findById(parentId)
      if (!parentComment) throw new NotFoundException('父评论不存在')

      depth = parentComment.depth + 1
      ancestors = [...parentComment.ancestors, new Types.ObjectId(parentId)]
    }

    const newComment = new this.commentModel({
      postId: new Types.ObjectId(postId),
      userId: new Types.ObjectId(userId),
      content,
      parentId: parentId ? new Types.ObjectId(parentId) : null,
      ancestors,
      depth,
      likes: [],
    })

    return newComment.save()
  }

  // 获取帖子的评论树
  async getCommentsTree(postId: string): Promise<CommentTreeItem[]> {
    // 获取所有评论（按创建时间排序）
    const comments = await this.commentModel
      .find({ postId: new Types.ObjectId(postId) })
      .sort({ createdAt: 1 })
      .lean()

    // 构建评论树
    const commentMap = new Map<string, CommentTreeItem>()
    const tree: CommentTreeItem[] = []

    comments.forEach((comment) => {
      const commentId = comment._id.toString()

      const treeItem: CommentTreeItem = {
        ...comment,
        replies: [],
        user: null, // 稍后填充用户信息
      }

      commentMap.set(commentId, treeItem)

      if (!comment.parentId) {
        tree.push(treeItem)
      } else {
        const parentId = comment.parentId.toString()
        const parent = commentMap.get(parentId)
        if (parent) {
          parent.replies.push(treeItem)
        }
      }
    })

    // 批量获取用户信息
    const userIds = [...new Set(comments.map((c) => c.userId.toString()))]
    const users = await this.userService.findUsersByIds(userIds)
    const userMap = new Map(users.map((u) => [u._id.toString(), u]))

    // 填充用户信息
    for (const item of commentMap.values()) {
      item.user = userMap.get(item.userId.toString()) || null
    }

    return tree
  }

  // 获取评论的直接回复（分页）
  async getCommentReplies(
    commentId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedReplies> {
    const skip = (page - 1) * limit

    const [replies, total] = await Promise.all([
      this.commentModel
        .find({ parentId: new Types.ObjectId(commentId) })
        .sort({ createdAt: 1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.commentModel.countDocuments({ parentId: new Types.ObjectId(commentId) }),
    ])

    // 获取用户信息
    const userIds = [...new Set(replies.map((r) => r.userId.toString()))]
    const users = await this.userService.findUsersByIds(userIds)
    const userMap = new Map(users.map((u) => [u._id.toString(), u]))

    const enrichedReplies = replies.map((reply) => ({
      ...reply,
      user: userMap.get(reply.userId.toString()) || null,
    }))

    return {
      data: enrichedReplies,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  // 点赞/取消点赞评论
  async toggleLike(commentId: string, userId: string): Promise<Comment> {
    const comment = await this.commentModel.findById(commentId)
    if (!comment) throw new NotFoundException('评论不存在')

    const userIdObj = new Types.ObjectId(userId)
    const likeIndex = comment.likes.indexOf(userIdObj)

    if (likeIndex === -1) {
      comment.likes.push(userIdObj)
    } else {
      comment.likes.splice(likeIndex, 1)
    }

    return comment.save()
  }
}
```

### WebSocket 实时通知

```typescript
// comment.gateway.ts
@WebSocketGateway({ namespace: 'comments' })
export class CommentGateway {
  @WebSocketServer()
  server: Server

  // 新评论通知
  notifyNewComment(postId: string, comment: Comment) {
    this.server.to(`post_${postId}`).emit('new-comment', comment)
  }

  // 新回复通知
  notifyNewReply(parentCommentId: string, reply: Comment) {
    this.server.to(`comment_${parentCommentId}`).emit('new-reply', reply)
  }

  // 点赞通知
  notifyCommentLike(commentId: string, userId: string, like: boolean) {
    this.server.to(`comment_${commentId}`).emit('comment-like', {
      commentId,
      userId,
      like,
    })
  }
}
```

## 前端实现（Nuxt.js）

### 评论组件结构

```
components/
  comments/
    CommentSection.vue       # 评论区域容器
    CommentList.vue          # 评论列表
    CommentItem.vue          # 单个评论项
    CommentForm.vue          # 评论表单
    CommentReplies.vue       # 回复列表
```

### 核心组件实现

```vue
<!-- CommentItem.vue -->
<template>
  <div
    class="comment-item"
    :class="[`depth-${depth}`]">
    <div class="comment-header">
      <UserAvatar
        :user="comment.user"
        size="sm" />
      <div class="user-info">
        <span class="username">{{ comment.user.username }}</span>
        <span class="timestamp">{{ formatTime(comment.createdAt) }}</span>
      </div>
      <button
        class="like-btn"
        @click="toggleLike">
        <Icon :name="isLiked ? 'ph:heart-fill' : 'ph:heart'" />
        <span>{{ comment.likes.length }}</span>
      </button>
    </div>

    <div class="comment-content">
      <p>{{ comment.content }}</p>
    </div>

    <div class="comment-actions">
      <button @click="toggleReplyForm">回复</button>
      <button
        v-if="depth < 5"
        @click="toggleReplies">
        {{ showReplies ? '收起回复' : `查看回复 (${replyCount})` }}
      </button>
    </div>

    <CommentForm
      v-if="showReplyForm"
      :parentId="comment._id"
      :postId="comment.postId"
      @submit="handleReplySubmit"
      @cancel="toggleReplyForm" />

    <div
      v-if="showReplies"
      class="replies-container">
      <CommentReplies
        :parentId="comment._id"
        :initialReplies="initialReplies"
        @load-more="loadMoreReplies" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '~/stores/user'

const props = defineProps({
  comment: Object,
  depth: { type: Number, default: 0 },
  initialReplies: { type: Array, default: () => [] },
})

const emit = defineEmits(['reply-submitted', 'like-toggled'])

const userStore = useUserStore()
const showReplyForm = ref(false)
const showReplies = ref(false)
const repliesPage = ref(1)
const replies = ref([...props.initialReplies])
const replyCount = ref(props.comment.replyCount || 0)

const isLiked = computed(() => {
  return props.comment.likes.some((id) => id.toString() === userStore.user?._id?.toString())
})

const toggleReplyForm = () => {
  showReplyForm.value = !showReplyForm.value
}

const toggleReplies = () => {
  showReplies.value = !showReplies.value
}

const handleReplySubmit = async (newReply) => {
  emit('reply-submitted', newReply)
  replies.value.unshift(newReply)
  replyCount.value += 1
  showReplyForm.value = false

  // 如果之前没有展开回复，自动展开
  if (!showReplies.value) {
    showReplies.value = true
  }
}

const toggleLike = async () => {
  emit('like-toggled', props.comment._id, !isLiked.value)
}

const loadMoreReplies = async (newReplies) => {
  replies.value = [...replies.value, ...newReplies]
  repliesPage.value += 1
}
</script>

<style scoped>
.comment-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: var(--bg-secondary);

  &.depth-1 {
    margin-left: 1.5rem;
  }
  &.depth-2 {
    margin-left: 3rem;
  }
  /* 最多支持5级嵌套 */
  &.depth-3 {
    margin-left: 4.5rem;
  }
  &.depth-4 {
    margin-left: 6rem;
  }
  &.depth-5 {
    margin-left: 7.5rem;
  }
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.user-info {
  flex: 1;
  margin-left: 0.75rem;

  .username {
    font-weight: 600;
    margin-right: 0.5rem;
  }

  .timestamp {
    color: var(--text-secondary);
    font-size: 0.85rem;
  }
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);

  &:hover {
    color: var(--primary);
  }
}

.comment-actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;

  button {
    color: var(--text-secondary);
    background: none;
    border: none;
    cursor: pointer;

    &:hover {
      color: var(--primary);
      text-decoration: underline;
    }
  }
}

.replies-container {
  margin-top: 1rem;
  border-left: 2px solid var(--border-color);
  padding-left: 1rem;
}
</style>
```

### 性能优化策略

1. **按需加载回复**

   - 初始只加载前 3 条回复
   - 点击"查看更多"时加载更多回复
   - 使用虚拟滚动处理大量评论

2. **评论分页**

   ```typescript
   // 后端分页实现
   async getCommentsByPost(
     postId: string,
     page: number = 1,
     limit: number = 20
   ): Promise<PaginatedComments> {
     const skip = (page - 1) * limit;

     const [comments, total] = await Promise.all([
       this.commentModel
         .find({ postId, depth: 0 }) // 只获取顶级评论
         .sort({ createdAt: -1 })
         .skip(skip)
         .limit(limit)
         .lean(),
       this.commentModel.countDocuments({ postId, depth: 0 })
     ]);

     // 获取前3条回复（针对每个顶级评论）
     const commentIds = comments.map(c => c._id);
     const replies = await this.commentModel
       .find({
         ancestors: { $in: commentIds },
         depth: 1
       })
       .sort({ createdAt: 1 })
       .limit(commentIds.length * 3) // 每个顶级评论最多3条回复
       .lean();

     // 构建树结构
     const commentMap = new Map(comments.map(c => [c._id.toString(), { ...c, replies: [] }]));

     replies.forEach(reply => {
       const parentId = reply.ancestors[0]?.toString();
       if (parentId && commentMap.has(parentId)) {
         commentMap.get(parentId).replies.push(reply);
       }
     });

     return {
       data: Array.from(commentMap.values()),
       pagination: {
         page,
         limit,
         total,
         totalPages: Math.ceil(total / limit)
       }
     };
   }
   ```

3. **实时更新优化**

   - 使用 Socket.IO 增量更新
   - 防抖处理频繁更新
   - 客户端缓存管理

4. **渲染优化**
   - 使用 Vue 的`v-once`指令处理静态内容
   - 评论项使用`key`属性优化 diff 算法
   - 避免深层嵌套的响应式对象

## 安全与审核机制

1. **内容审核**

   - 集成第三方内容审核 API（如阿里云、腾讯云）
   - 敏感词过滤系统
   - 用户举报机制

2. **速率限制**

   ```typescript
   // 使用NestJS限流模块
   @Throttle(5, 60) // 每分钟最多5条评论
   @Post()
   async createComment(@Body() dto: CreateCommentDto) {
     // ...
   }
   ```

3. **XSS 防护**

   - 使用 DOMPurify 清理用户输入
   - 内容渲染时使用`v-html`的安全替代方案

4. **权限控制**
   - 用户只能编辑/删除自己的评论
   - 管理员删除评论时保留审核记录

## 进阶功能建议

1. **评论引用**

   ```javascript
   // 在评论模型中增加
   {
     replyTo: ObjectId, // 被回复的评论ID
     quotedText: String // 引用的文本片段
   }
   ```

2. **富文本评论**

   - 集成 TinyMCE 或 Quill 编辑器
   - 支持图片/表情/代码块等格式
   - Markdown 格式支持

3. **评论投票**

   ```javascript
   // 扩展评论模型
   {
     upvotes: [ObjectId],
     downvotes: [ObjectId],
     score: Number // 综合得分 = upvotes - downvotes
   }
   ```

4. **评论折叠**

   - 基于用户反馈自动折叠低质量评论
   - 根据评分阈值折叠评论
   - 用户手动折叠/展开评论分支

5. **AI 分析**
   - 情感分析（正面/负面评论）
   - 自动摘要生成
   - 话题聚类分析

## 部署优化建议

1. **Redis 缓存策略**

   - 缓存热门帖子的评论树
   - 缓存用户点赞状态
   - 设置合理的 TTL（如 30 分钟）

2. **分片策略**

   - 按帖子 ID 分片评论集合
   - 使用 MongoDB 分片集群

3. **CDN 加速**
   - 静态资源（头像、表情等）使用 CDN
   - 压缩评论传输数据（使用 Brotli 压缩）
