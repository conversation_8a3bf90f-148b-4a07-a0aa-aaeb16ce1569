<template>
  <div class="mobile-auth-layout">
    <MoblieNavBar>
      <template #right><div></div></template>
    </MoblieNavBar>
    <div class="box">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.mobile-auth-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: var(--background-base);
  color: var(--text-primary);

  > .box {
    flex: 1;
    padding: 2rem;
  }
}
</style>

