<template>
  <div class="app-image-selector">
    <!-- 展示已选图片 -->
    <div class="selected-preview">
      <div class="preview-container">
        <!-- 已选图片 -->
        <div
          v-for="(image, index) in selectedImages"
          :key="'preview-' + image.id"
          class="preview-item"
          :class="{ 'has-error': image.hasError }"
          @click="removeImage(image.id)"
          @keydown.enter="removeImage(image.id)"
          @keydown.delete="removeImage(image.id)"
          tabindex="0"
          role="button"
          :aria-label="`移除第${index + 1}张图片`"
        >
          <img
            :src="image.url"
            :alt="`预览图片 ${index + 1}`"
            @error="handleImageError(image.id)"
            @load="handleImageLoad(image.id)"
          />
          <div class="image-index">{{ index + 1 }}</div>
          <div class="remove-overlay">
            <div class="remove-icon">×</div>
          </div>
          <!-- 加载错误提示 -->
          <div v-if="image.hasError" class="error-overlay">
            <div class="error-icon">!</div>
            <div class="error-text">加载失败</div>
          </div>
        </div>

        <!-- 添加图片按钮 -->
        <div
          v-if="canAddMore"
          class="preview-item add-image-placeholder"
          @click="openImageSelector"
          @keydown.enter="openImageSelector"
          tabindex="0"
          role="button"
          aria-label="添加图片"
        >
          <div class="add-icon">+</div>
          <div class="add-text">添加图片</div>
          <div class="add-hint">
            {{ selectedImages.length }}/{{ maxSelection }}
          </div>
        </div>

        <!-- 空占位符 -->
        <template v-for="i in placeholderCount" :key="'placeholder-' + i">
          <div class="preview-item empty-placeholder" aria-hidden="true"></div>
        </template>
      </div>
    </div>

    <!-- 图片选择器弹窗 -->
    <RikkaDialog
      :show="showDialog"
      title="选择图片"
      :width="dialogWidth"
      :show-footer="true"
      @close="closeDialog"
      @keydown.esc="closeDialog"
    >
      <div class="image-selector">
        <div
          v-for="image in images"
          :key="image.id"
          class="image-item"
          :class="{
            selected: isSelected(image.id),
            disabled: isDisabled(image.id),
            'has-error': image.hasError,
          }"
          @click="toggleSelection(image.id)"
          @keydown.enter="toggleSelection(image.id)"
          @keydown.space.prevent="toggleSelection(image.id)"
          tabindex="0"
          role="button"
          :aria-label="`选择图片 ${image.id}`"
          :aria-pressed="isSelected(image.id)"
        >
          <img
            :src="image.url"
            :alt="`图片 ${image.id}`"
            @error="handleImageError(image.id)"
            @load="handleImageLoad(image.id)"
          />
          <div class="checkmark" v-if="isSelected(image.id)" aria-hidden="true">
            ✓
          </div>
          <div
            class="selection-number"
            v-if="showSelectionNumber(image.id)"
            aria-hidden="true"
          >
            {{ getSelectionNumber(image.id) }}
          </div>
          <!-- 禁用状态遮罩 -->
          <div
            class="disabled-overlay"
            v-if="isDisabled(image.id)"
            aria-hidden="true"
          ></div>
          <!-- 加载错误提示 -->
          <div v-if="image.hasError" class="error-overlay" aria-hidden="true">
            <div class="error-icon">!</div>
            <div class="error-text">加载失败</div>
          </div>
        </div>
      </div>
      <!-- 哨兵元素 -->
      <div ref="sentinelRef" class="sentinel-element" aria-hidden="true"></div>
      <!-- 加载状态提示 -->
      <div
        v-if="loading"
        class="loading-indicator"
        role="status"
        aria-live="polite"
      >
        <div class="loader" aria-hidden="true"></div>
        努力加载中....
      </div>
      <div v-else-if="!hasMore && images.length > 0" class="loading-indicator">
        已经到底啦！
      </div>
      <div v-else-if="images.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">📷</div>
        <div class="empty-text">暂无图片</div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <RippleButton class="btn-cancel" @click="closeDialog" type="button">
            取消
          </RippleButton>
          <RippleButton
            @click="clearSelection"
            :disabled="tempSelectedImages.length === 0"
            class="btn-clear"
            type="button"
          >
            清空
          </RippleButton>
          <RippleButton
            class="btn-confirm"
            @click="confirmSelection"
            :disabled="tempSelectedImages.length === 0"
            type="button"
          >
            确定 ({{ tempSelectedImages.length }})
          </RippleButton>
        </div>
      </template>
    </RikkaDialog>
  </div>
</template>

<script setup lang="ts">
interface Image {
  id: string;
  url: string;
  hasError?: boolean;
  isLoading?: boolean;
}

// 定义组件的 props
const props = defineProps({
  images: {
    type: Array as () => Image[],
    required: true,
    default: () => [],
  },
  maxSelection: {
    type: Number,
    default: 9,
  },
  modelValue: {
    type: Array as () => string[],
    default: () => [],
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 定义组件可以触发的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void;
  (e: 'change', value: string[]): void;
  (e: 'load-more'): void;
  (e: 'image-error', imageId: string): void;
}>();

// 响应式状态
const showDialog = ref(false);
const selectedImageIds = ref<string[]>([]);
const tempSelectedImages = ref<Image[]>([]);
const sentinelRef = ref<HTMLElement | null>(null);

// 计算属性
const selectedImages = computed(() => {
  // 确保只返回在 props.images 中存在的已选图片
  return props.images.filter((image) =>
    selectedImageIds.value.includes(image.id)
  );
});

const canAddMore = computed(() => {
  return selectedImages.value.length < props.maxSelection;
});

const placeholderCount = computed(() => {
  const totalItems = selectedImages.value.length + (canAddMore.value ? 1 : 0);
  const remainder = totalItems % 3;
  return remainder > 0 ? 3 - remainder : 0;
});

// 响应式弹窗宽度 - 移动端自适应
const windowWidth = ref(
  typeof window !== 'undefined' ? window.innerWidth : 1024
);

const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '95vw' : '40rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 方法
const openImageSelector = () => {
  if (!canAddMore.value) return;

  // 初始化临时选择 - 修复：确保只选择已存在的图片
  tempSelectedImages.value = [];
  if (selectedImageIds.value.length > 0) {
    // 只添加在 props.images 中存在的图片
    const validImages = props.images.filter((image) =>
      selectedImageIds.value.includes(image.id)
    );
    tempSelectedImages.value = validImages.map((img) => ({ ...img }));
  }

  // 验证选择状态
  validateSelectionState();

  showDialog.value = true;
};

const closeDialog = () => {
  showDialog.value = false;
};

const confirmSelection = () => {
  selectedImageIds.value = tempSelectedImages.value.map((image) => image.id);
  emit('update:modelValue', selectedImageIds.value);
  emit('change', selectedImageIds.value);
  showDialog.value = false;
};

const clearSelection = () => {
  tempSelectedImages.value = [];
};

const removeImage = (imageId: string) => {
  const newSelectedIds = selectedImageIds.value.filter((id) => id !== imageId);
  selectedImageIds.value = newSelectedIds;
  emit('update:modelValue', newSelectedIds);
  emit('change', newSelectedIds);
};

const isSelected = (id: string) => {
  // 只检查临时选择状态，不检查已确认的选择
  return tempSelectedImages.value.some((img) => img.id === id);
};

const isDisabled = (id: string) => {
  return (
    tempSelectedImages.value.length >= props.maxSelection && !isSelected(id)
  );
};

const showSelectionNumber = (id: string) => {
  return isSelected(id) && tempSelectedImages.value.length > 1;
};

const getSelectionNumber = (id: string) => {
  const index = tempSelectedImages.value.findIndex((img) => img.id === id);
  return index + 1;
};

const toggleSelection = (id: string) => {
  if (isDisabled(id)) return;

  // 验证图片是否存在于 props.images 中
  const imageToToggle = props.images.find((img) => img.id === id);
  if (!imageToToggle) {
    return;
  }

  const imageIndex = tempSelectedImages.value.findIndex((img) => img.id === id);

  if (imageIndex > -1) {
    // 取消选择
    tempSelectedImages.value.splice(imageIndex, 1);
  } else {
    // 添加选择 - 确保不超过最大选择数
    if (tempSelectedImages.value.length < props.maxSelection) {
      tempSelectedImages.value.push({ ...imageToToggle });
    }
  }
};

const handleImageError = (imageId: string) => {
  // 更新图片错误状态
  const image = props.images.find((img) => img.id === imageId);
  if (image) {
    image.hasError = true;
  }
  emit('image-error', imageId);
};

const handleImageLoad = (imageId: string) => {
  // 清除图片错误状态
  const image = props.images.find((img) => img.id === imageId);
  if (image) {
    image.hasError = false;
    image.isLoading = false;
  }
};

// 验证选择状态的一致性
const validateSelectionState = () => {
  const tempIds = tempSelectedImages.value.map((img) => img.id);
  const validTempIds = tempIds.filter((id) =>
    props.images.some((img) => img.id === id)
  );

  if (tempIds.length !== validTempIds.length) {
    tempSelectedImages.value = tempSelectedImages.value.filter((img) =>
      validTempIds.includes(img.id)
    );
  }
};

// Intersection Observer
let observer: IntersectionObserver | null = null;

const initScrollObserver = () => {
  if (observer) observer.disconnect();

  if (sentinelRef.value) {
    observer = new IntersectionObserver(
      (entries) => {
        const sentinel = entries[0];
        if (sentinel?.isIntersecting && !props.loading && props.hasMore) {
          emit('load-more');
        }
      },
      {
        root: null,
        rootMargin: '0px 0px 200px 0px',
        threshold: 0.01,
      }
    );

    observer.observe(sentinelRef.value);
  }
};

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    selectedImageIds.value = [...newVal];
  },
  { immediate: true }
);

watch(showDialog, async (newVal) => {
  if (newVal) {
    await nextTick();
    initScrollObserver();
  } else if (observer) {
    observer.disconnect();
    observer = null;
  }
});

// 监听数据变化，重新初始化观察器
watch(
  [() => props.images, () => props.loading, () => props.hasMore],
  () => {
    if (showDialog.value) {
      nextTick(() => {
        initScrollObserver();
      });
    }
  },
  { deep: true }
);

// 生命周期
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
    observer = null;
  }
});
</script>

<style scoped lang="scss">
.app-image-selector {
  --primary-color: var(--interactive-primary);
  --error-color: var(--interactive-danger);
  --border-color: var(--neutral-border);
  --bg-color: var(--background-surface);
  --text-color: var(--text-primary);
  --shadow-light: var(--shadow-dimension-sm);
  --shadow-medium: var(--shadow-dimension-md);
  --transition: all 0.3s ease;
  --loading-color: var(--magic-gold);
  --spinner-size: 40px;
}

.selected-preview {
  padding: 1rem;
  background-color: var(--background-elevated);
  border-radius: var(--border-radius-md);
  border: var(--border-mystic-divider);
  box-shadow: var(--shadow-dimension-sm);
}

.preview-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.preview-item {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  box-shadow: var(--shadow-dimension-sm);
  transition: var(--transition);
  cursor: pointer;
  outline: none;

  &:focus-visible {
    box-shadow: var(--shadow-neon-primary);
  }

  &.has-error {
    border: 2px solid var(--interactive-danger);
    box-shadow: var(--shadow-neon-danger);
  }

  &.add-image-placeholder {
    border: 2px dashed var(--neutral-border);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--background-elevated);

    &:hover {
      border-color: var(--interactive-primary);
      background-color: var(--background-floating);
      box-shadow: var(--shadow-neon-primary);
    }

    &:focus-visible {
      border-color: var(--interactive-primary);
      background-color: var(--background-floating);
      box-shadow: var(--shadow-neon-primary);
    }
  }

  &.empty-placeholder {
    visibility: hidden;
    border: none;
    box-shadow: none;
    cursor: default;
  }
}

.preview-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.image-index {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  border-radius: 0.25rem 0 0 0;
  z-index: 2;
}

.remove-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
  z-index: 3;
}

.preview-item:hover .remove-overlay {
  opacity: 1;
}

.remove-icon {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.add-icon {
  font-size: 2rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.add-text {
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.add-hint {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(210, 15, 57, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.error-icon {
  color: var(--interactive-danger);
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.error-text {
  color: var(--interactive-danger);
  font-size: 0.75rem;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.selection-info {
  font-size: 0.875rem;
  color: var(--text-color);
  font-weight: 500;
}

.image-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.9375rem;
  position: relative;

  // 移动端优化
  @media (max-width: 768px) {
    gap: 0.5rem;
    padding: 0.5rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

.image-item {
  position: relative;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  aspect-ratio: 1/1;
  transition: var(--transition);
  outline: none;

  &:focus-visible {
    box-shadow: var(--shadow-neon-primary);
  }

  &.disabled {
    cursor: not-allowed;

    img {
      filter: grayscale(100%);
      opacity: 0.6;
    }

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  &.has-error {
    border: 2px solid var(--interactive-danger);
    box-shadow: var(--shadow-neon-danger);
  }
}

.image-item:hover:not(.disabled) {
  transform: scale(1.03);
  box-shadow: var(--shadow-dimension-md);

  // 移动端禁用悬停效果
  @media (max-width: 768px) {
    transform: none;
    box-shadow: none;
  }
}

.image-item.selected {
  box-shadow:
    0 0 0 3px var(--interactive-primary),
    var(--shadow-neon-primary);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.checkmark {
  position: absolute;
  top: 0.3125rem;
  right: 0.3125rem;
  background: var(--interactive-primary);
  color: var(--text-primary);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 2;
  box-shadow: var(--shadow-neon-primary);
}

.selection-number {
  position: absolute;
  bottom: 0.3125rem;
  right: 0.3125rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  z-index: 2;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

.loading-indicator,
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 1.25rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.empty-text {
  color: var(--text-secondary);
}

.loader {
  width: var(--spinner-size);
  height: var(--spinner-size);
  border: 3px solid rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  border-top-color: var(--magic-gold);
  animation: spin 1s ease-in-out infinite;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    box-shadow: var(--shadow-neon-glow);
    animation: glow 1.5s ease-in-out infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.sentinel-element {
  width: 100%;
  visibility: hidden;
  grid-column: 1 / -1;
  margin-top: 1rem;
  transform: scale(0);
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;
  gap: 1rem;

  // 移动端优化
  @media (max-width: 768px) {
    gap: 0.5rem;
    padding: 0 0.5rem;
  }

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: var(--transition);

    // 移动端按钮宽度优化
    @media (max-width: 768px) {
      width: 6rem;
      font-size: 1.4rem;
    }

    @media (max-width: 480px) {
      width: 5rem;
      font-size: 1.2rem;
      padding: 0.5rem;
    }

    &:hover {
      background-color: var(--button-cancel-hover);

      // 移动端禁用悬停效果
      @media (max-width: 768px) {
        background-color: var(--button-cancel);
      }
    }
  }

  > .btn-clear {
    width: 8rem;
    background-color: var(--interactive-danger);
    color: var(--text-primary);
    transition: var(--transition);

    // 移动端按钮宽度优化
    @media (max-width: 768px) {
      width: 6rem;
      font-size: 1.4rem;
    }

    @media (max-width: 480px) {
      width: 5rem;
      font-size: 1.2rem;
      padding: 0.5rem;
    }

    &:hover:not(:disabled) {
      background-color: var(--interactive-danger-glow);
      box-shadow: var(--shadow-neon-danger);

      // 移动端禁用悬停效果
      @media (max-width: 768px) {
        background-color: var(--interactive-danger);
        box-shadow: none;
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: var(--transition);

    // 移动端按钮宽度优化
    @media (max-width: 768px) {
      width: 6rem;
      font-size: 1.4rem;
    }

    @media (max-width: 480px) {
      width: 5rem;
      font-size: 1.2rem;
      padding: 0.5rem;
    }

    &:hover:not(:disabled) {
      background-color: var(--button-primary-hover);
      box-shadow: var(--shadow-neon-primary);

      // 移动端禁用悬停效果
      @media (max-width: 768px) {
        background-color: var(--button-primary);
        box-shadow: none;
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}
</style>

