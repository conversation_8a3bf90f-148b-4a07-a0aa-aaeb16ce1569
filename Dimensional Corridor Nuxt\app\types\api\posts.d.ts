/**
 * 文章上传信息
 * @property {string} title - 文章标题
 * @property {string} content - 文章内容
 * @property {string} category - 文章分类
 * @property {string[]} tags - 文章标签数组
 * @property {string[]} photos - 关联的图片ID数组
 * @property {string} [status] - 可选，文章状态（如：草稿/已发布）
 */
interface PostsUploadInfo {
  title: string;
  content: string;
  category: string;
  tags: string[];
  photos: string[];
  status?: string;
}

/**
 * 用户自己发布的文章详情
 * @property {string} id - 文章唯一ID
 * @property {string} title - 文章标题
 * @property {string} content - 文章内容
 * @property {string} category - 文章分类
 * @property {string[]} tags - 文章标签数组
 * @property {any[]} photos - 关联的图片信息数组
 * @property {number} viewCount - 浏览量
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {string} status - 文章状态
 */
interface GetSelfPostsInfo {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  photos: any[];
  viewCount: number;
  favoriteCount: number;
  likeCount: number;
  status: string;
  isLiked: boolean;
  isFavorited: boolean;
}

/**
 * 其他用户发布的文章详情
 * @property {string} id - 文章唯一ID
 * @property {Object} user - 发布用户信息
 * @property {string} user.uid - 用户ID
 * @property {string} user.nickname - 用户昵称
 * @property {string} user.avatar - 用户头像URL
 * @property {string} title - 文章标题
 * @property {string} content - 文章内容
 * @property {string} category - 文章分类
 * @property {string[]} tags - 文章标签数组
 * @property {Object[]} photos - 关联的图片信息
 * @property {string} photos.id - 图片ID
 * @property {string} photos.url - 图片URL
 * @property {string} photos.filename - 图片文件名
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {boolean} isLiked - 当前用户是否点赞
 * @property {boolean} isFavorited - 当前用户是否收藏
 * @property {string} status - 文章状态
 */
interface GetOtherPostsInfo {
  id: string;
  user: {
    uid: string;
    nickname: string;
    avatar: string;
  };
  title: string;
  content: string;
  category: string;
  tags: string[];
  photos: {
    id: string;
    url: string;
    filename: string;
  }[];
  favoriteCount: number;
  likeCount: number;
  isLiked: boolean;
  isFavorited: boolean;
  status: string;
}

/**
 * 文章排序字段
 * - 'createTime': 按创建时间排序
 * - 'updateTime': 按更新时间排序
 * - 'viewCount': 按浏览量排序
 * - 'favoriteCount': 按收藏数排序
 * - 'likeCount': 按点赞数排序
 */
type PostsSortField =
  | 'createTime'
  | 'updateTime'
  | 'viewCount'
  | 'favoriteCount'
  | 'likeCount';

/**
 * 文章列表查询参数
 * @extends QueryTemplate<PostsSortField>
 * @property {string} [category] - 可选，按分类筛选
 * @property {string[]} [tags] - 可选，按标签筛选
 */
type PostsListQuery = QueryTemplate<PostsSortField> & {
  category?: string;
  tags?: string[];
};

/**
 * 文章列表项
 * @property {string} id - 文章唯一ID
 * @property {Object} user - 发布用户基本信息
 * @property {string} user.uid - 用户ID
 * @property {string} user.avatar - 用户头像URL
 * @property {string} title - 文章标题
 * @property {string} content - 文章内容摘要
 * @property {Object[]} photos - 关联的图片信息
 * @property {string} photos.id - 图片ID
 * @property {string} photos.url - 图片URL
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {boolean} isLiked - 当前用户是否点赞
 * @property {boolean} isFavorited - 当前用户是否收藏
 */
interface PostsList {
  id: string;
  user: {
    uid: string;
    nickname: string;
    avatar: string;
  };
  title: string;
  content: string;
  photos: {
    id: string;
    filename: string;
    url: string;
    attributes: {
      size: number;
      width: number;
      height: number;
      format: string;
    };
  }[];
  favoriteCount: number;
  likeCount: number;
  isLiked: boolean;
  isFavorited: boolean;
}
