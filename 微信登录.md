在 NestJS 中实现微信登录需要基于 **OAuth2.0 授权码模式**，以下是完整实现步骤：

---

### 一、准备工作

#### 1. 注册微信开放平台

- 访问 [微信开放平台](https://open.weixin.qq.com/) 注册账号
- 创建网站应用，获取 **AppID** 和 **AppSecret**
- 配置授权回调域名（如 `https://yourdomain.com/api/auth/wechat/callback`）

#### 2. 流程图解

```mermaid
sequenceDiagram
  participant 用户
  participant 前端
  participant 后端
  participant 微信服务器

  用户->>前端: 点击"微信登录"
  前端->>后端: 请求微信登录地址
  后端->>前端: 返回微信授权URL
  前端->>微信服务器: 重定向到授权页面
  用户->>微信服务器: 确认授权
  微信服务器->>后端: 回调并携带code
  后端->>微信服务器: 用code换access_token
  微信服务器->>后端: 返回access_token和openid
  后端->>微信服务器: 用access_token获取用户信息
  微信服务器->>后端: 返回用户信息(openid, unionid等)
  后端->>后端: 创建/更新本地用户
  后端->>前端: 返回JWT令牌或用户信息
```

---

### 二、后端实现步骤

#### 1. 安装依赖

```bash
npm install axios qs @nestjs/jwt @nestjs/passport passport passport-wechat
```

#### 2. 配置微信参数 (`.env`)

```env
# 微信配置
WECHAT_APP_ID=your_appid
WECHAT_APP_SECRET=your_secret
WECHAT_CALLBACK_URL=https://yourdomain.com/api/auth/wechat/callback
```

#### 3. 创建微信登录模块

```typescript
// src/auth/wechat.strategy.ts
import { Injectable } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { Strategy } from 'passport-wechat'
import { AuthService } from './auth.service'

@Injectable()
export class WechatStrategy extends PassportStrategy(Strategy, 'wechat') {
  constructor(private authService: AuthService) {
    super({
      appID: process.env.WECHAT_APP_ID,
      appSecret: process.env.WECHAT_APP_SECRET,
      client: 'web',
      callbackURL: process.env.WECHAT_CALLBACK_URL,
      scope: 'snsapi_login', // 需要用户授权
    })
  }

  async validate(accessToken: string, refreshToken: string, profile: any): Promise<any> {
    // profile包含微信用户信息
    const { openid, unionid, nickname, headimgurl } = profile
    return this.authService.validateWechatUser({
      openid,
      unionid,
      nickname,
      avatar: headimgurl,
    })
  }
}
```

#### 4. 实现用户服务

```typescript
// src/auth/auth.service.ts
import { Injectable } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { UserService } from '../user/user.service'

@Injectable()
export class AuthService {
  constructor(private userService: UserService, private jwtService: JwtService) {}

  async validateWechatUser(wechatUser: {
    openid: string
    unionid?: string
    nickname?: string
    avatar?: string
  }) {
    // 1. 查询是否已存在用户
    let user = await this.userService.findByWechatOpenid(wechatUser.openid)

    // 2. 不存在则创建用户
    if (!user) {
      user = await this.userService.createWechatUser({
        openid: wechatUser.openid,
        unionid: wechatUser.unionid,
        nickname: wechatUser.nickname || '微信用户',
        avatar: wechatUser.avatar || '',
      })
    }

    // 3. 更新用户信息（可选）
    else if (wechatUser.nickname || wechatUser.avatar) {
      user = await this.userService.updateUser(user.id, {
        nickname: wechatUser.nickname || user.nickname,
        avatar: wechatUser.avatar || user.avatar,
      })
    }

    // 4. 生成JWT
    return this.generateJwt(user)
  }

  private generateJwt(user: any) {
    const payload = {
      sub: user.id,
      username: user.username,
    }
    return {
      access_token: this.jwtService.sign(payload),
    }
  }
}
```

#### 5. 用户模块实体示例

```typescript
// src/user/user.entity.ts
import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm'

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true })
  openid: string // 微信唯一标识

  @Column({ nullable: true })
  unionid: string // 微信多应用统一标识

  @Column({ default: '微信用户' })
  nickname: string

  @Column({ nullable: true })
  avatar: string

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date
}
```

#### 6. 控制器实现

```typescript
// src/auth/auth.controller.ts
import { Controller, Get, Req, Res } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { Request, Response } from 'express'

@Controller('auth')
export class AuthController {
  // 获取微信登录地址
  @Get('wechat')
  @UseGuards(AuthGuard('wechat'))
  wechatLogin() {
    // 自动触发微信OAuth流程
  }

  // 微信回调处理
  @Get('wechat/callback')
  @UseGuards(AuthGuard('wechat'))
  wechatCallback(@Req() req: Request, @Res() res: Response) {
    // 登录成功后重定向到前端页面并携带token
    const token = req.user.access_token
    res.redirect(`https://frontend.com/login-success?token=${token}`)
  }
}
```

---

### 三、前端对接示例

#### 1. 触发微信登录

```html
<!-- 前端登录按钮 -->
<a href="/api/auth/wechat">微信登录</a>
```

#### 2. 处理回调

```javascript
// 前端路由处理回调
if (window.location.search.includes('token=')) {
  const token = new URLSearchParams(window.location.search).get('token')
  localStorage.setItem('jwt', token)
  window.location.href = '/' // 清除URL中的token参数
}
```

---

### 四、注意事项

1. **安全配置**

   - 必须使用 **HTTPS** 协议
   - 验证 `state` 参数防止 CSRF 攻击

   ```typescript
   // 生成state并存储到session
   const state = crypto.randomBytes(16).toString('hex')
   req.session.wechatState = state
   // 在验证时检查state是否匹配
   ```

2. **错误处理**

   - 处理微信接口返回的错误码（如 40029 无效 code）
   - 添加重试机制和友好错误提示

3. **用户合并**

   - 如果已有邮箱/手机账号，需提供绑定微信功能

   ```typescript
   // 绑定微信到现有账号
   async bindWechat(userId: number, openid: string) {
     return this.userRepository.update(userId, { openid });
   }
   ```

4. **生产环境优化**
   - 使用 **Redis** 缓存 access_token（微信 access_token 有效期为 2 小时）
   - 记录登录日志
   - 实现接口限流

---

### 五、微信接口文档参考

- [网站应用微信登录文档](https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html)
- [UnionID 机制说明](https://developers.weixin.qq.com/doc/oplatform/Mobile_App/WeChat_Login/Authorized_API_call_UnionID.html)

通过以上实现，即可在 NestJS 应用中完成微信登录功能，建议使用 Postman 测试接口流程后再进行前端集成。
