# WebSocket 群组管理

## 📋 概述

本文档详细介绍WebSocket系统中的群组管理功能，包括加入群组、离开群组、群组状态管理等。

## 🏠 群组概念

### 什么是群组？
群组是一个虚拟的聊天室，用户可以加入群组进行多人聊天。每个群组有唯一的群组ID，用户必须先加入群组才能在群组内发送和接收消息。

### 群组特性
- ✅ 动态加入和离开
- ✅ 实时成员列表
- ✅ 群组消息广播
- ✅ 成员状态通知

## 🚪 加入群组

### 基本加入操作
```javascript
// 加入群组
function joinGroup(groupId) {
  socket.emit('join_group', groupId);
}

// 监听群组状态变化
socket.on('group_status', (data) => {
  if (data.action === 'joined') {
    console.log('✅ 成功加入群组:', {
      groupId: data.groupId,     // 群组ID
      action: data.action,       // 操作类型 ('joined')
      members: data.members      // 当前群组成员列表
    });
    
    // 更新UI显示
    updateGroupUI(data.groupId, data.members);
    showNotification(`已加入群组: ${data.groupId}`);
  }
});

// 使用示例
joinGroup('general');      // 加入通用群组
joinGroup('developers');   // 加入开发者群组
joinGroup('project-123');  // 加入项目群组
```

### 加入群组的完整流程
```javascript
class GroupManager {
  constructor(socket) {
    this.socket = socket;
    this.joinedGroups = new Set();
    this.groupMembers = new Map();
    
    this.initEventListeners();
  }

  // 初始化事件监听
  initEventListeners() {
    this.socket.on('group_status', (data) => {
      this.handleGroupStatus(data);
    });

    this.socket.on('group_message', (data) => {
      this.handleGroupMessage(data);
    });
  }

  // 加入群组
  async joinGroup(groupId) {
    if (this.joinedGroups.has(groupId)) {
      console.warn(`已经在群组 ${groupId} 中`);
      return false;
    }

    try {
      this.socket.emit('join_group', groupId);
      
      // 等待加入确认 (可选的Promise包装)
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('加入群组超时'));
        }, 5000);

        const handleStatus = (data) => {
          if (data.groupId === groupId && data.action === 'joined') {
            clearTimeout(timeout);
            this.socket.off('group_status', handleStatus);
            resolve(data);
          }
        };

        this.socket.on('group_status', handleStatus);
      });
    } catch (error) {
      console.error('加入群组失败:', error);
      return false;
    }
  }

  // 处理群组状态变化
  handleGroupStatus(data) {
    const { groupId, action, members } = data;

    switch (action) {
      case 'joined':
        this.joinedGroups.add(groupId);
        this.groupMembers.set(groupId, members);
        console.log(`✅ 成功加入群组: ${groupId}`);
        this.onGroupJoined?.(groupId, members);
        break;

      case 'left':
        this.joinedGroups.delete(groupId);
        this.groupMembers.delete(groupId);
        console.log(`👋 已离开群组: ${groupId}`);
        this.onGroupLeft?.(groupId);
        break;

      case 'member_joined':
        // 其他成员加入群组
        if (this.groupMembers.has(groupId)) {
          this.groupMembers.set(groupId, members);
          console.log(`👋 新成员加入群组 ${groupId}`);
          this.onMemberJoined?.(groupId, members);
        }
        break;

      case 'member_left':
        // 其他成员离开群组
        if (this.groupMembers.has(groupId)) {
          this.groupMembers.set(groupId, members);
          console.log(`👋 成员离开群组 ${groupId}`);
          this.onMemberLeft?.(groupId, members);
        }
        break;
    }
  }

  // 事件回调
  onGroupJoined = null;    // 加入群组回调
  onGroupLeft = null;      // 离开群组回调
  onMemberJoined = null;   // 成员加入回调
  onMemberLeft = null;     // 成员离开回调
}
```

## 🚪 离开群组

### 基本离开操作
```javascript
// 离开群组
function leaveGroup(groupId) {
  socket.emit('leave_group', groupId);
}

// 监听离开群组响应
socket.on('group_status', (data) => {
  if (data.action === 'left') {
    console.log('👋 已离开群组:', {
      groupId: data.groupId,     // 群组ID
      action: data.action,       // 操作类型 ('left')
      members: data.members      // 剩余群组成员列表
    });
    
    // 更新UI显示
    removeGroupFromUI(data.groupId);
    showNotification(`已离开群组: ${data.groupId}`);
  }
});

// 使用示例
leaveGroup('general');      // 离开通用群组
leaveGroup('project-123');  // 离开项目群组
```

### 批量离开群组
```javascript
// 批量离开多个群组
function leaveAllGroups() {
  const joinedGroups = Array.from(groupManager.joinedGroups);
  
  joinedGroups.forEach(groupId => {
    leaveGroup(groupId);
  });
  
  console.log(`正在离开 ${joinedGroups.length} 个群组`);
}

// 离开指定的多个群组
function leaveMultipleGroups(groupIds) {
  groupIds.forEach(groupId => {
    if (groupManager.joinedGroups.has(groupId)) {
      leaveGroup(groupId);
    } else {
      console.warn(`未加入群组: ${groupId}`);
    }
  });
}
```

## 👥 群组成员管理

### 获取群组成员列表
```javascript
// 从群组状态事件中获取成员列表
socket.on('group_status', (data) => {
  if (data.members) {
    console.log(`群组 ${data.groupId} 成员列表:`, data.members);
    displayGroupMembers(data.groupId, data.members);
  }
});

// 显示群组成员
function displayGroupMembers(groupId, members) {
  const membersList = document.getElementById(`members-${groupId}`);
  if (!membersList) return;

  membersList.innerHTML = '';
  
  members.forEach(memberId => {
    const memberElement = document.createElement('div');
    memberElement.className = 'group-member';
    memberElement.textContent = memberId; // 实际应用中应该显示用户昵称
    membersList.appendChild(memberElement);
  });
  
  // 更新成员数量显示
  const countElement = document.getElementById(`count-${groupId}`);
  if (countElement) {
    countElement.textContent = members.length;
  }
}
```

### 监听成员变化
```javascript
// 扩展的群组管理器，包含成员变化监听
class AdvancedGroupManager extends GroupManager {
  constructor(socket) {
    super(socket);
    this.memberChangeCallbacks = new Map();
  }

  // 注册成员变化回调
  onMemberChange(groupId, callback) {
    if (!this.memberChangeCallbacks.has(groupId)) {
      this.memberChangeCallbacks.set(groupId, []);
    }
    this.memberChangeCallbacks.get(groupId).push(callback);
  }

  // 移除成员变化回调
  offMemberChange(groupId, callback) {
    const callbacks = this.memberChangeCallbacks.get(groupId);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 处理群组状态变化 (重写父类方法)
  handleGroupStatus(data) {
    super.handleGroupStatus(data);

    // 触发成员变化回调
    const callbacks = this.memberChangeCallbacks.get(data.groupId);
    if (callbacks && data.members) {
      callbacks.forEach(callback => {
        try {
          callback(data.groupId, data.members, data.action);
        } catch (error) {
          console.error('成员变化回调执行失败:', error);
        }
      });
    }
  }

  // 获取群组成员数量
  getGroupMemberCount(groupId) {
    const members = this.groupMembers.get(groupId);
    return members ? members.length : 0;
  }

  // 检查用户是否在群组中
  isMemberInGroup(groupId, memberId) {
    const members = this.groupMembers.get(groupId);
    return members ? members.includes(memberId) : false;
  }

  // 获取所有已加入的群组
  getJoinedGroups() {
    return Array.from(this.joinedGroups);
  }

  // 获取群组信息
  getGroupInfo(groupId) {
    return {
      groupId,
      isJoined: this.joinedGroups.has(groupId),
      members: this.groupMembers.get(groupId) || [],
      memberCount: this.getGroupMemberCount(groupId)
    };
  }
}

// 使用示例
const advancedGroupManager = new AdvancedGroupManager(socket);

// 监听特定群组的成员变化
advancedGroupManager.onMemberChange('general', (groupId, members, action) => {
  console.log(`群组 ${groupId} 成员变化:`, action, members);
  updateGroupMembersList(groupId, members);
});
```

## 💬 群组消息处理

### 发送群组消息
```javascript
// 发送群组消息 (需要先加入群组)
function sendGroupMessage(groupId, content) {
  // 检查是否已加入群组
  if (!groupManager.joinedGroups.has(groupId)) {
    console.error(`未加入群组 ${groupId}，无法发送消息`);
    return false;
  }

  socket.emit('group_message', {
    groupId: groupId,
    content: content
  });

  return true;
}

// 使用示例
if (sendGroupMessage('general', '大家好！')) {
  console.log('群组消息发送成功');
} else {
  console.log('群组消息发送失败');
}
```

### 接收群组消息
```javascript
// 监听群组消息
socket.on('group_message', (data) => {
  console.log('收到群组消息:', {
    groupId: data.groupId,     // 群组ID
    from: data.from,           // 发送者ID
    content: data.content,     // 消息内容
    timestamp: data.timestamp  // 时间戳
  });

  // 只处理已加入群组的消息
  if (groupManager.joinedGroups.has(data.groupId)) {
    displayGroupMessage(data);
  }
});

// 显示群组消息
function displayGroupMessage(data) {
  const messagesContainer = document.getElementById(`messages-${data.groupId}`);
  if (!messagesContainer) return;

  const messageElement = document.createElement('div');
  messageElement.className = 'group-message';
  messageElement.innerHTML = `
    <div class="message-header">
      <span class="group-name">[${data.groupId}]</span>
      <span class="sender">${data.from}</span>
      <span class="timestamp">${new Date(data.timestamp).toLocaleTimeString()}</span>
    </div>
    <div class="message-content">${data.content}</div>
  `;

  messagesContainer.appendChild(messageElement);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}
```

## 🎯 完整的群组管理示例

### 群组聊天组件
```javascript
class GroupChatComponent {
  constructor(socket) {
    this.socket = socket;
    this.groupManager = new AdvancedGroupManager(socket);
    this.currentGroup = null;
    
    this.initUI();
    this.bindEvents();
  }

  // 初始化UI
  initUI() {
    this.createGroupListUI();
    this.createChatUI();
    this.createMemberListUI();
  }

  // 绑定事件
  bindEvents() {
    // 群组管理器事件
    this.groupManager.onGroupJoined = (groupId, members) => {
      this.addGroupToList(groupId, members);
      this.showNotification(`已加入群组: ${groupId}`);
    };

    this.groupManager.onGroupLeft = (groupId) => {
      this.removeGroupFromList(groupId);
      if (this.currentGroup === groupId) {
        this.currentGroup = null;
        this.clearChatArea();
      }
      this.showNotification(`已离开群组: ${groupId}`);
    };

    this.groupManager.onMemberJoined = (groupId, members) => {
      this.updateMembersList(groupId, members);
    };

    this.groupManager.onMemberLeft = (groupId, members) => {
      this.updateMembersList(groupId, members);
    };

    // 消息事件
    this.socket.on('group_message', (data) => {
      if (data.groupId === this.currentGroup) {
        this.displayMessage(data);
      }
      this.updateGroupLastMessage(data.groupId, data);
    });
  }

  // 加入群组
  async joinGroup(groupId) {
    try {
      await this.groupManager.joinGroup(groupId);
      this.switchToGroup(groupId);
    } catch (error) {
      this.showError(`加入群组失败: ${error.message}`);
    }
  }

  // 离开群组
  leaveGroup(groupId) {
    this.groupManager.leaveGroup(groupId);
  }

  // 切换到指定群组
  switchToGroup(groupId) {
    if (!this.groupManager.joinedGroups.has(groupId)) {
      console.error(`未加入群组: ${groupId}`);
      return;
    }

    this.currentGroup = groupId;
    this.updateChatHeader(groupId);
    this.loadGroupMessages(groupId);
    this.updateMembersList(groupId, this.groupManager.groupMembers.get(groupId));
  }

  // 发送消息
  sendMessage(content) {
    if (!this.currentGroup) {
      this.showError('请先选择一个群组');
      return;
    }

    if (this.groupManager.sendGroupMessage(this.currentGroup, content)) {
      this.clearMessageInput();
    } else {
      this.showError('消息发送失败');
    }
  }

  // UI更新方法
  addGroupToList(groupId, members) {
    // 实现群组列表UI更新
  }

  removeGroupFromList(groupId) {
    // 实现群组列表UI更新
  }

  updateMembersList(groupId, members) {
    // 实现成员列表UI更新
  }

  displayMessage(data) {
    // 实现消息显示UI更新
  }

  showNotification(message) {
    // 实现通知显示
  }

  showError(message) {
    // 实现错误显示
  }
}

// 使用示例
const groupChat = new GroupChatComponent(socket);

// 加入一些群组
groupChat.joinGroup('general');
groupChat.joinGroup('developers');
groupChat.joinGroup('project-123');
```

## ⚠️ 注意事项

### 1. 群组限制
- 用户可以同时加入多个群组
- 群组ID必须是有效的字符串
- 发送群组消息前必须先加入群组

### 2. 成员管理
- 成员列表由服务器维护
- 成员变化会实时通知所有群组成员
- 断开连接会自动离开所有群组

### 3. 消息处理
- 只能接收已加入群组的消息
- 群组消息会广播给所有群组成员
- 建议实现消息历史记录功能

### 4. 性能考虑
- 避免频繁加入/离开群组
- 合理管理群组数量
- 实现消息缓存机制

## 🔗 相关文档

- [连接与认证](./01-connection-auth.md)
- [消息传递功能](./02-messaging.md)
- [用户状态管理](./03-user-status.md)
- [错误处理](./05-error-handling.md)
