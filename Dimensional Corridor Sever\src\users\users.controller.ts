import {
  Controller,
  Post,
  Body,
  Req,
  Put,
  Param,
  Get,
  Delete,
  HttpException,
  Query,
} from '@nestjs/common';
import { UsersService } from './service/users.service'; // 导入UserService，用于处理用户相关的业务逻辑
import { CreateUserDto } from './dtos/create-user.dto'; // 导入CreateUserDto，用于创建用户的请求体验证
import { Request, Response } from 'express'; // 导入Express的Response对象，用于手动控制HTTP响应
import { Public } from 'src/common/decorator/public.decorator';
import { formatResponse } from 'src/common/utils';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { UpdateUserDto } from './dtos/update-user.dto';
import { plainToClass } from 'class-transformer';
import { UserProfileInfoDto } from './filter-dtos/user-profile.dto';
import { PublicUserInfoDto } from './filter-dtos/user-public.dto';
import { ResetUserDto } from './dtos/reset-password.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { UpdatePhoneDto } from './dtos/update-phone.dto';
import { UpdateEmailDto } from './dtos/update-email.dto';
import { UserSearchInfoDto } from './filter-dtos/user-search.dto';
import { SearchUserDto } from './dtos/search-user.dto';
import { SocialService } from './service/social.service';
import { UpdateUserSettingDto } from './dtos/update-user-setting.dto';
import { CaptchaService } from 'src/captcha/captcha.service';
import { PhotoListInfoDto } from 'src/photos/filters/photo-list.dto';
import { ContentListInfoDto } from 'src/contents/filters/content-list.dto';

// 定义UserController类，并使用@Controller装饰器指定路由前缀为'user'
@Controller('users')
export class UsersController {
  // 构造函数，注入UserService实例
  constructor(
    private readonly userService: UsersService,
    private readonly socialService: SocialService,
    private readonly captchaService: CaptchaService
  ) {}

  @ApiOperation({ summary: '用户注册-公开路由' })
  @ApiOkResponse({ description: '创建成功返回基础信息' })
  // !: 定义POST请求处理方法，用于创建用户
  @Public()
  @Post()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    // 使用UserService的create方法处理创建用户逻辑
    const user = await this.userService.create(createUserDto, req.ip || '');

    if (createUserDto.email) {
      this.captchaService.sendWelcomeEmail(user.email);
    }

    // 自动关注管理员
    await this.socialService.toggleFollowUser(user.id, '000000');

    return formatResponse(
      HttpStatusEnum.OK,
      '创建成功',
      plainToClass(PublicUserInfoDto, user, { excludeExtraneousValues: true })
    );
  }

  @ApiOperation({ summary: '用户信息更新-用户路由' })
  @ApiOkResponse({ description: '更新成功返回更新成功后的信息' })
  // !: 定义PUT请求处理方法，用于更新用户信息
  @Put('info')
  async update(@Body() updateUserDto: UpdateUserDto, @Req() req: Request) {
    // 使用UserService的update方法处理更新用户信息逻辑，并返回结果
    const user = await this.userService.updateAll(req.user.id, updateUserDto);

    return formatResponse(
      HttpStatusEnum.OK,
      '更新成功',
      plainToClass(UserProfileInfoDto, user, { excludeExtraneousValues: true })
    );
  }

  @ApiOperation({ summary: '全部用户获取指定用户的用户信息-公开路由' })
  @ApiOkResponse({ description: '获取成功返回指定用户信息' })
  // !: 定义GET请求处理方法，用于获取其他用户信息
  @Public()
  @Get('info/:uid')
  async findOne(@Param('uid') uid: string, @Req() req: Request) {
    // 使用UserService的findOneById方法处理获取用户信息逻辑，并返回结果
    const targetUser = await this.userService.findOneByUid(uid);
    const user = await this.userService.getUserProfile(
      targetUser.id,
      req.user ? req.user.id : undefined
    );
    this.userService.increaseVisitors(targetUser.id);

    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(PublicUserInfoDto, user, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '获取用户自己的用户信息-用户路由' })
  @ApiOkResponse({ description: '获取成功返回自己的用户信息' })
  // !: 定义GET请求处理方法，用于获取自己的用户信息
  @Get('info')
  async findMe(@Req() req: Request) {
    // 使用UserService的findOneById方法处理获取用户信息逻辑，并返回结果
    const user = await this.userService.getUserProfile(req.user.id);

    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(UserProfileInfoDto, user, { excludeExtraneousValues: true })
    );
  }

  @ApiOperation({ summary: '全部用户模糊查询搜索用户-公开路由' })
  @ApiOkResponse({ description: '获取成功返回用户列表' })
  // !: 定义GET请求处理方法，用于获取用户列表，用于搜索功能
  @Public()
  @Get('list/search')
  async findListSearch(
    @Query() searchUserDto: SearchUserDto,
    @Req() req: Request
  ) {
    const query = { ...searchUserDto };

    if (searchUserDto.keyword) {
      // 关键字检查
      // 如果是uid格式，则直接搜索用户
      if (/^\d+$/.test(searchUserDto.keyword)) {
        query['uid'] = searchUserDto.keyword;
      }
      query['nickname'] = searchUserDto.keyword;
    }

    // 使用UserService的findList方法处理获取用户列表逻辑，并返回结果
    const data = await this.userService.findListSearch(
      {
        ...query,
      },
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((user) =>
        plainToClass(UserSearchInfoDto, user, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '推荐用户列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回推荐用户列表' })
  // !: 定义GET请求处理方法，用于推荐用户列表
  @Get('list/recommend')
  @Public()
  async findListRecommend(
    @Query('number') number: number,
    @Req() req: Request
  ) {
    // 使用UserService的findList方法处理获取推荐用户列表逻辑，并返回结果
    const list = await this.userService.getRandomUsers(
      number || 10,
      req.user ? req.user.id : undefined
    );

    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      list.map((user) =>
        plainToClass(UserSearchInfoDto, user, {
          excludeExtraneousValues: true,
        })
      )
    );
  }

  @ApiOperation({ summary: '用户删除-用户路由' })
  @ApiOkResponse({ description: '删除成功' })
  // !: 定义DELETE请求处理方法，用于删除用户
  @Delete()
  async delete(@Req() req: Request) {
    await this.userService.delete(req.user.id);

    return formatResponse(HttpStatusEnum.OK, '删除成功');
  }

  @ApiOperation({ summary: '找回密码-公开路由' })
  @ApiOkResponse({ description: '密码修改成功' })
  // !: 定义PUT请求处理方法，用于找回密码
  @Put('reset-password')
  @Public()
  async resetPassword(@Body() resetuserdto: ResetUserDto) {
    await this.userService.resetPassword(resetuserdto.password, {
      type: resetuserdto.email ? 'email' : 'phone',
      value: resetuserdto.email || resetuserdto.phone || '',
      code: resetuserdto.code,
    });
    return formatResponse(HttpStatusEnum.OK, '密码修改成功');
  }

  @ApiOperation({ summary: '修改密码-用户路由' })
  @ApiOkResponse({ description: '密码修改成功' })
  // !: 定义PUT请求处理方法，用于更改密码
  @Put('change-password')
  async changePassword(
    @Body() updatePasswordDto: UpdatePasswordDto,
    @Req() req: Request
  ) {
    if (
      updatePasswordDto.newPassword !== updatePasswordDto.confirmNewPassword
    ) {
      return formatResponse(HttpStatusEnum.BAD_REQUEST, '两次输入的密码不一致');
    }
    await this.userService.changePassword(
      req.user.id,
      updatePasswordDto.oldPassword,
      updatePasswordDto.newPassword
    );
    return formatResponse(HttpStatusEnum.OK, '密码修改成功');
  }

  @ApiOperation({ summary: '更新用户邮箱-用户路由' })
  @ApiOkResponse({ description: '邮箱更新成功' })
  // !: 定义PUT请求处理方法，用于更新用户邮箱
  @Put('update-email')
  async updateEmail(
    @Body() updateUserDto: UpdateEmailDto,
    @Req() req: Request
  ) {
    if (updateUserDto.email === updateUserDto.oldEmail) {
      throw new HttpException('新邮箱与旧邮箱相同', HttpStatusEnum.BAD_REQUEST);
    }

    await this.userService.updateEmail(
      req.user.id,
      updateUserDto.oldEmail,
      updateUserDto.email,
      updateUserDto.code
    );

    return formatResponse(HttpStatusEnum.OK, '邮箱更新成功');
  }

  @ApiOperation({ summary: '更新用户手机号-用户路由' })
  @ApiOkResponse({ description: '手机号更新成功' })
  // !: 定义PUT请求处理方法，用于更新用户手机号
  @Put('update-phone')
  async updatePhone(
    @Body() updateUserDto: UpdatePhoneDto,
    @Req() req: Request
  ) {
    if (updateUserDto.phone === updateUserDto.oldPhone) {
      throw new HttpException(
        '新手机号与旧手机号相同',
        HttpStatusEnum.BAD_REQUEST
      );
    }

    await this.userService.updatePhone(
      req.user.id,
      updateUserDto.oldPhone,
      updateUserDto.phone,
      updateUserDto.code
    );

    return formatResponse(HttpStatusEnum.OK, '手机号更新成功');
  }

  @ApiOperation({ summary: '获取用户设置-用户路由' })
  @ApiOkResponse({ description: '获取成功返回用户设置' })
  // !: 定义GET请求处理方法，用于获取用户设置
  @Get('settings')
  @Public()
  async getSettings(@Query('uid') uid: string) {
    const setting = await this.userService.getSetting(uid);
    return formatResponse(HttpStatusEnum.OK, '获取成功', setting);
  }

  @ApiOperation({ summary: '修改用户设置-用户路由' })
  @ApiOkResponse({ description: '设置更新成功' })
  // !: 定义PUT请求处理方法，用于修改用户设置
  @Put('settings')
  async updateSettings(
    @Body() updateUserSettingDto: UpdateUserSettingDto,
    @Req() req: Request
  ) {
    const setting = await this.userService.updateSetting(
      req.user.id,
      updateUserSettingDto
    );

    return formatResponse(HttpStatusEnum.OK, '设置更新成功', setting);
  }

  @ApiOperation({ summary: '关注或取消关注用户-用户路由' })
  @ApiOkResponse({ description: '操作成功' })
  // !: 定义POST请求处理方法，用于关注或取消关注用户
  @Post('follow/:uid')
  async follow(@Param('uid') uid: string, @Req() req: Request) {
    const flag = await this.socialService.toggleFollowUser(req.user.id, uid);

    return formatResponse(
      HttpStatusEnum.OK,
      flag ? '关注成功' : '取消关注成功'
    );
  }

  @ApiOperation({ summary: '获取关注用户列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回关注用户列表' })
  // !: 定义GET请求处理方法，用于获取关注用户列表
  @Get('followings/:uid')
  @Public()
  async getFollowings(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getFollowings(
      uid,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((user) =>
        plainToClass(UserSearchInfoDto, user, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取粉丝列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回粉丝列表' })
  // !: 定义GET请求处理方法，用于获取粉丝列表
  @Get('fans/:uid')
  @Public()
  async getFans(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getFollowers(
      uid,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((user) =>
        plainToClass(UserSearchInfoDto, user, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取用户点赞图片列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回用户点赞图片列表' })
  // !: 定义GET请求处理方法，用于获取用户点赞图片列表
  @Get('photos/like/:uid')
  @Public()
  async getImagesLike(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getPhotosLikes(
      uid,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((photo) =>
        plainToClass(PhotoListInfoDto, photo, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取用户收藏图片列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回用户收藏图片列表' })
  // !: 定义GET请求处理方法，用于获取用户收藏的图片列表
  @Get('photos/collect/:uid')
  @Public()
  async getImagesCollect(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getPhotosFavorites(
      uid,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((photo) =>
        plainToClass(PhotoListInfoDto, photo, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取用户点赞帖子列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回用户点赞帖子列表' })
  // !: 定义GET请求处理方法，用于获取用户点赞的帖子列表
  @Get('contents/like/:uid')
  @Public()
  async getContentsLike(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getContentsLikes(
      uid,
      req.user ? req.user.id : undefined
    );
    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((content) => {
        return plainToClass(ContentListInfoDto, content, {
          excludeExtraneousValues: true,
        });
      }),
    });
  }

  @ApiOperation({ summary: '获取用户收藏帖子列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回用户收藏帖子列表' })
  // !: 定义GET请求处理方法，用于获取用户收藏的帖子列表
  @Get('contents/collect/:uid')
  @Public()
  async getContentsCollect(@Param('uid') uid: string, @Req() req: Request) {
    const data = await this.socialService.getContentsFavorites(
      uid,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((content) => {
        return plainToClass(ContentListInfoDto, content, {
          excludeExtraneousValues: true,
        });
      }),
    });
  }
}
