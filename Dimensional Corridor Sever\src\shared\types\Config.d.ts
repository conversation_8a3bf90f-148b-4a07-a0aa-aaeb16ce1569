interface ServerConfig {
  /** 服务器监听的端口 */
  port: number;
}

interface DatabaseConfig {
  /** 数据库主机 */
  host: string;
  /** 数据库端口 */
  port: number;
  /** 数据库名称 */
  name: string;
  /** 数据库用户名 */
  username: string;
  /** 数据库密码 */
  password: string;
}

interface RedisConfig {
  /** Redis 主机 */
  host: string;
  /** Redis 端口 */
  port: number;
  /** Redis 密码 */
  password: string;
}

interface JwtConfig {
  /** Jwt 密钥 */
  secret: string;
  /** Jwt 过期时间 */
  expiresIn: string;
}

interface EmailConfig {
  /** 应用名称 */
  appName: string;
  /** 客服邮箱 */
  supportEmail: string;
  /** 退订处理 */
  unsubscribeUrl: string;
  /** 邮箱服务地址  */
  host: string;
  /** 端口 */
  port: number;
  /** 是否使用 SSL */
  secure: boolean;
  /** 用户名 */
  user: string;
  /** 密码 */
  pass: string;
  /** 发件人 */
  from: string;
}

interface TwilioConfig {
  /** 账号 SID */
  accountSid: string;
  /** 授权令牌 */
  authToken: string;
  /** 短信发送者 */
  phone: string;
}

interface DysmsapiConfig {
  /** 短信应用 ID */
  accessKeyId: string;
  /** 短信应用密钥 */
  accessKeySecret: string;
  /** 短信签名 */
  signName: string;
  /** 短信模板代码 */
  templateCode: string;
}

interface WeChatConfig {
  /** 微信公众平台 APPID */
  appID: string;
  /** 微信公众平台 APPSECRET */
  appSecret: string;
  /** 微信公众平台 回调地址 */
  callbackUrl: string;
}

interface SwaggerConfig {
  /** 文档标题 */
  title: string;
  /** 文档描述 */
  description: string;
  /** 文档版本 */
  version: string;
  /** 文档路径 */
  path: string;
}

interface DeepSeekChatConfig {
  /** DeepSeekChat API 密钥 */
  apiKey: string;
  /** DeepSeekChat API 基础 URL */
  baseUrl: string;
  /** ai 指定身份提示词 */
  aiPrompt: string;
}

/** 超级管理员信息初始化配置 */
interface DefaultAdminConfig {
  /** 管理员uid */
  uid: string;
  /** 管理员昵称 */
  nickname: string;
  /** 管理员邮箱 */
  email: string;
  /** 管理员手机号 */
  phone: string;
  /** 管理员密码 */
  password: string;
  /** 注册IP */
  registerIP: string;
}

interface ConfigConfig {
  /** 服务器配置 */
  server: ServerConfig;
  /** 数据库配置 */
  database: DatabaseConfig;
  /** Redis 配置 */
  redis: RedisConfig;
  /** Jwt 配置 */
  jwt: JwtConfig;
  /** 邮箱配置 */
  email: EmailConfig;
  /** 短信配置 */
  sms: SmsConfig;
  /** 微信配置 */
  wechat: WeChatConfig;
  /** Swagger 配置 */
  swagger: SwaggerConfig;
  /** DeepSeekChat 配置 */
  deepSeekChat: DeepSeekChatConfig;
  /** 默认管理员配置 */
  defaultAdmin: DefaultAdminConfig;
}
