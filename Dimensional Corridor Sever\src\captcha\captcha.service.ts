import { HttpException, Injectable } from '@nestjs/common';
import { RedisService } from 'src/redis/redis.service';
import { v4 as uuidv4 } from 'uuid';
import * as svgCaptcha from 'svg-captcha';
import * as config from 'config'; // 引入配置模块
import * as Twilio from 'twilio';
import * as Dysmsapi from '@alicloud/dysmsapi20170525';
import * as OpenApi from '@alicloud/openapi-client';
import * as Util from '@alicloud/tea-util';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { MailerService } from '@nestjs-modules/mailer';
import { UnsubscribeEmail } from './schemas/unsubscribeEmail.schemas';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

const twilioConfig = config.get<TwilioConfig>('twilio'); // 读取 Twilio 配置
const dysmsapiConfig = config.get<DysmsapiConfig>('dysmsapi'); // 读取 阿里云短信服务 配置
const emailConfig = config.get<EmailConfig>('email'); // 获取邮箱相关配置

@Injectable()
export class CaptchaService {
  private twilioClient: Twilio.Twilio;
  private dysmsapiClient: Dysmsapi.default;

  constructor(
    @InjectModel(UnsubscribeEmail.name)
    private unsubscribeEmailModel: Model<UnsubscribeEmail>, // 注入 UnsubscribeEmail 模型
    private readonly redisService: RedisService, // 注入 RedisService
    private readonly mailerService: MailerService // 注入 MailerService
  ) {
    // 实例化 Twilio 客户端
    this.twilioClient = Twilio(twilioConfig.accountSid, twilioConfig.authToken);

    // 实例化 阿里云短信服务 客户端
    const config: OpenApi.Config = new OpenApi.Config({
      accessKeyId: dysmsapiConfig.accessKeyId,
      accessKeySecret: dysmsapiConfig.accessKeySecret,
      endpoint: 'dysmsapi.aliyuncs.com', // 明确指定服务端点
    });

    this.dysmsapiClient = new Dysmsapi.default(config);
  }

  /**
   * ?: 图形验证码获取
   * @returns {{ svg_token: string; svg_code: string }} 返回包含 svg_token 和 svg_code 的对象
   * @throws HttpException 图形验证码获取失败
   * @description 该接口用于获取图形验证码，svg_token 用于唯一标识，svg_code 用于前端显示的svg验证码
   */
  getCaptcha() {
    const token = uuidv4(); // 唯一标识
    const captcha = svgCaptcha.create({
      size: 4, // 验证码长度
      noise: 3, // 干扰线数量
      color: true, // 彩色字符
      ignoreChars: '0o1i', // 排除易混淆字符
      background: '#f6f6f6', // 背景颜色
      width: 120, // 图片宽度
      height: 40, // 图片高度
    });

    this.redisService.set(
      `svg_token:${token}`,
      captcha.text.toLowerCase(),
      60 * 5
    ); // 设置 Redis 缓存，过期时间为 5 分钟

    return {
      svg_token: token,
      svg_code: captcha.data,
    };
  }

  /**
   * ?: 图形验证码验证
   * @param svg_token 图形验证码唯一标识
   * @param svg_code 用户输入的验证码
   * @returns void
   * @throws HttpException 图形验证码验证失败
   * @description 该接口用于验证用户输入的图形验证码是否正确，正确则返回 void，错误则抛出 HttpException
   */
  async verifyCaptcha(svg_token: string, svg_code: string) {
    // 获取 Redis 缓存的验证码
    const captcha = await this.redisService.get(`svg_token:${svg_token}`);
    if (!captcha) {
      // 如果验证码已过期，抛出未授权异常
      throw new HttpException('验证码已过期', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证验证码是否正确
    if (captcha.toLowerCase() !== svg_code.toLowerCase()) {
      // 如果验证码不正确，抛出未授权异常
      throw new HttpException('验证码不正确', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证码正确，删除 Redis 缓存的验证码
    this.redisService.del(`svg_token:${svg_token}`);
  }

  /**
   * ?: 发送邮件
   * @param email 用户邮箱
   * @param title 邮件标题
   * @param template 邮件模板
   * @param context 邮件模板变量
   */
  async sendEmail(
    email: string,
    title: string,
    template: EmailTemplate = 'captcha',
    context: {
      [name: string]: any;
    }
  ) {
    // 检查邮箱是否已退订
    const unsubscribe = await this.unsubscribeEmailModel.findOne({ email });
    if (unsubscribe) {
      throw new HttpException(
        '邮箱已退订，请联系客服',
        HttpStatusEnum.BAD_REQUEST
      );
    }

    // 检查模板是否为需要限制发送频率的模板
    if (['captcha'].includes(template)) {
      // 检查是否频繁发送（60秒内只能发送一次）
      const lastSent = await this.redisService.get(`email_cooldown:${email}`);
      if (lastSent) {
        throw new HttpException(
          '邮件发送频繁，请稍后再试',
          HttpStatusEnum.TOO_MANY_REQUESTS
        );
      }
    }

    // 生成一个退订连接，使用redius缓存
    const unsubscribeToken = uuidv4();
    await this.redisService.set(
      `unsubscribe_token:${unsubscribeToken}`,
      email,
      60 * 60 // h过期
    ); // 设置 Redis 缓存

    // 发送验证码
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: title,
        template, // 对应模板文件名
        context: {
          appName: emailConfig.appName, // 应用名称
          supportEmail: emailConfig.supportEmail, // 客服邮箱
          unsubscribeUrl: emailConfig.unsubscribeUrl + '/' + unsubscribeToken, // 退订链接
          year: new Date().getFullYear(), // 年份
          ...context,
        }, // 模板变量
        from: emailConfig.from, // 发件人邮箱
      });
      if (['captcha'].includes(template)) {
        await this.redisService.set(`email_cooldown:${email}`, 60, 60); // 设置冷却时间，60秒内只能发送一次
      }
    } catch (err) {
      console.error(err);
      throw new HttpException(
        `邮件发送失败`,
        HttpStatusEnum.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * ?: 退订邮件
   * @param email 用户邮箱
   * @returns void
   * @description 该接口用于退订用户的邮件
   */
  async unsubscribeEmail(unsubscribeToken: string) {
    // 检查redius缓存中是否有该邮箱的退订token
    const email = await this.redisService.get(
      `unsubscribe_token:${unsubscribeToken}`
    );
    if (!email) {
      throw new HttpException('无效的退订链接', HttpStatusEnum.BAD_REQUEST);
    }

    const unsubscribe = await this.unsubscribeEmailModel.findOne({ email });
    if (unsubscribe) {
      throw new HttpException('您已退订', HttpStatusEnum.BAD_REQUEST);
    }

    await this.unsubscribeEmailModel.create({ email });
  }

  /**
   * ?: 重新订阅邮件
   * @param email 用户邮箱
   * @returns void
   * @description 该接口用于重新订阅用户的邮件
   */
  async subscribeEmail(email: string) {
    const unsubscribe = await this.unsubscribeEmailModel.findOne({ email });
    if (!unsubscribe) {
      throw new HttpException('您已订阅', HttpStatusEnum.BAD_REQUEST);
    }

    await unsubscribe.deleteOne();
  }

  /**
   * ?: 发送验证邮件
   * @param email 用户邮箱
   * @param template 邮件模板
   * @returns void
   * @throws HttpException 邮件发送失败
   * @description 该接口用于发送验证邮件，包含验证码，5分钟内有效
   */
  async sendEmailCaptcha(email: string, template: EmailTemplate = 'captcha') {
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    // 生成 6 位随机字符串
    await this.sendEmail(email, '请查收您的验证码！', template, {
      email,
      code,
    });

    // 设置 Redis 缓存，缓存验证码，过期时间为 10 分钟
    await this.redisService.set(`email_token:${email}`, code, 60 * 10); // 设置 Redis 缓存，过期时间为 10 分钟

    return {
      email_code: code,
    };
  }

  /**
   * ?: 用户注册成功，邮箱发送欢迎邮件
   * @param email 用户邮箱
   * @returns void
   * @description 该接口用于用户注册成功后，发送欢迎邮件
   */
  async sendWelcomeEmail(email: string) {
    await this.sendEmail(email, '欢迎注册！', 'welcome', {
      email,
    });
  }

  /**
   * ?: 验证邮箱
   * @param email 用户邮箱
   * @param email_code 用户输入的验证码
   * @returns void
   * @throws HttpException 验证码验证失败
   * @description 该接口用于验证用户输入的验证码是否正确，正确则返回 void，错误则抛出 HttpException
   */
  async verifyEmail(email: string, email_code: string) {
    // 获取 Redis 缓存的验证码
    const code = await this.redisService.get(`email_token:${email}`);

    if (!code) {
      // 如果验证码已过期，抛出未授权异常
      throw new HttpException('验证码已过期', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证验证码是否正确
    if (code !== email_code) {
      // 如果验证码不正确，抛出未授权异常
      throw new HttpException('验证码不正确', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证码正确，删除 Redis 缓存的验证码
    this.redisService.del(`email_token:${email}`);
  }

  /**
   * ?: 发送短信验证码
   * @param phone 用户手机号
   * @returns void
   * @throws HttpException 短信发送失败
   * @description 该接口用于发送短信验证码，包含验证码，5分钟内有效
   */
  async sendSMS(phone: string) {
    // 检查是否频繁发送（如60秒内只能发送一次）
    const lastSent = await this.redisService.get(`sms_cooldown:${phone}`);
    if (lastSent) {
      throw new HttpException(
        '短信发送频繁，请稍后再试',
        HttpStatusEnum.TOO_MANY_REQUESTS
      );
    }

    const code = Math.floor(100000 + Math.random() * 900000).toString();

    try {
      // twilio 发送短信
      await this.twilioClient.messages.create({
        body: `验证码：${code}，5分钟内有效。`,
        from: twilioConfig.phone,
        to: phone,
      });

      await this.redisService.set(`sms_token:${phone}`, code, 60 * 5); // 设置 Redis 缓存，过期时间为 5 分钟
      await this.redisService.set(`sms_cooldown:${phone}`, code, 60); // 设置冷却时间，60秒内只能发送一次

      return {
        sms_code: code,
      };
    } catch (err) {
      // 发送短信失败，抛出未授权异常
      console.error(err);
      throw new HttpException(
        '短信发送失败',
        HttpStatusEnum.INTERNAL_SERVER_ERROR
      );
    }

    try {
      // 阿里云短信服务 发送短信
      const request = new Dysmsapi.SendSmsRequest({
        phoneNumbers: phone,
        signName: dysmsapiConfig.signName,
        templateCode: dysmsapiConfig.templateCode,
        templateParam: JSON.stringify({ code }),
      });

      const runtime = new Util.RuntimeOptions({});
      const response = await this.dysmsapiClient.sendSmsWithOptions(
        request,
        runtime
      );

      if (response.body?.code === 'OK') {
        await this.redisService.set(`sms_token:${phone}`, code, 60 * 5); // 设置 Redis 缓存，过期时间为 5 分钟
        await this.redisService.set(`sms_cooldown:${phone}`, code, 60); // 设置冷却时间，60秒内只能发送一次

        return {
          sms_code: code,
        };
      } else {
        throw new HttpException(
          response.body?.message || '短信发送失败',
          HttpStatusEnum.INTERNAL_SERVER_ERROR
        );
        return false;
      }
    } catch (err) {
      // 发送短信失败，抛出未授权异常
      console.error(err);
      throw new HttpException(
        '短信发送失败',
        HttpStatusEnum.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * ?: 短信验证码验证
   * @param phone 用户手机号
   * @param sms_code 用户输入的验证码
   * @returns void
   * @throws HttpException 验证码验证失败
   * @description 该接口用于验证用户输入的验证码是否正确，正确则返回 void，错误则抛出 HttpException
   */
  async verifySMS(phone: string, sms_code: string) {
    // 获取 Redis 缓存的验证码
    const code = await this.redisService.get(`sms_token:${phone}`); // 获取 Redis 缓存的验证码
    if (!code) {
      // 如果验证码已过期，抛出未授权异常
      throw new HttpException('验证码已过期', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证验证码是否正确
    if (code !== sms_code) {
      // 如果验证码不正确，抛出未授权异常
      throw new HttpException('验证码不正确', HttpStatusEnum.BAD_REQUEST);
    }

    // 验证码正确，删除 Redis 缓存的验证码
    this.redisService.del(`sms_token:${phone}`); // 删除 Redis 缓存的验证码
    this.redisService.del(`sms_cooldown:${phone}`); // 删除冷却时间

    return true;
  }
}
