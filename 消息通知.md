### 一、后端设计方案（NestJS）

1. **通知服务模块**

```typescript
// notification.module.ts
@Module({
  imports: [MongooseModule.forFeature([{ name: 'Notification', schema: NotificationSchema }])],
  providers: [NotificationService, NotificationGateway],
  exports: [NotificationService],
})
export class NotificationModule {}

// 通知模型
@Schema({ timestamps: true })
export class Notification {
  @Prop({ required: true }) // 接收用户ID
  recipient: string

  @Prop({ required: true, enum: ['like', 'favorite', 'download', 'follow'] })
  type: string

  @Prop({ required: true }) // 触发用户ID
  sender: string

  @Prop() // 关联内容ID（图片/帖子）
  contentId?: string

  @Prop({ default: false })
  read: boolean
}
```

2. **Socket.IO 网关实现**

```typescript
// notification.gateway.ts
@WebSocketGateway({ namespace: 'notifications' })
export class NotificationGateway {
  @WebSocketServer()
  server: Server

  // 用户连接时存储Socket映射
  handleConnection(client: Socket, @Query() query: { userId: string }) {
    client.join(`user_${query.userId}`)
  }

  // 发送通知方法
  sendNotification(userId: string, payload: any) {
    this.server.to(`user_${userId}`).emit('new-notification', payload)
  }
}
```

3. **事件触发服务**

```typescript
// notification.service.ts
@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(Notification.name) private notiModel: Model<Notification>,
    private notificationGateway: NotificationGateway
  ) {}

  async createNotification(data: CreateNotiDto) {
    const notification = await this.notiModel.create(data)

    // 实时推送
    this.notificationGateway.sendNotification(data.recipient, {
      type: data.type,
      sender: data.sender,
      contentId: data.contentId,
    })

    return notification
  }
}

// 在点赞/收藏等服务中调用
@Injectable()
export class LikeService {
  constructor(private notiService: NotificationService) {}

  async likePost(userId: string, postId: string) {
    // ...点赞逻辑

    // 触发通知 (排除自己操作)
    if (postOwner !== userId) {
      await this.notiService.createNotification({
        type: 'like',
        sender: userId,
        recipient: postOwner,
        contentId: postId,
      })
    }
  }
}
```

### 二、前端实现方案（Nuxt.js）

1. **Socket.IO 客户端集成**

```javascript
// plugins/socket.client.js
export default defineNuxtPlugin(() => {
  const user = useAuthStore().user
  if (!user) return

  const socket = io('https://api.sixflower.love/notifications', {
    transports: ['websocket'],
    query: { userId: user._id },
  })

  // 监听通知事件
  socket.on('new-notification', (data) => {
    useNotificationStore().addNotification(data)
    // 播放提示音/显示Toast
  })
})
```

2. **Pinia 状态管理**

```javascript
// stores/notification.js
export const useNotificationStore = defineStore('notification', {
  state: () => ({
    unreadCount: 0,
    notifications: [],
  }),
  actions: {
    addNotification(noti) {
      this.notifications.unshift(noti)
      this.unreadCount++
    },
    markAsRead(ids) {
      // 调用API标记已读
    },
  },
})
```

3. **通知中心组件**

```vue
<!-- components/NotificationBell.vue -->
<template>
  <div @click="openPanel">
    <Icon name="ph:bell" />
    <span v-if="unreadCount">{{ unreadCount }}</span>
  </div>

  <div
    v-show="isOpen"
    class="notification-panel">
    <div
      v-for="noti in notifications"
      :key="noti._id">
      <div :class="{ unread: !noti.read }">
        <Avatar :user="noti.sender" />
        <span v-if="noti.type === 'like'"> {{ noti.sender.name }} 点赞了你的作品 </span>
        <!-- 其他通知类型 -->
      </div>
    </div>
  </div>
</template>
```

### 三、系统架构设计

```mermaid
graph LR
A[用户操作] --> B[业务服务]
B --> C{需要通知?}
C -->|是| D[通知服务]
D --> E[保存到MongoDB]
D --> F[Socket.IO网关]
F --> G[Redis适配器]
G --> H[用户Socket连接]
H --> I[前端实时展示]
```

### 四、进阶优化建议

1. **性能优化**

   - 使用 Redis 发布/订阅替代直接广播
   - 通知聚合（如："3 人点赞了你的图片"）

   ```typescript
   // 示例聚合逻辑
   const aggregateNotifications = async (userId) => {
     return this.notiModel.aggregate([
       { $match: { recipient: userId, read: false } },
       {
         $group: {
           _id: { type: '$type', contentId: '$contentId' },
           count: { $sum: 1 },
           senders: { $push: '$sender' },
         },
       },
     ])
   }
   ```

2. **通知降噪策略**

   - 用户设置中心添加通知偏好设置
   - 频率限制（同用户 5 分钟内不重复提醒）
   - 重要度分级（下载 > 收藏 > 点赞）

3. **离线用户处理**

   - 登录时拉取未读通知
   - 移动端 PWA 后台同步
   - 邮件/SMS 备用通道（重要通知）

4. **安全防护**
   - WebSocket 连接 JWT 认证
   ```typescript
   // Socket网关认证
   @UseGuards(WsJwtGuard)
   handleConnection(client: Socket) {...}
   ```
   - 通知内容 XSS 过滤
   - 频率限制（@nestjs/throttler）

### 五、部署建议

1. **Socket.IO 集群方案**

```bash
# 使用Redis适配器
npm install @socket.io/redis-adapter redis
```

```typescript
// gateway配置
const pubClient = createClient({ host: 'redis-server' });
const subClient = pubClient.duplicate();

@WebSocketGateway({
  adapter: new RedisAdapter(pubClient, subClient)
})
```

2. **性能监控**
   - 添加 Socket 连接数指标
   - 通知延迟监控（从操作到送达时间）
   - 错误日志跟踪（失败通知记录）

### 六、扩展可能性

1. **结合 AI 功能**

   - 智能通知分类（"你可能感兴趣的新作品"）
   - 垃圾通知自动过滤（使用文本分析）

2. **多设备同步**

   - 使用 Firebase Cloud Messaging（FCM）实现移动推送
   - WebSocket 连接与移动推送的优雅降级

3. **数据分析看板**
   - 用户互动热力图
   - 通知点击转化率统计
