import { PhotoAttributesDto } from './create-photo.dto';
import {
  IsArray,
  IsOptional,
  IsString,
  Length,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { PhotoStatusEnum } from 'src/shared/enums/Photo.enum';

export class UpdatePhotoDto {
  /** 图片名 */
  @ApiProperty({ description: '图片名' })
  @IsOptional()
  @IsString({ message: '图片名必须是字符串' })
  @Length(1, 30, { message: '图片名长度不能超过30' })
  filename: string;

  /** 分类 */
  @ApiProperty({ description: '图片分类' })
  @IsOptional()
  @IsString({ message: '分类必须是字符串' })
  @Length(1, 10, { message: '分类长度不能超过10' })
  category: string;

  /** 标签 */
  @ApiProperty({ description: '图片标签', type: [String] })
  @IsOptional()
  @IsArray({ message: '标签必须是数组' })
  @IsString({ each: true, message: '每个标签必须是字符串' })
  @Length(1, 10, { each: true, message: '每个标签长度不能超过10' })
  tags: string[];

  /** 图片属性 */
  @ApiProperty({
    description: '图片属性',
    type: PhotoAttributesDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PhotoAttributesDto)
  attributes: PhotoAttributesDto;

  /** 图片状态 */
  @ApiProperty({ description: '图片状态', enum: PhotoStatusEnum })
  @IsOptional()
  @IsString({ message: '图片状态必须是字符串' })
  status: PhotoStatusEnum;
}
