import { HttpException, Injectable } from '@nestjs/common'; // 导入 NestJS 的 Injectable 装饰器
import { InjectModel } from '@nestjs/mongoose'; // 导入 InjectModel 装饰器，用于注入 Mongoose 模型
import { Model, Types } from 'mongoose'; // 导入 Mongoose 的 Model 类
import { CounterService } from 'src/counter/counter.service';
import { isFans, isFollowing, nameGen, PasswordUtil } from 'src/common/utils';
import { CaptchaService } from 'src/captcha/captcha.service';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import dayjs from 'dayjs';
import { Users, UsersDocument } from '../schemas/users.schema';
import { CreateUserDto } from '../dtos/create-user.dto';
import { PipelineStage } from 'mongoose';
import { UsersFollow } from '../schemas/users-follow.schema';
import { UsersSetting } from '../schemas/users-setting.schema';
import { UpdateUserSettingDto } from '../dtos/update-user-setting.dto';
import { Photos } from 'src/photos/schemas/photos.schema';
import { Contents } from 'src/contents/schemas/contents.schema';

/** 用户核心服务：注册、登录、修改密码、修改个人信息等 */
@Injectable() // 标记 UserService 类为可注入的服务
export class UsersService {
  // 定义一个私有的用户数组，用于存储用户数据（仅用于示例，实际应用中应通过数据库管理）

  // 构造函数中注入 User 模型
  constructor(
    @InjectModel(Users.name) private readonly userModel: Model<Users>,
    @InjectModel(UsersFollow.name) private usersFollowModel: Model<UsersFollow>,
    @InjectModel(UsersSetting.name)
    private usersSettingModel: Model<UsersSetting>,
    private readonly counterService: CounterService,
    @InjectModel(Photos.name) private photosModel: Model<Photos>,
    @InjectModel(Contents.name) private contentsModel: Model<Contents>,
    private readonly captchaService: CaptchaService // 注入 CaptchaService
  ) {}

  /**
   * !: 创建新用户的异步方法
   * @param createUserDto 新用户的 DTO 对象
   * @param registerIP 注册 IP 地址
   * @returns 新用户的完整信息
   * @throws HttpException 若邮箱或手机号已注册或验证码错误，则抛出此异常
   */
  async create(
    createUserDto: CreateUserDto,
    registerIP: string
  ): Promise<Users> {
    const data = {};

    // 验证是邮箱还是手机号登录，并验证验证码
    if (createUserDto.email) {
      // 验证邮箱是否已注册
      const emailExists = await this.userModel.exists({
        email: createUserDto.email,
      });
      if (emailExists) {
        throw new HttpException('邮箱已注册', HttpStatusEnum.BAD_REQUEST);
      }
      // 验证邮箱验证码
      await this.captchaService.verifyEmail(
        createUserDto.email,
        createUserDto.code
      );
      data['email'] = createUserDto.email;
    } else if (createUserDto.phone) {
      // 验证手机号是否已注册
      const phoneExists = await this.userModel.exists({
        phone: createUserDto.phone,
      });
      if (phoneExists) {
        throw new HttpException('手机号已注册', HttpStatusEnum.BAD_REQUEST);
      }
      // 验证手机验证码
      await this.captchaService.verifySMS(
        createUserDto.phone,
        createUserDto.code
      );
      data['phone'] = createUserDto.phone;
    }

    // 生成用户昵称
    const nickname = await nameGen.generate({
      useSpecialChars: false,
      suffixLength: 8,
    });

    // 生成用户 ID
    const uid = await this.counterService.formatSequence('user');

    data['password'] = await PasswordUtil.hash(createUserDto.password);
    data['uid'] = uid;
    data['nickname'] = nickname;
    data['registerIP'] = registerIP;

    // 创建新用户实例
    const createdCat = new this.userModel(data); // 使用注入的模型创建新用户实例

    createdCat.save(); // 保存用户实例到数据库，并返回保存后的用户对象

    await this.usersSettingModel.create({
      user: createdCat._id.toString(),
    });

    return createdCat.toJSON();
  }

  /**
   * !: 根据 ID 查找用户的异步方法
   * @param id 用户 ID
   * @returns 用户的完整信息
   */
  async findOneById(id: string) {
    const user = await this.userModel.findById(id);
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 根据 UID 查找用户的异步方法
   * @param uid 用户 UID
   * @returns 用户的完整信息
   */
  async findOneByUid(uid: string) {
    const user = await this.userModel.findOne({ uid });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 根据邮箱查找用户的异步方法
   * @param email 用户邮箱
   * @returns 用户的完整信息
   */
  async findOneByEmail(email: string) {
    const user = await this.userModel.findOne({ email });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 根据手机号查找用户的异步方法
   * @param phone 用户手机号
   * @returns 用户的完整信息
   */
  async findOneByPhone(phone: string) {
    const user = await this.userModel.findOne({ phone });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 用户所有信息直接修改的异步方法
   * @param options 包含昵称、头像、密码等修改信息的对象
   * @returns 修改后的用户信息
   */
  async updateAll(id: string, options: Partial<Users>): Promise<Users> {
    const user = await this.userModel.findByIdAndUpdate(
      id,
      { $set: { ...options } },
      {
        new: true,
      }
    );
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }

    return user.toJSON();
  }

  /**
   * !: 用户访客数自增的异步方法
   * @param id 用户 ID
   * @returns 用户的完整信息
   */
  async increaseVisitors(id: string) {
    const user = await this.userModel.findByIdAndUpdate(
      id,
      {
        $inc: { visitors: 1 },
      },
      { new: true }
    );
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 用户登录密码错误次数自增的异步方法
   * @param id 用户 ID
   * @returns 用户的完整信息
   */
  async increaseLoginError(id: string) {
    const user = await this.userModel.findByIdAndUpdate(
      id,
      {
        $inc: { failedAttempts: 1 },
      },
      { new: true }
    );
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    // 若登录失败次数超过 5 次，则锁定用户
    if (user.failedAttempts >= 5) {
      const lockUntil = new Date(
        Date.now() + Math.pow(10, user.failedAttempts - 5) * 60 * 1000
      );
      await this.userModel.findByIdAndUpdate(
        id,
        {
          $set: { lockUntil },
          $inc: { riskLevel: 1 },
        },
        { new: true }
      );
      throw new HttpException(
        `登录失败次数过多，账户已被锁定，请在 ${dayjs(lockUntil).format('YYYY-MM-DD HH:mm:ss')} 后重试`,
        HttpStatusEnum.FORBIDDEN
      );
    }
    throw new HttpException(
      `登陆失败，还有 ${5 - user.failedAttempts} 次机会`,
      HttpStatusEnum.FORBIDDEN
    );
  }

  /**
   * !: 用户登陆成功
   * @param id 用户 ID
   * @returns 用户的完整信息
   */
  async loginSuccess(id: string) {
    const user = await this.userModel.findByIdAndUpdate(
      id,
      {
        $set: { failedAttempts: 0, lastLoginTime: new Date() },
      },
      { new: true }
    );

    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 用户注销的异步方法
   * @param id 用户 ID
   * @returns 注销后的用户信息
   */
  async delete(id: string) {
    const user = await this.userModel.findByIdAndUpdate(id, {
      $set: { isDeleted: true },
    });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return user.toJSON();
  }

  /**
   * !: 用户重置密码的异步方法
   * @param id 用户 ID
   * @param password 新密码
   * @param code 验证码
   * @returns {boolean} 重置密码成功返回 true
   */
  async resetPassword(
    password: string,
    code: { type: 'email' | 'phone'; value: string; code: string }
  ) {
    // 验证用户身份
    let id = '';
    if (code.type === 'email') {
      await this.captchaService.verifyEmail(code.value, code.code);
      id = (await this.findOneByEmail(code.value)).id;
    } else if (code.type === 'phone') {
      await this.captchaService.verifySMS(code.value, code.code);
      id = (await this.findOneByPhone(code.value)).id;
    }

    const user = await this.userModel.findByIdAndUpdate(id, {
      $set: {
        password: await PasswordUtil.hash(password),
        failedAttempts: 0,
      },
    });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    return true;
  }

  /**
   * !: 用户修改密码的异步方法
   * @param id 用户 ID
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns {boolean} 修改密码成功返回 true
   */
  async changePassword(id: string, oldPassword: string, newPassword: string) {
    const user = await this.userModel.findById(id);
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    if (!(await PasswordUtil.compare(oldPassword, user.password))) {
      throw new HttpException('密码错误', HttpStatusEnum.BAD_REQUEST);
    }
    if (await PasswordUtil.compare(newPassword, user.password)) {
      throw new HttpException(
        '新密码不能与旧密码相同',
        HttpStatusEnum.BAD_REQUEST
      );
    }
    await this.userModel.findByIdAndUpdate(
      id,
      {
        $set: {
          password: await PasswordUtil.hash(newPassword),
          failedAttempts: 0,
          lastLoginTime: new Date(),
        },
      },
      { new: true }
    );
    return true;
  }

  /**
   * !: 用户修改邮箱的异步方法
   * @param id 用户 ID
   * @param oldEmail 旧邮箱
   * @param email 新邮箱
   * @param code 验证码
   * @returns {boolean} 修改邮箱成功返回 true
   */
  async updateEmail(
    id: string,
    oldEmail: string,
    newEmail: string,
    code: string
  ) {
    // 验证用户身份
    const user = await this.userModel.findById(id);
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    // 如果已经绑定了邮箱，则验证旧邮箱
    if (user.email) {
      if (user.email !== oldEmail) {
        throw new HttpException('旧邮箱错误', HttpStatusEnum.BAD_REQUEST);
      }
    }
    // 验证验证码
    // 验证邮箱是否已注册
    const emailExists = await this.userModel.exists({
      email: newEmail,
    });
    if (emailExists) {
      throw new HttpException('邮箱已注册', HttpStatusEnum.BAD_REQUEST);
    }
    // 验证邮箱验证码
    await this.captchaService.verifyEmail(newEmail, code);
    await this.updateAll(id, { email: newEmail });
    return true;
  }

  /**
   * !: 用户修改手机号的异步方法
   * @param id 用户 ID
   * @param oldPhone 旧手机号
   * @param phone 新手机号
   * @param code 验证码
   * @returns {boolean} 修改手机号成功返回 true
   */
  async updatePhone(
    id: string,
    oldPhone: string,
    newPhone: string,
    code: string
  ) {
    // 验证用户身份
    const user = await this.userModel.findById(id);
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    // 如果已经绑定了手机号，则验证旧手机号
    if (user.phone) {
      if (user.phone !== oldPhone) {
        throw new HttpException('旧手机号错误', HttpStatusEnum.BAD_REQUEST);
      }
    }
    // 验证验证码
    // 验证手机号是否已注册
    const phoneExists = await this.userModel.exists({
      phone: newPhone,
    });
    if (phoneExists) {
      throw new HttpException('手机号已注册', HttpStatusEnum.BAD_REQUEST);
    }
    // 验证手机验证码
    await this.captchaService.verifySMS(newPhone, code);
    await this.updateAll(id, { phone: newPhone });
    return true;
  }

  /**
   * !: 获取用户资料的异步方法(包含关注状态)
   * @param targetUserId 目标用户 ID
   * @param currentUserId 当前用户 ID（可选）
   * @returns 用户资料
   * @throws HttpException 若用户不存在，则抛出此异常
   */
  async getUserProfile(targetUserId: string, currentUserId?: string) {
    const pipeline: PipelineStage[] = [
      { $match: { _id: new Types.ObjectId(targetUserId) } },
      // 加入关注状态聚合管道
      ...isFollowing(currentUserId),
      // 加入粉丝状态聚合管道
      ...isFans(currentUserId),
    ];

    const user = await this.userModel.aggregate<Users>(pipeline).exec();
    const userProfile = user[0];

    return userProfile;
  }

  /**
   * !: 获取随机用户的异步方法（排除当前用户，排除已关注用户）
   * @param userId 当前用户ID（需要排除）
   * @param limit 需要获取的随机用户数量
   * @returns 随机用户列表（不包含自己和已关注的用户）
   * @throws HttpException 若可用用户数不足，返回空数组
   */
  async getRandomUsers(limit: number, userId?: string) {
    // 1. 获取当前用户已关注的用户 ID 列表
    const followedUserIds = await this.usersFollowModel
      .find(
        { follower: userId }, // 当前用户是关注者
        { following: 1, _id: 0 } // 只保留被关注者的 ID
      )
      .distinct('following'); // 去重

    // 2. 构建查询条件（排除自己和已关注的用户）
    const filter = {
      _id: {
        $ne: new Types.ObjectId(userId), // 排除自己
        $nin: followedUserIds, // 排除已关注的用户
      },
      isDeleted: false, // 未删除的用户
    };

    //  获取有效用户总数（排除自己和已关注用户后）
    const validCount = await this.userModel.countDocuments(filter);

    // 处理边界情况
    if (validCount === 0) {
      return [];
    }
    if (limit > validCount) {
      limit = validCount; // 不能超过可用数量
    }

    // 使用聚合管道实现高效随机抽样
    const pipeline = [
      { $match: filter }, // 应用过滤条件
      // 加入关注状态聚合管道
      ...isFollowing(userId),
      // 加入粉丝状态聚合管道
      ...isFans(userId),

      { $sample: { size: limit } }, // 随机抽样
    ];

    return await this.userModel.aggregate<Users>(pipeline);
  }

  /**
   * !: 获取用户列表的异步方法，用于搜索、分页等
   * @param options 包含搜索条件、分页信息等的对象
   * @param userId 当前用户 ID（可选）
   * @returns 用户列表
   */
  async findListSearch(
    options: Partial<Users & PaginationQuery<Users>>,
    userId?: string
  ) {
    // 参数安全校验和默认值
    const allowedSortFields = [
      'createTime',
      'nickname',
      'uid',
      'updateTime',
      'followingsCount',
      'visitedCount',
      'totalDownloads',
    ];
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'asc',
      ...rest
    } = options;
    const pageSafe = Math.max(1, Number(page) || 1);
    const pageSizeSafe = Math.max(1, Number(pageSize) || 10);
    const sortFieldSafe = allowedSortFields.includes(sortField)
      ? sortField
      : 'createTime';
    const sortOrderSafe = sortOrder === 'desc' ? -1 : 1;
    const skip = (pageSafe - 1) * pageSizeSafe;

    // 构建查询条件
    const filter = { isDeleted: false };
    if (userId) {
      filter['_id'] = { $ne: new Types.ObjectId(userId) };
    }
    if (rest.keyword) {
      filter['$or'] = [
        { nickname: { $regex: rest.keyword, $options: 'i' } },
        { uid: { $regex: rest.keyword, $options: 'i' } },
        { bio: { $regex: rest.keyword, $options: 'i' } },
      ];
    }

    // 定义聚合管道
    const pipeline: PipelineStage[] = [
      { $match: filter },
      ...isFollowing(userId),
      ...isFans(userId),
      {
        $facet: {
          meta: [{ $count: 'total' }],
          data: [
            { $sort: { [sortFieldSafe]: sortOrderSafe } },
            { $skip: skip },
            { $limit: pageSizeSafe },
            { $project: { password: 0 } }, // 排除敏感字段
          ],
        },
      },
    ];

    type FacetResult = {
      meta: { total: number }[];
      data: UsersDocument[];
    };

    // 执行聚合查询
    const result = await this.userModel.aggregate<FacetResult>(pipeline);

    // 解析管道结果
    const [facetResult] = result;
    const total = facetResult?.meta?.[0]?.total || 0;
    const list = facetResult?.data || [];

    return {
      page: pageSafe,
      pageSize: pageSizeSafe,
      totalCount: total,
      totalPage: Math.ceil(total / pageSizeSafe),
      sortField: sortFieldSafe,
      sortOrder: sortOrder === 'desc' ? 'desc' : 'asc',
      list,
    };
  }

  /**
   * !: 用于更改用户设置
   * @param id 用户 ID
   * @param options 包含设置信息的对象
   * @returns 修改后的用户设置
   */
  async updateSetting(id: string, options: Partial<UpdateUserSettingDto>) {
    const userSetting = await this.usersSettingModel.findOneAndUpdate(
      { user: id },
      { $set: options },
      { new: true }
    );
    if (!userSetting?.toJSON()) {
      throw new HttpException('用户设置不存在', HttpStatusEnum.NOT_FOUND);
    }

    if (typeof options.showMyPhotoList === 'boolean') {
      // 如果修改了图片公开状态

      await this.photosModel.updateMany(
        { user: id },
        { $set: { isPublic: userSetting.showMyPhotoList } }
      );
    }

    if (typeof options.showMyContentList === 'boolean') {
      // 如果修改了帖子公开状态
      await this.contentsModel.updateMany(
        { user: id },
        { $set: { isPublic: userSetting.showMyContentList } }
      );
    }

    return userSetting.toJSON();
  }

  /**
   * !: 用于获取用户设置
   * @param id 用户 ID
   * @param uid 用户 UID
   * @returns 用户设置
   */
  async getSetting(uid: string) {
    const user = await this.userModel.findOne({ uid });
    if (!user?.toJSON()) {
      throw new HttpException('用户不存在', HttpStatusEnum.NOT_FOUND);
    }
    const userSetting = await this.usersSettingModel.findOne({
      user: user.toJSON().id,
    });
    if (!userSetting?.toJSON()) {
      throw new HttpException('用户设置不存在', HttpStatusEnum.NOT_FOUND);
    }
    return userSetting.toJSON();
  }

  /**
   * ! 获取用户所有图片的总下载量
   * @param userId 用户 ID
   * @returns 用户所有图片的总下载量
   */
  async getUserTotalDownloads(userId: string): Promise<number> {
    type AggregationResult = {
      _id: null;
      totalDownloads: number;
    };
    const result = await this.photosModel.aggregate<AggregationResult>([
      {
        $match: {
          user: userId, // 匹配当前用户的图片
          isDeleted: false, // 可选：只统计未删除的图片
        },
      },
      {
        $group: {
          _id: null,
          totalDownloads: { $sum: '$downloadCount' }, // 计算下载量总和
        },
      },
    ]);

    return result[0]?.totalDownloads || 0; // 返回统计结果或0
  }
}
