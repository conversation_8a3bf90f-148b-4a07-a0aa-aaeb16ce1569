<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-微信登录</Title>
    </Head>
    <!-- 标题 -->
    <h1>微信登录</h1>

    <!-- 二维码 -->
    <div class="qrcode" @click="login">
      <img src="/qrcode.png" alt="" title="点击刷新" />
    </div>

    <!-- 占位 -->
    <div class="placeholder"></div>
    <div class="bottom">
      <!-- 分割线加文字 -->
      <div class="divider">
        <div class="line"></div>
        <span class="divider-text">其他方式登录</span>
        <div class="line"></div>
      </div>
      <!-- 其他登陆方式的图标 -->
      <div class="login-methods">
        <NuxtLink to="/mobile/auth/login/phone"
          ><div class="icon">
            <div class="phone">
              <IconSvg name="phone" size="2rem" color="#fff" />
            </div>
            <span>手机</span>
          </div></NuxtLink
        >
        <NuxtLink to="/mobile/auth/login/email">
          <div class="icon">
            <div class="email">
              <IconSvg name="email" size="2rem" color="#fff" />
            </div>
            <span>邮箱</span>
          </div>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'mobile-auth',
});

const router = useRouter();

const login = async () => {
  // 微信登录逻辑
  useMessage({
    name: '功能开发中',
    description: '微信登录功能正在开发中',
    type: 'info',
  });

  // 跳转到首页
  router.replace('/mobile/mine');
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 标题的样式
  > h1 {
    font-size: 2rem;
    margin: 0.5rem 0 1rem 0;
    color: var(--text-primary);
    text-align: center;
  }

  // 二维码样式
  > .qrcode {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    cursor: pointer;

    > img {
      width: 200px;
      border-radius: 1rem;
      border: 2px solid var(--border-color);
    }
  }

  .placeholder {
    flex: 1;
  }

  /* 分割线样式 */
  .bottom {
    width: 100%;

    > .divider {
      display: flex;
      align-items: center;
      margin: 1.5rem 0;

      > .line {
        flex: 1;
        height: 1px;
        background: rgba(255, 255, 255, 0.3); /* 半透明分割线 */
      }

      > .divider-text {
        padding: 0 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.3rem;
      }
    }

    // 其他登陆方式区域的样式
    > .login-methods {
      display: flex;
      justify-content: space-evenly;

      .icon {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        > div {
          padding: 0.5rem;
          border-radius: 50%;

          &.phone {
            background-color: #4228c4;
          }

          &.email {
            background-color: #2196f3;
          }
        }

        > span {
          margin-top: 0.5rem;
          font-size: 1.3rem;
        }
      }
    }
  }
}
</style>

