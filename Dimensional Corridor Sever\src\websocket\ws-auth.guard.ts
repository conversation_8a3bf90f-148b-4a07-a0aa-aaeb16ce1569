import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Injectable,
} from '@nestjs/common'; // 导入 NestJS 的 guard 相关类和接口
import { Reflector } from '@nestjs/core'; // 导入 Reflector 类，用于访问控制器和方法的元数据
import { JwtService } from '@nestjs/jwt'; // 导入 JwtService 类，用于处理 JWT 令牌
import { Socket } from 'socket.io'; // 导入 socket.io 的 Socket 类型
import * as config from 'config'; // 导入 config 模块，用于读取配置文件
import { IS_PUBLIC_KEY } from 'src/common/decorator/public.decorator'; // 导入公共路由的装饰器键
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum'; // 导入 HTTP 状态码枚举

// 从配置文件中获取 JWT 配置
const jwtConfig = config.get<JwtConfig>('jwt');

@Injectable() // 标记 WsAuthGuard 类为可注入的 guard
export class WsAuthGuard implements CanActivate {
  // 构造函数中注入 JwtService 和 Reflector
  constructor(
    private jwtService: JwtService,
    private reflector: Reflector
  ) {}

  /**
   * !: CanActivate 接口是一个函数签名，用于定义一个类或方法是否可以被激活。
   * @param context : ExecutionContext 接口是一个函数签名，用于表示当前请求的执行上下文。
   * @returns Promise<boolean> : 返回一个 Promise，用于表示是否可以激活。
   * @throws HttpException : 如果用户未登录，则抛出未授权的异常。
   * @description 该方法用于检查 WebSocket 客户端是否已登录，并附加用户信息到客户端对象。
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 使用 Reflector 检查是否是公共路由
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(), // 获取当前处理的方法
      context.getClass(), // 获取当前处理的类
    ]);
    // 如果是公共路由，则直接返回 true
    if (isPublic) return true;

    // 获取 WebSocket 客户端对象
    const client = context.switchToWs().getClient<Socket>();
    // 从客户端中提取 JWT 令牌
    const token = this.extractToken(client);

    // 如果没有提取到令牌，则抛出未授权的异常
    if (!token) {
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }

    try {
      // 验证 JWT 令牌
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: jwtConfig.secret, // 使用配置文件中的 secret 进行验证
      });
      // 将用户信息附加到客户端对象
      client['user'] = payload;
      // 返回 true 表示连接可以激活
      return true;
    } catch {
      // 如果验证失败，则抛出未授权的异常
      throw new HttpException('用户未登录', HttpStatusEnum.UNAUTHORIZED);
    }
  }

  /**
   * !: 该方法用于从 WebSocket 客户端的握手头信息中提取 JWT 令牌。
   * @param client : Socket 类型，表示 WebSocket 客户端对象。
   * @returns string | undefined : 返回 JWT 令牌字符串，如果没有找到，则返回 undefined。
   * @description 该方法用于从 WebSocket 客户端的握手头信息中提取 JWT 令牌。
   */
  private extractToken(client: Socket): string | undefined {
    // 从客户端握手头信息中获取 authorization 字段
    const [type, token] =
      client.handshake.headers.authorization?.split(' ') || [];
    // 如果 authorization 字段以 'Bearer' 开头，则返回令牌部分
    return type === 'Bearer' ? token : undefined;
  }
}
