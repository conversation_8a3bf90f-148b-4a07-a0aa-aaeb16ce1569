import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length } from 'class-validator';

export class UpdatePasswordDto {
  /** 旧密码 */
  @ApiProperty({ description: '旧密码', example: '123456' })
  @IsNotEmpty({ message: '旧密码不能为空' })
  @IsString({ message: '旧密码必须是字符串' })
  @Length(6, 20, { message: '旧密码长度必须在 6-20 之间' })
  oldPassword: string;

  /** 新密码 */
  @ApiProperty({ description: '新密码', example: '123456' })
  @IsNotEmpty({ message: '新密码不能为空' })
  @IsString({ message: '新密码必须是字符串' })
  @Length(6, 20, { message: '新密码长度必须在 6-20 之间' })
  newPassword: string;

  /** 确认新密码 */
  @ApiProperty({ description: '确认新密码', example: '123456' })
  @IsNotEmpty({ message: '确认新密码不能为空' })
  @IsString({ message: '确认新密码必须是字符串' })
  @Length(6, 20, { message: '确认新密码长度必须在 6-20 之间' })
  confirmNewPassword: string;
}
