<template>
  <!-- 验证码 -->
  <div class="captcha-container" :class="{ 'is-loading': isLoading }">
    <ClientOnly>
      <div
        class="captcha-image"
        v-html="captchaSvg"
        @click.prevent="refresh"
      ></div>
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  apiUrl: {
    type: String,
    default: '/captcha/image',
  },
});

// 响应式状态
const captchaSvg = ref('');
const isLoading = ref(false);
const error = ref(false);

// 防抖刷新方法
const refresh = useDebounceFn(async () => {
  try {
    isLoading.value = true;
    error.value = false;

    const url = await useApi().getCaptchaImg(props.apiUrl);
    captchaSvg.value = `<img src="${url}" alt="captcha" style="width:100%;height:100%;" />`;
  } catch (err: any) {
    console.error('验证码加载失败:', err.message as string);
    error.value = true;
    captchaSvg.value = ''; // 清空验证码SVG
  } finally {
    isLoading.value = false;
  }
}, 300);

// 暴露方法给父组件
defineExpose({
  refresh,
});

// 自动初始化
onMounted(() => refresh());
</script>
<style scoped lang="scss">
.captcha-container {
  position: relative;
  display: inline-block;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  background: #f3f4f6;
  width: 7.8rem;
  height: 2.7rem;
  margin-bottom: -1rem;

  .captcha-image {
    cursor: pointer;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f6f6f6; // 占位符背景颜色
  }

  .loading-overlay,
  .error-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-overlay {
    cursor: pointer;
    color: #ef4444;
  }

  &.is-loading {
    .captcha-image {
      cursor: progress;
    }
  }
}
</style>
