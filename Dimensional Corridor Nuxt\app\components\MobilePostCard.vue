<template>
  <ClientOnly>
    <!-- 卡片容器 -->
    <div class="post-card">
      <!-- 卡片主体，点击后导航到文章详情页 -->
      <div
        class="post-body"
        :style="bodyStyle"
        @click="
          $router.push({
            path: '/mobile/posts/details',
            query: { id: post.id },
          })
        "
      >
        <!-- 图片区域 -->
        <div :rotate-z="2" :translate-z="20" :translate-y="15">
          <!-- 如果存在图片则显示 -->
          <img
            v-if="hasImage"
            :src="post.cover?.url"
            :alt="post.title"
            class="image-section"
            :style="imageStyle"
            @load="handleImageLoad"
            @error="handleImageError"
            loading="lazy"
          />
        </div>

        <!-- 内容区域 -->
        <div :translate-z="30" class="content-item">
          <div class="content-section">
            <!-- 文章标题 -->
            <h3 class="post-title">{{ post.title }}</h3>
            <!-- 文章摘要 -->
            <p class="post-summary">{{ summary }}</p>
          </div>
        </div>

        <!-- 标签区域 -->
        <div :translate-z="30">
          <!-- 如果存在标签则显示 -->
          <div class="tags-section" v-if="tags.length">
            <!-- 循环显示每个标签 -->
            <div class="tag" v-for="(tag, index) in tags" :key="index">
              {{ tag }}
            </div>
          </div>
        </div>

        <!-- 统计信息区域 -->
        <div class="stats-section">
          <!-- 点赞统计 -->
          <div class="stat">
            <IconSvg
              name="like"
              :size="15"
              :color="post.isLiked ? '#FF5252' : '#CCCCCC'"
            />
            <span>{{ post.likeCount }}</span>
          </div>
          <!-- 收藏统计 -->
          <div class="stat">
            <IconSvg
              name="favorite"
              :size="15"
              :color="post.isFavorited ? '#FFD700' : '#CCCCCC'"
            />
            <span>{{ post.favoriteCount }}</span>
          </div>
          <!-- 作者头像 -->
          <div class="stat">
            <img :src="post.user.avatar" :alt="post.user.uid" class="avatar" />
          </div>
        </div>
      </div>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
// 定义组件的props类型
interface Props {
  post: PostsWaterfallGalleryItem;
  fixedWidth: number;
}

// 获取组件的props
const props = defineProps<Props>();

// 计算属性，判断文章是否有图片
const hasImage = computed(() => !!props.post.cover?.url);

// 计算属性，生成文章摘要
const summary = computed(() => {
  return props.post.content.length > 100
    ? props.post.content.substring(0, 100) + '...'
    : props.post.content;
});

// 计算属性，获取文章标签（当前实现为空数组）
const tags = computed<string[]>(() => []);

// 计算属性，根据图片宽度和高度计算卡片总高度
const cardHeight = computed(() => {
  const contentHeight = 150;
  let imageHeight = 0;

  if (props.post.cover) {
    imageHeight =
      props.fixedWidth * (props.post.cover.height / props.post.cover.width);
  }

  return contentHeight + imageHeight;
});

// 计算属性，生成卡片主体样式
const bodyStyle = computed(() => ({
  width: `${props.fixedWidth}px`,
  height: `${cardHeight.value}px`,
}));

// 计算属性，生成图片样式
const imageStyle = computed(() => ({
  height: props.post.cover
    ? `${props.fixedWidth * (props.post.cover.height / props.post.cover.width)}px`
    : '0px',
}));

// 图片加载完成后的处理函数
const handleImageLoad = (e: Event) => {
  const img = e.target as HTMLImageElement;
  img.style.opacity = '1';
};

// 图片加载错误后的处理函数
const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement;
  // 设置错误占位图颜色和透明度
  img.style.backgroundColor = '#350047';
  img.style.opacity = '0.8';
};
</script>

<style lang="scss" scoped>
// 卡片容器样式
.post-card {
  display: flex;
  justify-content: center;
  align-items: center;

  // 卡片主体样式
  .post-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: 1rem;
    background: rgba(53, 0, 71, 0.7);
    box-shadow: 0 4px 20px rgba(138, 43, 226, 0.25);
    cursor: pointer;

    img {
      border-radius: 1rem;
    }

    // 鼠标悬停时改变阴影和标题颜色
    &:hover {
      box-shadow: 0 6px 25px rgba(138, 43, 226, 0.4);

      .post-title {
        color: #fff;
      }
    }
  }

  // 图片样式
  .image-section {
    display: block;
    width: 100%;
    object-fit: cover;
    background-color: #350047;
    transition:
      transform 0.3s ease,
      opacity 0.5s ease;
    opacity: 0;
  }

  // 内容区域样式
  .content-section {
    padding: 16px;
    flex-grow: 1;
    height: 100%;

    // 文章标题样式
    .post-title {
      font-size: 1.1rem;
      font-weight: bold;
      margin-bottom: 10px;
      color: #e0b0ff;
      line-height: 1.3;
      transition: color 0.2s ease;
    }

    // 文章摘要样式
    .post-summary {
      font-size: 0.9rem;
      color: #d8bfd8;
      line-height: 1.1rem;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      flex: 1;
    }
  }

  // 内容项样式
  .content-item {
    width: 100%;
    flex: 1;
  }

  // 标签区域样式
  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 0 16px 12px;

    // 单个标签样式
    .tag {
      padding: 4px 12px;
      background: linear-gradient(
        145deg,
        rgba(74, 0, 128, 0.47),
        rgba(41, 0, 90, 0.46)
      );
      border-radius: 12px;
      border: 1px solid #8a2be2;
      color: #d8bfd8;
      font-size: 0.8rem;
      box-shadow: 0 0 8px rgba(138, 43, 226, 0.4);
      transition: all 0.2s ease;

      // 鼠标悬停时标签样式变化
      &:hover {
        box-shadow: 0 2px 12px rgba(138, 43, 226, 0.6);
        background: linear-gradient(
          145deg,
          rgba(94, 0, 158, 0.57),
          rgba(61, 0, 120, 0.56)
        );
      }
    }
  }

  // 统计信息区域样式
  .stats-section {
    display: flex;
    justify-content: space-around;
    padding: 8px 16px;
    height: 40px;
    border-top: 1px solid rgba(138, 43, 226, 0.3);
    background: rgba(65, 0, 100, 0.4);
    font-size: 0.85rem;
    color: #d8bfd8;
    border-radius: 1rem;

    // 单个统计项样式
    .stat {
      display: flex;
      align-items: center;
      gap: 4px;
      transition: transform 0.2s ease;

      span {
        transition: color 0.2s ease;
      }

      // 鼠标悬停时改变统计项文字颜色
      &:hover span {
        color: #fff;
      }

      img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
    }
  }
}
</style>

