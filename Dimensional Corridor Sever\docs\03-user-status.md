# WebSocket 用户状态管理

## 🌟 概述

本文档详细介绍WebSocket系统中的用户在线状态管理功能，包括状态类型、状态更新、在线用户查询等。

## 📊 状态类型

### 支持的状态
- `online` - 在线 (默认状态)
- `away` - 离开 (暂时离开但保持连接)
- `busy` - 忙碌 (在线但不希望被打扰)
- `offline` - 离线 (仅在断开连接时自动设置)

### 状态自动管理
```javascript
// 用户连接时自动设置为在线
socket.on('connect', () => {
  console.log('已连接，状态自动设置为: online');
});

// 用户断开时自动设置为离线
socket.on('disconnect', () => {
  console.log('已断开，状态自动设置为: offline');
});
```

## 🔄 状态更新

### 更新自己的状态
```javascript
// 更新用户状态
function updateMyStatus(status) {
  socket.emit('update_user_status', { status });
}

// 监听状态更新响应
socket.on('status_update_response', (response) => {
  if (response.success) {
    console.log('✅ 状态更新成功:', response.status);
    updateStatusUI(response.status);
  } else {
    console.error('❌ 状态更新失败:', response.error);
  }
});

// 使用示例
updateMyStatus('away');   // 设置为离开
updateMyStatus('busy');   // 设置为忙碌
updateMyStatus('online'); // 设置为在线
```

### 监听其他用户状态变化
```javascript
// 监听用户上线事件
socket.on('user_online', (data) => {
  console.log('👋 用户上线:', {
    user: data.user,           // 用户信息
    timestamp: data.timestamp  // 上线时间
  });
  
  addOnlineUser(data.user);
});

// 监听用户下线事件
socket.on('user_offline', (data) => {
  console.log('👋 用户下线:', {
    userId: data.userId,       // 用户ID
    lastSeen: data.lastSeen,   // 最后活跃时间
    timestamp: data.timestamp  // 下线时间
  });
  
  removeOnlineUser(data.userId);
});

// 监听用户状态更新
socket.on('user_status_update', (data) => {
  console.log('🔄 用户状态更新:', {
    user: data.user,           // 用户信息(包含新状态)
    timestamp: data.timestamp  // 更新时间
  });
  
  updateUserStatus(data.user);
});
```

## 🔍 状态查询

### 查询单个用户状态
```javascript
// 查询指定用户状态
function getUserStatus(userId) {
  socket.emit('get_user_status', { userId });
}

// 监听用户状态响应
socket.on('user_status_response', (response) => {
  if (response.success) {
    const userStatus = response.data;
    console.log('用户状态:', {
      userId: userStatus.userId,
      status: userStatus.status,       // 当前状态
      lastSeen: userStatus.lastSeen,   // 最后活跃时间
      device: userStatus.device,       // 设备信息
      isOnline: userStatus.isOnline    // 是否在线
    });
    
    displayUserStatus(userStatus);
  } else {
    console.error('获取用户状态失败:', response.error);
  }
});

// 使用示例
getUserStatus('user123');
```

### 批量查询用户状态
```javascript
// 批量查询多个用户状态
function getBatchUserStatus(userIds) {
  socket.emit('get_batch_user_status', { userIds });
}

// 监听批量状态响应
socket.on('batch_user_status_response', (response) => {
  if (response.success) {
    const userStatuses = response.data;
    console.log('批量用户状态:', userStatuses);
    
    userStatuses.forEach(userStatus => {
      updateUserStatusInUI(userStatus);
    });
  } else {
    console.error('批量获取用户状态失败:', response.error);
  }
});

// 使用示例
getBatchUserStatus(['user1', 'user2', 'user3']);
```

### 获取所有在线用户
```javascript
// 获取所有在线用户
function getAllOnlineUsers() {
  socket.emit('get_all_online_users');
}

// 监听在线用户列表响应
socket.on('all_online_users_response', (response) => {
  if (response.success) {
    const { count, users } = response.data;
    console.log(`当前在线用户数: ${count}`);
    console.log('在线用户列表:', users);
    
    updateOnlineUsersList(users);
    updateOnlineCount(count);
  } else {
    console.error('获取在线用户失败:', response.error);
  }
});

// 使用示例
getAllOnlineUsers();
```

## 🎯 用户状态管理器

### 完整的状态管理类
```javascript
class UserStatusManager {
  constructor(socket) {
    this.socket = socket;
    this.onlineUsers = new Map();
    this.myStatus = 'online';
    
    this.initEventListeners();
  }

  // 初始化事件监听
  initEventListeners() {
    // 用户上线
    this.socket.on('user_online', (data) => {
      this.handleUserOnline(data);
    });

    // 用户下线
    this.socket.on('user_offline', (data) => {
      this.handleUserOffline(data);
    });

    // 用户状态更新
    this.socket.on('user_status_update', (data) => {
      this.handleUserStatusUpdate(data);
    });

    // 状态更新响应
    this.socket.on('status_update_response', (response) => {
      this.handleStatusUpdateResponse(response);
    });

    // 在线用户列表响应
    this.socket.on('all_online_users_response', (response) => {
      this.handleOnlineUsersResponse(response);
    });

    // 用户状态查询响应
    this.socket.on('user_status_response', (response) => {
      this.handleUserStatusResponse(response);
    });
  }

  // 更新我的状态
  updateMyStatus(status) {
    const validStatuses = ['online', 'away', 'busy'];
    if (!validStatuses.includes(status)) {
      console.error('无效的状态:', status);
      return false;
    }

    this.socket.emit('update_user_status', { status });
    return true;
  }

  // 查询用户状态
  getUserStatus(userId) {
    this.socket.emit('get_user_status', { userId });
  }

  // 批量查询用户状态
  getBatchUserStatus(userIds) {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      console.error('用户ID数组不能为空');
      return false;
    }

    if (userIds.length > 50) {
      console.warn('建议单次查询用户数量不超过50个');
    }

    this.socket.emit('get_batch_user_status', { userIds });
    return true;
  }

  // 获取所有在线用户
  getAllOnlineUsers() {
    this.socket.emit('get_all_online_users');
  }

  // 处理用户上线
  handleUserOnline(data) {
    const user = data.user;
    this.onlineUsers.set(user.id, user);
    
    console.log(`用户 ${user.nickname} 上线了`);
    this.onUserOnline?.(user);
  }

  // 处理用户下线
  handleUserOffline(data) {
    const user = this.onlineUsers.get(data.userId);
    this.onlineUsers.delete(data.userId);
    
    console.log(`用户 ${data.userId} 下线了`);
    this.onUserOffline?.(data.userId, user);
  }

  // 处理用户状态更新
  handleUserStatusUpdate(data) {
    const user = data.user;
    this.onlineUsers.set(user.id, user);
    
    console.log(`用户 ${user.nickname} 状态更新为: ${user.status}`);
    this.onUserStatusUpdate?.(user);
  }

  // 处理状态更新响应
  handleStatusUpdateResponse(response) {
    if (response.success) {
      this.myStatus = response.status;
      console.log(`我的状态已更新为: ${response.status}`);
      this.onMyStatusUpdate?.(response.status);
    } else {
      console.error('状态更新失败:', response.error);
      this.onStatusUpdateError?.(response.error);
    }
  }

  // 处理在线用户列表响应
  handleOnlineUsersResponse(response) {
    if (response.success) {
      const { count, users } = response.data;
      
      // 更新在线用户列表
      this.onlineUsers.clear();
      users.forEach(user => {
        this.onlineUsers.set(user.id, user);
      });
      
      console.log(`获取到 ${count} 个在线用户`);
      this.onOnlineUsersUpdate?.(users, count);
    } else {
      console.error('获取在线用户失败:', response.error);
    }
  }

  // 处理用户状态查询响应
  handleUserStatusResponse(response) {
    if (response.success) {
      const userStatus = response.data;
      console.log('用户状态查询结果:', userStatus);
      this.onUserStatusQuery?.(userStatus);
    } else {
      console.error('用户状态查询失败:', response.error);
    }
  }

  // 获取当前在线用户列表
  getOnlineUsers() {
    return Array.from(this.onlineUsers.values());
  }

  // 获取在线用户数量
  getOnlineCount() {
    return this.onlineUsers.size;
  }

  // 检查用户是否在线
  isUserOnline(userId) {
    return this.onlineUsers.has(userId);
  }

  // 获取用户信息
  getUser(userId) {
    return this.onlineUsers.get(userId);
  }

  // 获取我的状态
  getMyStatus() {
    return this.myStatus;
  }

  // 事件回调 (可以被外部设置)
  onUserOnline = null;           // 用户上线回调
  onUserOffline = null;          // 用户下线回调
  onUserStatusUpdate = null;     // 用户状态更新回调
  onMyStatusUpdate = null;       // 我的状态更新回调
  onStatusUpdateError = null;    // 状态更新错误回调
  onOnlineUsersUpdate = null;    // 在线用户列表更新回调
  onUserStatusQuery = null;      // 用户状态查询回调
}

// 使用示例
const statusManager = new UserStatusManager(socket);

// 设置事件回调
statusManager.onUserOnline = (user) => {
  console.log('用户上线回调:', user);
  // 更新UI
};

statusManager.onUserOffline = (userId, user) => {
  console.log('用户下线回调:', userId);
  // 更新UI
};

statusManager.onMyStatusUpdate = (status) => {
  console.log('我的状态更新:', status);
  // 更新UI中的状态显示
};

// 使用方法
statusManager.updateMyStatus('away');
statusManager.getAllOnlineUsers();
statusManager.getUserStatus('user123');
```

## 📊 数据格式

### 用户状态对象
```typescript
interface UserStatus {
  userId: string;          // 用户ID
  status: string;          // 状态 ('online'|'away'|'busy'|'offline')
  lastSeen: Date;          // 最后活跃时间
  device?: string;         // 设备信息
  isOnline: boolean;       // 是否在线
}
```

### 用户信息对象
```typescript
interface OnlineUser {
  id: string;              // 用户ID
  nickname: string;        // 用户昵称
  avatar: string;          // 用户头像
  status: string;          // 当前状态
  lastSeen: Date;          // 最后活跃时间
}
```

## 🔗 相关文档

- [连接与认证](./01-connection-auth.md)
- [消息传递功能](./02-messaging.md)
- [群组管理](./04-group-management.md)
- [错误处理](./05-error-handling.md)
