# 🌟 次元回廊 - 后端服务

[![NestJS](https://img.shields.io/badge/nestjs-%23E0234E.svg?style=flat-square&logo=nestjs&logoColor=white)](https://nestjs.com/)
[![TypeScript](https://img.shields.io/badge/typescript-%23007ACC.svg?style=flat-square&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-%234ea94b.svg?style=flat-square&logo=mongodb&logoColor=white)](https://www.mongodb.com/)
[![Redis](https://img.shields.io/badge/redis-%23DD0031.svg?style=flat-square&logo=redis&logoColor=white)](https://redis.io/)
[![Socket.IO](https://img.shields.io/badge/Socket.io-black?style=flat-square&logo=socket.io&badgeColor=010101)](https://socket.io/)

## 📋 项目简介

次元回廊后端服务是一个基于 NestJS 框架构建的图片分享社交平台后端系统。提供完整的用户管理、内容管理、实时通信、AI聊天等功能，支持多种登录方式和丰富的社交互动功能。

## ✨ 核心功能

### 🔐 用户系统

- **多种登录方式**: 邮箱、手机号、微信登录
- **JWT认证**: 安全的用户认证和授权
- **用户管理**: 完整的用户信息管理和权限控制
- **账户安全**: 密码加密、验证码验证、账户注销

### 📱 内容管理

- **图片上传**: 支持多图上传和存储
- **内容发布**: 图文混合内容发布系统
- **内容审核**: 图片和文本内容审核机制
- **搜索功能**: 全文搜索和标签筛选

### 💬 社交功能

- **实时聊天**: WebSocket实时消息系统
- **AI对话**: 集成AI聊天功能
- **用户互动**: 点赞、评论、关注系统
- **通知推送**: 实时消息通知

### 🛡️ 安全防护

- **图形验证码**: SVG验证码防机器人
- **接口限流**: 防止恶意请求和DDoS攻击
- **数据加密**: 敏感信息加密存储
- **权限控制**: 基于角色的访问控制

## 🛠️ 技术栈

| 功能模块     | 技术选型               | 版本 |
| ------------ | ---------------------- | ---- |
| **框架**     | NestJS                 | 11.x |
| **语言**     | TypeScript             | 5.x  |
| **数据库**   | MongoDB + Mongoose     | 8.x  |
| **缓存**     | Redis                  | 5.x  |
| **认证**     | JWT + Passport         | -    |
| **实时通信** | Socket.IO              | 4.x  |
| **邮件服务** | @nestjs-modules/mailer | 2.x  |
| **短信服务** | 阿里云短信服务         | 4.x  |
| **API文档**  | Swagger                | 11.x |
| **安全防护** | Helmet + Throttler     | -    |
| **任务调度** | @nestjs/schedule       | 6.x  |

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **MongoDB** >= 5.0
- **Redis** >= 6.0
- **pnpm** >= 8.0 (推荐使用)

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd "Dimensional Corridor Sever"
```

2. **安装依赖**

```bash
pnpm install
```

3. **环境配置**

创建配置文件并填入相应的配置信息：

```bash
# 复制配置文件模板
cp config/default.json config/development.json
```

配置文件示例：

```json
{
  "database": {
    "mongodb": "mongodb://localhost:27017/dimensional-corridor"
  },
  "redis": {
    "host": "localhost",
    "port": 6379
  },
  "jwt": {
    "secret": "your-jwt-secret"
  },
  "wechat": {
    "appId": "your-wechat-appid",
    "appSecret": "your-wechat-secret"
  },
  "aliyun": {
    "accessKeyId": "your-access-key-id",
    "accessKeySecret": "your-access-key-secret"
  }
}
```

4. **开发环境启动**

```bash
# 构建项目
pnpm build

# 启动开发服务器
pnpm dev
```

5. **生产环境部署**

```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start:prod
```

### 🔧 可用脚本

```bash
pnpm dev          # 开发模式启动
pnpm build        # 构建项目
pnpm start        # 启动服务
pnpm start:prod   # 生产模式启动
pnpm test         # 运行测试
pnpm lint         # 代码检查
pnpm format       # 代码格式化
```

## 📁 项目结构

```bash
Dimensional Corridor Sever/
│
├── config/                            # 配置管理
│   ├── default.json                   # 默认配置
│   ├── development.json               # 开发环境配置
│   └── production.json                # 生产环境配置
│
├── src/                               # 源代码目录
│   ├── app.module.ts                  # 根模块
│   ├── main.ts                        # 应用入口文件
│   │
│   ├── common/                        # 公共模块
│   │   ├── decorator/                 # 自定义装饰器
│   │   ├── guard/                     # 认证守卫
│   │   ├── middlewares/               # 中间件
│   │   └── utils/                     # 工具函数
│   │
│   ├── redis/                         # Redis缓存模块
│   │   ├── redis.module.ts            # Redis模块
│   │   ├── redis.service.ts           # Redis服务
│   │   ├── redis.decorators.ts        # Redis装饰器
│   │   └── redis.interface.ts         # Redis接口定义
│   │
│   ├── auth/                          # 认证授权模块
│   │   ├── auth.controller.ts         # 认证控制器
│   │   ├── auth.service.ts            # 认证服务
│   │   ├── auth.module.ts             # 认证模块
│   │   └── dtos/                      # 认证相关DTO
│   │
│   ├── users/                         # 用户管理模块
│   │   ├── users.controller.ts        # 用户控制器
│   │   ├── users.module.ts            # 用户模块
│   │   ├── service/                   # 用户服务
│   │   ├── dtos/                      # 用户DTO
│   │   ├── filter-dtos/               # 用户筛选DTO
│   │   └── schemas/                   # 用户数据模型
│   │
│   ├── contents/                      # 内容管理模块
│   │   ├── contents.controller.ts     # 内容控制器
│   │   ├── contents.service.ts        # 内容服务
│   │   ├── contents.module.ts         # 内容模块
│   │   ├── dtos/                      # 内容DTO
│   │   ├── filters/                   # 内容筛选器
│   │   └── schemas/                   # 内容数据模型
│   │
│   ├── photos/                        # 图片管理模块
│   │   ├── photos.controller.ts       # 图片控制器
│   │   ├── photos.service.ts          # 图片服务
│   │   ├── photos.module.ts           # 图片模块
│   │   ├── dtos/                      # 图片DTO
│   │   ├── filters/                   # 图片筛选器
│   │   └── schemas/                   # 图片数据模型
│   │
│   ├── search/                        # 搜索功能模块
│   │   ├── search.controller.ts       # 搜索控制器
│   │   ├── search.service.ts          # 搜索服务
│   │   ├── search.module.ts           # 搜索模块
│   │   ├── dto/                       # 搜索DTO
│   │   └── schemas/                   # 搜索数据模型
│   │
│   ├── aichat/                        # AI聊天模块
│   │   ├── aichat.controller.ts       # AI聊天控制器
│   │   ├── aichat.service.ts          # AI聊天服务
│   │   ├── aichat.module.ts           # AI聊天模块
│   │   └── dto/                       # AI聊天DTO
│   │
│   ├── captcha/                       # 验证码模块
│   │   ├── captcha.controller.ts      # 验证码控制器
│   │   ├── captcha.service.ts         # 验证码服务
│   │   ├── captcha.module.ts          # 验证码模块
│   │   ├── dtos/                      # 验证码DTO
│   │   └── schemas/                   # 验证码数据模型
│   │
│   ├── websocket/                     # WebSocket实时通信模块
│   │   ├── websocket.gateway.ts       # WebSocket网关
│   │   ├── websocket.module.ts        # WebSocket模块
│   │   ├── ws-auth.guard.ts           # WebSocket认证守卫
│   │   └── dtos/                      # WebSocket消息DTO
│   │
│   ├── admins/                        # 管理员模块
│   │   ├── admins.controller.ts       # 管理员控制器
│   │   ├── admins.service.ts          # 管理员服务
│   │   ├── admins.module.ts           # 管理员模块
│   │   ├── dto/                       # 管理员DTO
│   │   └── filter-dtos/               # 管理员筛选DTO
│   │
│   ├── counter/                       # 计数器模块
│   │   ├── counter.service.ts         # 计数器服务
│   │   ├── counter.module.ts          # 计数器模块
│   │   └── schemas/                   # 计数器数据模型
│   │
│   ├── shared/                        # 共享资源
│   │   ├── aiPrompt/                  # AI提示词
│   │   ├── enums/                     # 枚举定义
│   │   ├── templates/                 # 邮件模板
│   │   └── types/                     # TypeScript类型定义
│   │
│   └── public/                        # 静态资源文件
│
├── logs/                              # 日志文件
├── dist/                              # 构建输出目录
└── test/                              # 测试文件
```

## 📚 API文档

项目集成了Swagger自动生成API文档，启动服务后访问：

- **开发环境**: `http://localhost:3000/api`
- **生产环境**: `https://api.sixflower.love:3000/api`

## 🔧 主要API端点

### 认证相关

- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/wechat` - 微信登录
- `POST /auth/logout` - 用户登出

### 用户管理

- `GET /users/profile` - 获取用户信息
- `PUT /users/profile` - 更新用户信息
- `POST /users/upload-avatar` - 上传头像

### 内容管理

- `GET /contents` - 获取内容列表
- `POST /contents` - 发布内容
- `PUT /contents/:id` - 更新内容
- `DELETE /contents/:id` - 删除内容

### 图片管理

- `POST /photos/upload` - 上传图片
- `GET /photos` - 获取图片列表
- `DELETE /photos/:id` - 删除图片

### 搜索功能

- `GET /search/contents` - 搜索内容
- `GET /search/users` - 搜索用户

## 🌐 WebSocket事件

### 客户端连接示例

```javascript
import { io } from 'socket.io-client';

// 连接WebSocket
const socket = io('https://api.sixflower.love:3000', {
  auth: {
    token: 'your-jwt-token',
  },
});

// 监听连接事件
socket.on('connect', () => {
  console.log('连接成功');
});

// 发送消息
socket.emit('sendMessage', {
  receiverId: 'user123',
  content: '你好！',
  type: 'text',
});

// 接收消息
socket.on('receiveMessage', (data) => {
  console.log('收到消息:', data);
});

// AI聊天
socket.emit('aiChat', {
  message: '你好，AI助手',
});

socket.on('aiResponse', (response) => {
  console.log('AI回复:', response);
});
```

## 🛠️ 开发工具

### 代码规范

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型安全检查

### 测试工具

- **Jest**: 单元测试框架
- **Supertest**: HTTP接口测试

### 开发辅助

- **Swagger**: API文档自动生成
- **Copyfiles**: 静态资源复制
- **Cross-env**: 跨平台环境变量

## 🚀 部署说明

### Docker部署 (推荐)

```dockerfile
# Dockerfile示例
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

### PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start dist/main.js --name "dimensional-corridor-api"

# 查看状态
pm2 status

# 查看日志
pm2 logs dimensional-corridor-api
```

## 🔒 安全配置

### 环境变量配置

```bash
# .env 文件示例
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key
MONGODB_URI=mongodb://localhost:27017/dimensional-corridor
REDIS_URL=redis://localhost:6379

# 微信登录配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 阿里云短信配置
ALIYUN_ACCESS_KEY_ID=your-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret

# 邮件服务配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-email-password
```

## 📄 许可证

本项目采用 UNLICENSED 许可证。

---

**次元回廊后端服务** - 为用户提供优质的图片分享社交体验 🌟
