<template>
  <div class="menu-container">
    <ul class="nav">
      <!-- 遍历导航项数组，生成导航列表项 -->
      <li
        v-for="(item, index) in props.navItems || navItems"
        :key="index"
        :class="{ active: item.path === routerStore.get()?.split('?')[0] }"
      >
        <!-- 使用NuxtLink组件进行页面跳转 -->
        <NuxtLink :to="item.path + query">
          <!-- 根据当前激活的导航项动态显示图标的颜色 -->
          <IconSvg
            :name="item.icon"
            size="1.5rem"
            :color="
              item.path === routerStore.get()
                ? 'var(--text-primary)'
                : 'var(--text-secondary)'
            "
          />
          <!-- 显示导航项名称 -->
          <span>{{ item.name }}</span>
        </NuxtLink>
      </li>
    </ul>
    <div class="set">
      <ul class="set-list">
        <!-- 鼠标悬停时显示第一个设置悬浮菜单 -->
        <li @mouseenter="showSetMenufn(1)" @mouseleave="hideSetMenufn">
          <IconSvg name="setting" size="1.5rem" color="var(--text-secondary)" />
        </li>
        <!-- 鼠标悬停时显示第二个设置悬浮菜单 -->
        <li @mouseenter="showSetMenufn(2)" @mouseleave="hideSetMenufn">
          <IconSvg
            name="question"
            size="1.5rem"
            color="var(--text-secondary)"
          />
        </li>
      </ul>
      <!-- 设置的小型悬浮菜单，初始隐藏 -->
      <ul class="set-menu menu1" v-show="showSetMenu === 1">
        <!-- 鼠标悬停时保持菜单显示，点击时根据登录状态显示隐私设置弹窗 -->
        <li
          @mouseenter="showSetMenufn(1)"
          @mouseleave="hideSetMenufn"
          @click="privacyDialogFlag = getIsLogin()"
          :class="{ disabled: !getIsLogin() }"
        >
          <IconSvg
            name="privacy"
            size="1rem"
            color="var(--text-secondary)"
          /><span>隐私设置</span>
        </li>
        <!-- 鼠标悬停时保持菜单显示，点击时根据登录状态显示账号安全弹窗 -->
        <li
          @mouseenter="showSetMenufn(1)"
          @mouseleave="hideSetMenufn"
          @click="accountSafeDialogFlag = getIsLogin()"
          :class="{ disabled: !getIsLogin() }"
        >
          <IconSvg name="safe" size="1rem" color="var(--text-secondary)" /><span
            >账号安全</span
          >
        </li>
        <!-- 鼠标悬停时保持菜单显示，点击时显示皮肤选择弹窗 -->
        <li
          @mouseenter="showSetMenufn(1)"
          @mouseleave="hideSetMenufn"
          @click="skinDialogFlag = true"
        >
          <IconSvg name="skin" size="1rem" color="var(--text-secondary)" /><span
            >皮肤选择</span
          >
        </li>
        <!-- 鼠标悬停时保持菜单显示，点击时登出 -->
        <li
          @mouseenter="showSetMenufn(1)"
          @mouseleave="hideSetMenufn"
          @click="useApi().logout"
          :class="{ disabled: !getIsLogin() }"
        >
          <IconSvg name="quit" size="1rem" color="var(--text-secondary)" /><span
            >退出登录</span
          >
        </li>
      </ul>
      <!-- 设置的小型悬浮菜单，初始隐藏 -->
      <ul class="set-menu menu2" v-show="showSetMenu === 2">
        <!-- 鼠标悬停时保持菜单显示，点击时显示常见问题 -->
        <li @mouseenter="showSetMenufn(2)" @mouseleave="hideSetMenufn">
          <IconSvg
            name="question"
            size="1rem"
            color="var(--text-secondary)"
          /><span>常见问题</span>
        </li>
        <!-- 鼠标悬停时保持菜单显示，点击时显示反馈建议 -->
        <li @mouseenter="showSetMenufn(2)" @mouseleave="hideSetMenufn">
          <IconSvg
            name="feedback"
            size="1rem"
            color="var(--text-secondary)"
          /><span>反馈建议</span>
        </li>
        <!-- 鼠标悬停时保持菜单显示，点击时跳转到图片工具页面 -->
        <li @mouseenter="showSetMenufn(2)" @mouseleave="hideSetMenufn">
          <a href="https://imageutils.sixflower.top/" target="_blank">
            <IconSvg
              name="imgTools"
              size="1rem"
              color="var(--text-secondary)"
            />
            <span>图片工具</span>
          </a>
        </li>
      </ul>
      <!-- 隐私设置弹窗，初始隐藏 -->
      <AppSettingModal
        :show="privacyDialogFlag"
        @close="privacyDialogFlag = false"
      />
      <!-- 皮肤选择弹窗，初始隐藏 -->
      <AppSkinModal :show="skinDialogFlag" @close="skinDialogFlag = false" />
      <!-- 账号安全弹窗，初始隐藏 -->
      <AppAccountSafeModal
        :show="accountSafeDialogFlag"
        @close="accountSafeDialogFlag = false"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
// 引入routerStore和getIsLogin方法
const routerStore = useRouterStore();
const { getIsLogin } = useAuthStore();
// 控制设置悬浮菜单显示的索引
const showSetMenu = ref(0);

// 导航条数据
const navItems: NavItem[] = [
  {
    name: '警告',
    path: '/',
    icon: 'warning',
  },
];

// 定义NavItem接口
export interface NavItem {
  name: string;
  path: string;
  icon: IconSvg;
}

// 定义组件props
const props = defineProps({
  navItems: {
    type: Array as PropType<NavItem[]>,
  },
  query: {
    type: String,
    default: '',
  },
});

// 控制设置悬浮菜单显示隐藏的定时器
const timer: Ref<NodeJS.Timeout | undefined> = ref();
// 显示设置悬浮菜单
const showSetMenufn = (index: number) => {
  showSetMenu.value = index;
  if (timer.value) clearTimeout(timer.value);
};

// 隐藏设置悬浮菜单
const hideSetMenufn = () => {
  timer.value = setTimeout(() => {
    showSetMenu.value = 0;
  }, 200);
};

// 弹窗控制
// 隐私设置弹窗显示控制
const privacyDialogFlag = ref(false);
// 皮肤选择弹窗显示控制
const skinDialogFlag = ref(false);
// 账号安全弹窗显示控制
const accountSafeDialogFlag = ref(false);
</script>

<style lang="scss" scoped>
// 侧边菜单容器样式
.menu-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 导航列表样式
  > .nav {
    flex: 1;
    overflow: auto;
    // 导航列表项样式
    > li {
      color: var(--text-secondary);
      border-radius: 1rem;
      transition: all 0.4s ease;
      margin-bottom: 0.1rem;

      &:last-child {
        margin-bottom: 0;
      }

      &.active,
      &:hover {
        background-color: var(--background-floating);
        color: var(--text-primary);
      }

      a {
        display: flex;
        justify-content: center;
        padding: 0.8rem 1.3rem;

        > span {
          margin-left: 0.7rem;
        }
      }
    }
  }
  // 设置按钮区域样式
  > .set {
    position: relative;

    // 设置按钮列表样式
    > .set-list {
      display: flex;
      justify-content: space-evenly;

      // 设置按钮样式
      > li {
        padding: 0.4rem;
        border-radius: 0.8rem;
        cursor: pointer;

        &:hover {
          background-color: var(--background-floating);
        }
      }
    }

    // 设置悬浮菜单样式
    > .set-menu {
      position: absolute;
      padding: 0.5rem;
      top: -100%;
      background-color: #000;
      border-radius: 1rem;
      width: 9rem;
      z-index: 1;

      // 设置悬浮菜单项样式
      > li {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.7rem 0.5rem;
        border-radius: 0.8rem;
        cursor: pointer;
        font-size: 1rem;

        &:hover {
          background-color: var(--background-floating);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        span {
          margin-left: 1rem;
        }

        > a {
          display: flex;
          align-items: center;
        }
      }
    }

    // 设置第一个悬浮菜单的位置
    > .menu1 {
      top: calc(-2.5rem * 4 - 1.5rem);
    }

    // 设置第二个悬浮菜单的位置
    > .menu2 {
      top: calc(-2.5rem * 3 - 1.5rem);
    }
  }
}
</style>

