<template>
  <RikkaDialog
    :show="show"
    title="编辑资料"
    width="40rem"
    :mask-closable="false"
    @close="close"
  >
    <div class="info-editor-modal">
      <!-- 头像和背景图片编辑区域 -->
      <div
        class="background-img"
        :style="{
          backgroundImage: `url(${backgroundFile.file ? backgroundFile.url : userInfo.background})`,
        }"
      >
        <div
          class="avatar-img"
          :style="{
            backgroundImage: `linear-gradient(to left, #6666, #6666), url(${avatarFile.file ? avatarFile.url : userInfo.avatar + '?width=800'})`,
          }"
          @click="handleChooseAvatar"
        >
          <IconSvg name="camera" color="#fff" size="2rem" />
        </div>
        <div class="background-btn" @click="handleChooseBackground">
          <IconSvg name="edit" color="#fff" size="2rem" />
        </div>
      </div>
      <!-- 表单编辑区域 -->
      <div class="form-item">
        <div class="lab">
          <span> 昵称: </span>
          <RikkaInput
            v-model="userInfo.nickname"
            placeholder="请输入昵称"
            :maxLength="15"
            @keyup.enter="saveUserInfo"
          />
        </div>
        <div class="form-item">
          <div class="lab">
            <span> 性别: </span>
            <RikkaSelect
              v-model="userInfo.gender"
              :options="genderOptions"
              placeholder="请选择性别"
            />
          </div>
          <div class="lab">
            <span> 生日: </span>
            <RikkaDatePicker v-model="userInfo.birthday" />
          </div>
        </div>
        <div class="lab">
          <span> 简介: </span>
          <RikkaInput
            v-model="userInfo.bio"
            placeholder="请输入简介"
            type="textarea"
            :max-length="300"
          />
        </div>
      </div>
    </div>
    <RikkaDialog
      :show="showImageUpload"
      :title="isAvatar ? '选择头像' : '选择背景图'"
      :show-footer="false"
      :mask-closable="false"
      maxDodyHeight="70vh"
      @close="showImageUpload = false"
    >
      <RikkaImageUpload
        ref="imageUpload"
        @file-upload="handleFileUpload"
        @file-removed="clearFiles"
      />
      <RikkaImageCropper
        v-if="uoloadFile"
        :imageFile="uoloadFile"
        :enableAspectRatio="false"
        :forceCrop="true"
        :autoCropArea="1"
        :defaultAspectRatio="isAvatar ? 1 : 21 / 9"
        @crop-complete="handleCropComplete"
        style="margin: 1rem 0"
      />
    </RikkaDialog>
    <template #footer>
      <div class="dialog-footer">
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <RippleButton
          class="btn-confirm"
          @click="saveUserInfo"
          :disabled="isDisabled"
          >确定</RippleButton
        >
      </div>
    </template>
  </RikkaDialog>
</template>

<script lang="ts" setup>
const { show } = defineProps({
  show: Boolean,
});
const emit = defineEmits<{
  close: [value: boolean];
}>();
const close = (flag = false) => {
  clearFiles();
  emit('close', flag);
};

// 获取用户信息状态存储
const authStore = useAuthStore();
// 用户信息响应式对象
const userInfo = ref<Partial<Auth>>({
  avatar: authStore.authStore?.avatar || '',
  background: authStore.authStore?.background || '',
  nickname: authStore.authStore?.nickname || '',
  gender: authStore.authStore?.gender || 'hidden',
  bio: authStore.authStore?.bio || '',
  birthday: authStore.authStore?.birthday || '',
});
// 上传的文件
const uoloadFile = ref<File | null>(null);
// 头像文件对象
const avatarFile = ref<{ file: File | null; url: string }>({
  file: null,
  url: '',
});
// 背景图文件对象
const backgroundFile = ref<{ file: File | null; url: string }>({
  file: null,
  url: '',
});
// 当前选择的是头像还是背景图
const isAvatar = ref<boolean>(false);
// 是否打开图片上传弹窗
const showImageUpload = ref<boolean>(false);

// 性别选项
const genderOptions = [
  { value: 'female', label: '女' },
  { value: 'male', label: '男' },
  { value: 'hidden', label: '隐藏' },
];

// 处理选择头像事件
const handleChooseAvatar = () => {
  isAvatar.value = true;
  showImageUpload.value = true;
  console.log('去选择图片');
};

// 处理选择背景图事件
const handleChooseBackground = () => {
  isAvatar.value = false;
  showImageUpload.value = true;
  console.log('去选择背景');
};

// 清空文件
const clearFiles = () => {
  uoloadFile.value = null;
};

/**
 * 处理上传文件变化事件
 * @param files - 上传的文件数组
 */
const handleFileUpload = (files: File[]) => {
  if (files[0]) {
    uoloadFile.value = files[0];
  }
};

/**
 * 处理图片裁剪完成事件
 * @param file - 裁剪后的图片文件
 * @description 将裁剪后的图片文件存储到响应式变量
 */
const handleCropComplete = (file: File) => {
  showImageUpload.value = false;
  // 替换头像或背景图
  if (isAvatar.value) {
    avatarFile.value.file = file;
    avatarFile.value.url = URL.createObjectURL(file);
  } else {
    backgroundFile.value.file = file;
    backgroundFile.value.url = URL.createObjectURL(file);
  }
  clearFiles();
};

// 确认按钮是否可用
const isDisabled = computed(() => {
  return (
    !authStore.getIsLogin() ||
    !userInfo.value.nickname ||
    !userInfo.value.gender ||
    !userInfo.value.birthday ||
    !userInfo.value.bio
  );
});

// 点击确定按钮触发的事件
const saveUserInfo = async () => {
  if (isDisabled.value) return;
  try {
    useLoading().start({
      title: '正在保存...',
      description: '请稍候...',
    });
    // 处理头像上传
    if (avatarFile.value.file) {
      const data = await useApi().uploadPhoto(avatarFile.value.file);
      if (data && data.length > 0) {
        userInfo.value.avatar = data[0]!.url;
      }
    }
    // 处理背景图上传
    if (backgroundFile.value.file) {
      const data = await useApi().uploadPhoto(backgroundFile.value.file);
      if (data && data.length > 0) {
        userInfo.value.background = data[0]!.url;
      }
    }

    // 保存用户信息
    await useApi().updateMyInfo(userInfo.value);

    useLoading().stop();
    useMessage({
      name: '保存成功',
      description: '资料已保存',
      type: 'success',
    });
    close();
  } catch (err) {
    useLoading().stop();
    useMessage({
      name: '保存失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};
</script>

<style lang="scss" scoped>
.info-editor-modal {
  display: flex;
  flex-direction: column;
  > .background-img {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem 0;
    position: relative;

    > .avatar-img {
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      padding: 1rem;
      border-radius: 50%;
      cursor: pointer;
    }

    > .background-btn {
      position: absolute;
      bottom: 1rem;
      right: 1rem;
      cursor: pointer;
    }
  }

  > .form-item {
    > div {
      margin-top: 0.5rem;
    }

    > .form-item {
      display: flex;

      > :first-child {
        flex: 2;
      }

      > :last-child {
        flex: 3;
        margin-left: 1rem;
      }
    }

    .lab {
      display: flex;
      align-items: center;

      > span {
        margin-right: 1rem;
      }

      > div {
        flex: 1;
      }
    }
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

