<template>
  <div :class="{ app: true, isMobile: getDevice() !== 'desktop' }">
    <!-- 页面跳转时显示进图条 -->
    <NuxtLoadingIndicator color="pink" :height="2" />
    <!-- 主页面 -->
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <!-- 消息提示列表组件 -->
    <div class="message-list">
      <AnimatedList :delay="getDelay()" :data="getDataUpdata()">
        <Notification
          class="message-item"
          v-for="(item, idx) in getMessage().value"
          :key="idx"
          :name="item.name"
          :description="item.description"
          :icon="item.icon"
          :color="item.color"
          :time="item.time"
        />
      </AnimatedList>
    </div>
    <div class="loading-container">
      <RikkaLoading ref="loadingRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
useSeoMeta({
  title: '次元回廊 - 图片分享站',
  description:
    '次元回廊是一款图片分享，用户交流网站，致力于为二次元爱好者提供一个良好的交流平台',
  keywords: '次元回廊,Dimensional Corridor,二次元,图片分享,交流平台,交友,壁纸',
  viewport:
    'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no',
});

const { getDataUpdata, getMessage, getDelay } = useMessageStore();
const { getDevice } = useRouterStore();
</script>

<style lang="scss">
.page-enter-active,
.page-leave-active {
  transition: all 0.1s;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
  filter: blur(1rem);
}
.app {
  height: 100%;
  min-width: 120vh;
  overflow: hidden;

  > .message-list {
    position: absolute;
    left: 50%;
    top: 1rem;
    z-index: 1000;

    > div {
      position: relative;
      left: -50%;

      .message-item {
        background-color: var(--neutral-divider);
      }
    }
  }
}

.isMobile.app {
  min-width: auto;

  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
}
</style>
