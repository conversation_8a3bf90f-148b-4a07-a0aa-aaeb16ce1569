/**
 * 检查密码强度是否符合安全要求
 * @param {string} password - 待检查的密码字符串
 * @returns {boolean} - 返回布尔值，true表示密码符合要求，false表示不符合
 *
 * 密码规则说明：
 * 1. 必须包含至少1个数字字符(\d)
 * 2. 必须包含至少1个小写字母(a-z)
 * 3. 必须包含至少1个大写字母(A-Z)
 * 4. 必须包含至少1个特殊字符(!@#$%^&*()_+~`\-={}[]:";'<>?,./)
 * 5. 密码长度必须在6-18个字符之间
 *
 * 正则表达式分解说明：
 * ^(?=.*\d)          - 正向预查，确保字符串包含至少1个数字
 * (?=.*[a-z])        - 正向预查，确保字符串包含至少1个小写字母
 * (?=.*[A-Z])        - 正向预查，确保字符串包含至少1个大写字母
 * (?=.*[!@#$%^&*()_+~`\-={}[]:";'<>?,./]) - 正向预查，确保字符串包含至少1个特殊字符
 * .{6,18}$           - 匹配任意字符6-18次，确保长度在6-18之间
 *
 * 使用示例：
 * checkPassword('Abc123!') => true
 * checkPassword('weak') => false
 */
export const checkPassword = (password: string) => {
  // 定义密码强度正则表达式
  const reg =
    /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*()_+~`\-={}[\]:";'<>?,./]).{6,18}$/;
  // 使用正则表达式测试密码是否符合要求
  return reg.test(password);
};

