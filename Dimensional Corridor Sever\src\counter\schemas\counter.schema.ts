import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type UserDocument = HydratedDocument<Counter>;

@Schema({
  collection: 'counters', // 定义集合名称
})
export class Counter {
  @Prop({
    required: [true, '名称是必填的'],
    type: String,
    unique: true,
  })
  name: string;

  @Prop({
    required: [true, '值是必填的'],
    type: Number,
    default: 0,
  })
  seq: number;
}

export const CounterSchema = SchemaFactory.createForClass(Counter);
