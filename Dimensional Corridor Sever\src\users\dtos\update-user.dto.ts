import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  Length,
  IsOptional,
  IsUrl,
  IsEnum,
  IsDate,
} from 'class-validator';
import { XssSanitize } from 'src/common/decorator/xss-escape.decorator';
import { UserGenderEnum } from 'src/shared/enums/User.enum';

export class UpdateUserDto {
  /** 用户头像 需要是合法的 url */
  @ApiProperty({
    description: '用户头像 url',
    example: 'https://file.sixflower.top/images/original-default_avatar.png',
  })
  @IsUrl({ require_tld: false }, { message: '头像必须是一个合法的 url' })
  @IsOptional()
  avatar: string;

  /** 主页背景图片 需要是合法的 url */
  @ApiProperty({
    description: '主页背景图片 url',
    example:
      'https://file.sixflower.top/images/original-default_background.png',
  })
  @IsUrl({ require_tld: false }, { message: '背景图片必须是一个合法的 url' })
  @IsOptional()
  background: string;

  /** 用户昵称 */
  @ApiProperty({ description: '用户昵称', example: '六六六花花' })
  @IsString({ message: '昵称必须是字符串' })
  @Length(2, 20, { message: '昵称长度必须在 2-20 之间' })
  @IsOptional()
  nickname: string;

  /** 用户性别 */
  @ApiProperty({ description: '用户性别', example: 'female' })
  @IsEnum(UserGenderEnum, { message: '性别必须是 male, female, 或 hidden' })
  @IsOptional()
  gender: string;

  /** 用户简介 */
  @ApiProperty({ description: '用户简介', example: '六六六花花，一名程序员' })
  @IsOptional()
  @IsString({ message: '简介必须是字符串' })
  @Length(0, 100, { message: '简介长度必须在 0-100 之间' })
  @XssSanitize({})
  bio: string;

  /** 用户生日 */
  @ApiProperty({ description: '用户生日', example: '2000-01-01' })
  @IsOptional()
  @IsDate({ message: '生日必须是一个有效的日期' })
  birthday: Date;
}
