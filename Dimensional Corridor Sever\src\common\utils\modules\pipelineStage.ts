import { PipelineStage } from 'mongoose';

const createFollowPipeline = (
  from: string,
  fromField: string,
  toField: string,
  resultField: string,
  currentUserId?: string
): PipelineStage[] => {
  if (!currentUserId) {
    return [{ $addFields: { [resultField]: false } }];
  }

  return [
    {
      $lookup: {
        from: from,
        let: { targetId: '$_id' }, // 关键点：动态引用当前文档的 following
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$' + fromField, currentUserId] }, // 例如 follower: 当前用户ID
                  { $eq: ['$' + toField, { $toString: '$$targetId' }] }, // 例如 following: 目标用户的ID
                ],
              },
            },
          },
          { $limit: 1 },
        ],
        as: 'status',
      },
    },
    {
      $addFields: {
        [resultField]: { $gt: [{ $size: '$status' }, 0] },
      },
    },
    { $project: { status: 0 } },
  ];
};

/**
 * 判断当前用户是否关注目标用户(每个用户)
 * @param currentUserId 使用者ID
 * @returns PipelineStage[]
 */
export const isFollowing = (currentUserId?: string) =>
  createFollowPipeline(
    'usersfollows',
    'follower',
    'following',
    'isFollowing',
    currentUserId
  );

/**
 * 判断目标用户是否关注当前用户(每个用户)
 * @param currentUserId 目标用户ID
 * @returns PipelineStage[]
 */
export const isFans = (currentUserId?: string) =>
  createFollowPipeline(
    'usersfollows',
    'following',
    'follower',
    'isFans',
    currentUserId
  );

/**
 * 判断用户是否点赞当前图片(每个图片)
 * @param currentUserId 点赞用户ID
 * @returns PipelineStage[]
 */
export const isLikedPhoto = (currentUserId?: string) =>
  createFollowPipeline(
    'photoslikes',
    'user',
    'photo',
    'isLiked',
    currentUserId
  );

/**
 * 判断用户是否收藏当前图片(每个图片)
 * @param currentUserId 收藏用户ID
 * @returns PipelineStage[]
 */
export const isFavoritedPhoto = (currentUserId?: string) =>
  createFollowPipeline(
    'photosfavorites',
    'user',
    'photo',
    'isFavorited',
    currentUserId
  );

/**
 * 判断用户是否点赞当前帖子(每个帖子)
 * @param currentUserId 点赞用户ID
 * @returns PipelineStage[]
 */
export const isLikedContent = (currentUserId?: string) =>
  createFollowPipeline(
    'contentslikes',
    'user',
    'content',
    'isLiked',
    currentUserId
  );

/**
 * 判断用户是否收藏当前帖子(每个帖子)
 * @param currentUserId 收藏用户ID
 * @returns PipelineStage[]
 */
export const isFavoritedContent = (currentUserId?: string) =>
  createFollowPipeline(
    'contentsfavorites',
    'user',
    'content',
    'isFavorited',
    currentUserId
  );
