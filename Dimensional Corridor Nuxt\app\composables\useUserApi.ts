/**
 * 用户相关API的组合式函数
 * 提供与用户信息、设置、关注等相关的API接口封装
 */

/**
 * 获取当前登录用户的个人信息
 * @returns 返回Promise，解析后包含用户认证信息
 * @throws 当请求失败时抛出错误
 */
const getMyInfo = async () => {
  const authStore = useAuthStore(); // 获取认证状态管理实例
  try {
    const { data } = await useRequest<ApiResponse<Auth>>('/users/info');
    authStore.setAuth(data); // 更新认证状态
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 更新当前登录用户的个人信息
 * @param body - 包含要更新的用户信息字段
 * @returns 返回Promise，解析后包含更新后的用户信息
 * @throws 当请求失败时抛出错误
 */
const updateMyInfo = async (body: Partial<Auth>) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest<ApiResponse<Auth>>('/users/info', {
      method: 'PUT', // 使用PUT方法更新资源
      body, // 请求体包含更新数据
    });
    authStore.setAuth(data); // 更新认证状态
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 删除当前登录用户的账户
 * @returns 返回Promise，解析后为布尔值表示是否成功
 * @throws 当请求失败时抛出错误
 */
const deleteMyAccount = async () => {
  try {
    await useRequest('/users', {
      method: 'DELETE', // 使用DELETE方法删除资源
    });
    return true;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取当前登录用户的设置信息
 * @returns 返回Promise，解析后包含用户设置信息
 * @throws 当请求失败时抛出错误
 */
const getUserSetting = async (uid?: string) => {
  const authStore = useAuthStore();
  try {
    const { data: setting } = await useRequest<ApiResponse<UserSettings>>(
      '/users/settings',
      {
        query: {
          uid: uid || authStore.authStore?.uid,
        },
        token: authStore.isAuthenticated,
      }
    );
    return setting;
  } catch (err) {
    throw err;
  }
};

/**
 * 更新当前登录用户的设置信息
 * @param body - 包含要更新的用户设置
 * @returns 返回Promise，解析后包含更新后的设置信息
 * @throws 当请求失败时抛出错误
 */
const setMySetting = async (body: UserSettings) => {
  try {
    const { data } = await useRequest<ApiResponse<UserSettings>>(
      '/users/settings',
      {
        method: 'PUT',
        body,
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取其他用户的公开信息
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含其他用户的信息
 * @throws 当请求失败时抛出错误
 */
const getOtherUserInfo = async (uid: string) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest<ApiResponse<OtherUserInfo>>(
      `/users/info/${uid}`,
      {
        token: authStore.isAuthenticated, // 不需要认证token
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取用户列表（支持分页和模糊查询）
 * @param query - 查询参数对象
 * @returns 返回Promise，解析后包含用户列表数据
 * @throws 当请求失败时抛出错误
 */
const getUserList = async (query?: UserListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest<
      ApiResponse<GetListTemplate<UsersList, UserSortField>>
    >(`/users/list/search`, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序排列
        ...query, // 合并用户自定义查询参数
      },
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取推荐用户列表
 * @returns 返回Promise，解析后包含推荐用户列表
 * @throws 当请求失败时抛出错误
 */
const getRecommendUserList = async () => {
  try {
    const { data } = await useRequest('/users/list/recommend', {
      query: {
        number: 10, // 默认获取10条推荐
      },
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 修改当前登录用户的密码
 * @param body - 包含新旧密码的对象
 * @returns 返回Promise，解析后包含操作结果
 * @throws 当请求失败时抛出错误
 */
const changePassword = async (body: changePasswordBody) => {
  try {
    const { data } = await useRequest('/users/change-password', {
      method: 'PUT',
      body,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 修改当前登录用户的邮箱
 * @param body - 包含新旧邮箱的对象
 * @returns 返回Promise，解析后包含操作结果
 * @throws 当请求失败时抛出错误
 */
const changeEmail = async (body: changeEmailBody) => {
  try {
    const { data } = await useRequest('/users/update-email', {
      method: 'PUT',
      body,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 修改当前登录用户的手机号
 * @param body - 包含新旧手机号的对象
 * @returns 返回Promise，解析后包含操作结果
 * @throws 当请求失败时抛出错误
 */
const changePhone = async (body: changePhoneBody) => {
  try {
    const { data } = await useRequest('/users/update-phone', {
      method: 'PUT',
      body,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 关注或取消关注指定用户
 * @param uid - 目标用户的唯一标识符
 * @throws 当请求失败时抛出错误
 */
const followUser = async (uid: string) => {
  try {
    await useRequest('/users/follow/' + uid, {
      method: 'POST', // 使用POST方法执行关注操作
    });
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户的关注列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含关注列表数据
 * @throws 当请求失败时抛出错误
 */
const getFollowList = async (uid: string) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/followings/' + uid, {
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户的粉丝列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含粉丝列表数据
 * @throws 当请求失败时抛出错误
 */
const getFansList = async (uid: string) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/fans/' + uid, {
      token: authStore.isAuthenticated,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户收藏的图片列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含收藏图片列表数据
 * @throws 当请求失败时抛出错误
 */
const getFavoritePhotoList = async (uid: string, query?: PhotosListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/photos/collect/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户点赞的图片列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含点赞图片列表数据
 * @throws 当请求失败时抛出错误
 */
const getLikePhotoList = async (uid: string, query?: PhotosListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/photos/like/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户收藏的帖子列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含收藏帖子列表数据
 * @throws 当请求失败时抛出错误
 */
const getFavoriteContentList = async (uid: string, query?: PostsListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/contents/collect/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 获取指定用户点赞的帖子列表
 * @param uid - 目标用户的唯一标识符
 * @returns 返回Promise，解析后包含点赞帖子列表数据
 * @throws 当请求失败时抛出错误
 */
const getLikeContentList = async (uid: string, query?: PostsListQuery) => {
  const authStore = useAuthStore();
  try {
    const { data } = await useRequest('/users/contents/like/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated,
    });
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 导出用户相关API
 * @returns 返回包含所有用户API方法的对象
 * @description 部分方法添加了节流(throttle)或防抖(debounce)控制，延迟200ms
 */
export const useUserApi = () => {
  return {
    /** 获取用户自己的信息（节流控制200ms） */
    getMyInfo: useThrottleFn(getMyInfo, 200),
    /** 更新用户自己的信息（节流控制200ms） */
    updateMyInfo: useThrottleFn(updateMyInfo, 200),
    /** 获取用户的设置信息 */
    getUserSetting,
    /** 设置用户的设置信息（防抖控制200ms） */
    setMySetting: useDebounceFn(setMySetting, 200),
    /** 删除用户账户（防抖控制200ms） */
    deleteMyAccount: useDebounceFn(deleteMyAccount, 200),
    /** 更换密码（防抖控制200ms） */
    changePassword: useDebounceFn(changePassword, 200),
    /** 更换邮箱（防抖控制200ms） */
    changeEmail: useDebounceFn(changeEmail, 200),
    /** 更换手机号（防抖控制200ms） */
    changePhone: useDebounceFn(changePhone, 200),
    /** 获取用户列表(节流控制200ms) */
    getUserList: useThrottleFn(getUserList, 200),
    /** 获取推荐用户列表(节流控制200ms) */
    getRecommendUserList: useThrottleFn(getRecommendUserList, 200),
    /** 获取用户收藏的图片列表（节流控制200ms） */
    getFavoritePhotoList: useThrottleFn(getFavoritePhotoList, 200),
    /** 获取用户点赞的图片列表（节流控制200ms） */
    getLikePhotoList: useThrottleFn(getLikePhotoList, 200),
    /** 获取用户收藏的帖子列表（节流控制200ms） */
    getFavoriteContentList: useThrottleFn(getFavoriteContentList, 200),
    /** 获取用户点赞的帖子列表（节流控制200ms） */
    getLikeContentList: useThrottleFn(getLikeContentList, 200),
    /** 获取用户关注列表（节流控制200ms） */
    getFollowList: useThrottleFn(getFollowList, 200),
    /** 获取用户粉丝列表（节流控制200ms） */
    getFansList: useThrottleFn(getFansList, 200),
    /** 关注或取消关注指定用户（防抖控制200ms） */
    followUser: useDebounceFn(followUser, 200),
    /** 查看其他用户的资料（节流控制200ms） */
    getOtherUserInfo: useThrottleFn(getOtherUserInfo, 200),
  };
};
