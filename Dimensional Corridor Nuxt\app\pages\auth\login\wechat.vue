<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-微信登录</Title>
    </Head>

    <!-- 标题 -->
    <h1>微&nbsp;&nbsp;信&nbsp;&nbsp;登&nbsp;&nbsp;录</h1>

    <!-- 二维码 -->
    <div class="qrcode" @click="login">
      <img src="/qrcode.png" alt="" title="点击刷新" />
    </div>

    <!-- 分割线加文字 -->
    <div class="divider">
      <div class="line"></div>
      <span class="divider-text">其他方式登录</span>
      <div class="line"></div>
    </div>
    <!-- 其他登陆方式的图标 -->
    <div class="login-methods">
      <NuxtLink to="/auth/login/phone"
        ><div class="icon">
          <div class="phone">
            <IconSvg name="phone" size="1.5rem" color="#fff" />
          </div>
          <span>手机</span>
        </div></NuxtLink
      >
      <NuxtLink to="/auth/login/email"
        ><div class="icon">
          <div class="phone">
            <IconSvg name="email" size="1.5rem" color="#fff" />
          </div>
          <span>邮箱</span>
        </div></NuxtLink
      >
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'auth',
});

const router = useRouter();

const login = async () => {
  // 微信登录逻辑
  useMessage({
    name: '功能开发中',
    description: '微信登录功能正在开发中',
    type: 'info',
  });

  // 跳转到首页
  router.replace('/mine');
};
</script>

<style lang="scss" scoped>
.page-container {
  min-width: 14rem;

  // 标题的样式
  > h1 {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
  }

  > .qrcode {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5rem 0;

    img {
      width: 10rem;
    }
  }

  /* 分割线样式 */
  .divider {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;

    > .line {
      flex: 1;
      height: 1px;
      background: rgba(255, 255, 255, 0.3); /* 半透明分割线 */
    }

    > .divider-text {
      padding: 0 1rem;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9em;
    }
  }

  // 其他登陆方式区域的样式
  > .login-methods {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-evenly;

    .icon {
      display: flex;
      justify-content: center;
      align-items: center;

      > div {
        padding: 0.3rem;
        border-radius: 50%;

        &.phone {
          background-color: #4228c4;
        }

        &.wechat {
          background-color: #28c445;
        }
      }

      > span {
        margin-left: 0.5rem;
      }
    }
  }
}
</style>
