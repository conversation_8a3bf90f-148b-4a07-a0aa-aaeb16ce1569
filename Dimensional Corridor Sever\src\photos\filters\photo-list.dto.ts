import { Expose, Type } from 'class-transformer';

class PhotoAttributesDto {
  @Expose()
  size: number; // 图片大小
  @Expose()
  width: number; // 图片宽度
  @Expose()
  height: number; // 图片高度
  @Expose()
  format: string; // 图片格式
}

export class PhotoListInfoDto {
  @Expose()
  id: string; // 图片ID
  @Expose()
  filename: string; // 图片名称
  @Expose()
  url: string; // 图片地址
  @Expose()
  tags: Array<string>; // 标签
  @Expose()
  favoriteCount: number; // 收藏量
  @Expose()
  likeCount: number; // 点赞量
  @Expose()
  isLiked: boolean; // 是否点赞
  @Expose()
  isFavorited: boolean; // 是否收藏
  @Expose()
  downloadCount: number; // 下载次数
  @Expose()
  @Type(() => PhotoAttributesDto)
  attributes: PhotoAttributesDto; // 图片属性
}
