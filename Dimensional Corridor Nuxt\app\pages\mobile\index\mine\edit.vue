<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-信息修改</Title>
    </Head>
    <MoblieNavBar>
      信息修改
      <template #right>
        <RippleButton
          class="save-btn"
          @click="saveUserInfo"
          :disabled="isDisabled"
        >
          保存
        </RippleButton>
      </template>
    </MoblieNavBar>

    <main class="edit-content">
      <!-- 头像和背景图片编辑区域 -->
      <div
        class="background-section"
        :style="{
          backgroundImage: `url(${backgroundFile.file ? backgroundFile.url : userInfo.background})`,
        }"
      >
        <div
          class="avatar-section"
          :style="{
            backgroundImage: `linear-gradient(to left, #6666, #6666), url(${avatarFile.file ? avatarFile.url : userInfo.avatar + '?width=800'})`,
          }"
          @click="handleChooseAvatar"
        >
          <IconSvg name="camera" color="#fff" size="2rem" />
        </div>
        <div class="background-btn" @click="handleChooseBackground">
          <IconSvg name="edit" color="#fff" size="1.5rem" />
        </div>
      </div>

      <!-- 表单编辑区域 -->
      <div class="form-section">
        <!-- 昵称 -->
        <div class="form-item">
          <div class="form-label">昵称</div>
          <RikkaInput
            v-model="userInfo.nickname"
            placeholder="请输入昵称"
            :maxLength="15"
          />
        </div>

        <!-- 性别和生日 -->
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">性别</div>
            <RikkaSelect
              v-model="userInfo.gender"
              :options="genderOptions"
              placeholder="请选择性别"
            />
          </div>
          <div class="form-item">
            <div class="form-label">生日</div>
            <RikkaDatePicker v-model="userInfo.birthday" />
          </div>
        </div>

        <!-- 简介 -->
        <div class="form-item">
          <div class="form-label">简介</div>
          <RikkaInput
            v-model="userInfo.bio"
            placeholder="请输入简介"
            type="textarea"
            :max-length="300"
            :rows="4"
          />
        </div>
      </div>
    </main>

    <!-- 图片上传弹窗 -->
    <RikkaDialog
      :show="showImageUpload"
      :title="isAvatar ? '选择头像' : '选择背景图'"
      :show-footer="false"
      :mask-closable="true"
      maxDodyHeight="70vh"
      @close="closeImageUpload"
    >
      <RikkaImageUpload
        ref="imageUpload"
        :multiple="false"
        @file-upload="handleFileUpload"
        @file-removed="clearFiles"
      />
    </RikkaDialog>
  </div>
</template>

<script lang="ts" setup>
// 获取用户信息状态存储
const authStore = useAuthStore();

// 用户信息响应式对象
const userInfo = ref<Partial<Auth>>({
  avatar: authStore.authStore?.avatar || '',
  background: authStore.authStore?.background || '',
  nickname: authStore.authStore?.nickname || '',
  gender: authStore.authStore?.gender || 'hidden',
  bio: authStore.authStore?.bio || '',
  birthday: authStore.authStore?.birthday || '',
});

// 头像文件对象
const avatarFile = ref<{ file: File | null; url: string }>({
  file: null,
  url: '',
});

// 背景图文件对象
const backgroundFile = ref<{ file: File | null; url: string }>({
  file: null,
  url: '',
});

// 当前选择的是头像还是背景图
const isAvatar = ref<boolean>(false);

// 是否打开图片上传弹窗
const showImageUpload = ref<boolean>(false);

// 图片上传组件引用
const imageUpload = ref();

// 性别选项
const genderOptions = [
  { value: 'female', label: '女' },
  { value: 'male', label: '男' },
  { value: 'hidden', label: '隐藏' },
];

// 处理选择头像事件
const handleChooseAvatar = () => {
  isAvatar.value = true;
  showImageUpload.value = true;
};

// 处理选择背景图事件
const handleChooseBackground = () => {
  isAvatar.value = false;
  showImageUpload.value = true;
};

// 关闭图片上传弹窗
const closeImageUpload = () => {
  showImageUpload.value = false;
  clearFiles();
};

// 清空文件
const clearFiles = () => {
  // 清理文件引用
  if (avatarFile.value.url && avatarFile.value.file) {
    URL.revokeObjectURL(avatarFile.value.url);
  }
  if (backgroundFile.value.url && backgroundFile.value.file) {
    URL.revokeObjectURL(backgroundFile.value.url);
  }
};

/**
 * 处理上传文件变化事件 - 直接处理，无需裁剪
 * @param files - 上传的文件数组
 */
const handleFileUpload = (files: File[]) => {
  if (files[0]) {
    const file = files[0];

    // 直接设置文件，无需裁剪
    if (isAvatar.value) {
      // 清理之前的URL
      if (avatarFile.value.url && avatarFile.value.file) {
        URL.revokeObjectURL(avatarFile.value.url);
      }
      avatarFile.value.file = file;
      avatarFile.value.url = URL.createObjectURL(file);
    } else {
      // 清理之前的URL
      if (backgroundFile.value.url && backgroundFile.value.file) {
        URL.revokeObjectURL(backgroundFile.value.url);
      }
      backgroundFile.value.file = file;
      backgroundFile.value.url = URL.createObjectURL(file);
    }

    // 关闭弹窗
    showImageUpload.value = false;
  }
};

// 确认按钮是否可用
const isDisabled = computed(() => {
  return (
    !authStore.getIsLogin() ||
    !userInfo.value.nickname ||
    !userInfo.value.gender ||
    !userInfo.value.birthday ||
    !userInfo.value.bio
  );
});

// 保存用户信息
const saveUserInfo = async () => {
  if (isDisabled.value) return;

  try {
    useLoading().start({
      title: '正在保存...',
      description: '请稍候...',
    });

    // 处理头像上传
    if (avatarFile.value.file) {
      const data = await useApi().uploadPhoto(avatarFile.value.file);
      if (data && data.length > 0) {
        userInfo.value.avatar = data[0]!.url;
      }
    }

    // 处理背景图上传
    if (backgroundFile.value.file) {
      const data = await useApi().uploadPhoto(backgroundFile.value.file);
      if (data && data.length > 0) {
        userInfo.value.background = data[0]!.url;
      }
    }

    // 保存用户信息
    await useApi().updateMyInfo(userInfo.value);

    useLoading().stop();
    useMessage({
      name: '保存成功',
      description: '资料已保存',
      type: 'success',
    });

    // 返回上一页
    await navigateTo('/mobile/mine');
  } catch (err) {
    useLoading().stop();
    useMessage({
      name: '保存失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);

  .edit-content {
    flex: 1;
    overflow: auto;
    background: var(--background-elevated);
  }
}

// 导航栏保存按钮
.save-btn {
  padding: 0.5rem 1rem;
  font-size: 1.4rem;
  background-color: var(--button-primary);
  color: var(--text-primary);
  border-radius: var(--border-radius-sm);
  transition: var(--transition);

  &:hover:not(:disabled) {
    background-color: var(--button-primary-hover);
    box-shadow: var(--shadow-neon-primary);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--button-cancel);
  }

  // 移动端禁用悬停效果
  @media (max-width: 768px) {
    &:hover:not(:disabled) {
      background-color: var(--button-primary);
      box-shadow: none;
    }
  }
}

// 背景图片区域
.background-section {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  position: relative;
  margin-bottom: 2rem;

  // 移动端优化
  @media (max-width: 768px) {
    padding: 2rem 0;
    margin-bottom: 1.5rem;
  }

  .avatar-section {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    padding: 1rem;
    border-radius: 50%;
    cursor: pointer;
    width: 8rem;
    height: 8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);

    &:active {
      transform: scale(0.95);
    }

    // 移动端优化
    @media (max-width: 768px) {
      width: 6rem;
      height: 6rem;
      padding: 0.75rem;
    }
  }

  .background-btn {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    padding: 0.75rem;
    transition: var(--transition);

    &:active {
      transform: scale(0.95);
    }

    // 移动端优化
    @media (max-width: 768px) {
      bottom: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
    }
  }
}

// 表单区域
.form-section {
  padding: 1.5rem;

  // 移动端优化
  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// 表单项
.form-item {
  margin-bottom: 1.5rem;

  .form-label {
    font-size: 1.6rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: block;

    // 移动端优化
    @media (max-width: 768px) {
      font-size: 1.4rem;
      margin-bottom: 0.5rem;
    }
  }

  // 移动端优化
  @media (max-width: 768px) {
    margin-bottom: 1rem;
  }
}

// 表单行（性别和生日）
.form-row {
  display: flex;
  gap: 1rem;

  .form-item {
    flex: 1;
  }

  // 移动端优化
  @media (max-width: 768px) {
    gap: 0.75rem;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>

