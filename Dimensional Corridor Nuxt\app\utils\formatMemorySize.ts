/**
 * 将字节大小转换为易读的带单位字符串
 * @param {number} bytes - 内存大小（单位：字节）
 * @param {number} [decimals=2] - 可选，保留的小数位数（默认2位）
 * @returns {string} - 格式化后的内存大小字符串（如 "1.5 MB"）
 *
 * 功能说明：
 * 1. 自动选择合适的单位（B/KB/MB/GB/TB/PB/EB/ZB/YB）
 * 2. 支持自定义小数位数
 * 3. 自动清理多余的小数位零
 * 4. 处理零字节的特殊情况
 *
 * 实现原理：
 * 1. 使用对数计算确定最合适的单位
 * 2. 基于1024进制进行单位转换
 * 3. 使用正则表达式清理多余小数位
 *
 * 注意事项：
 * 1. 最大支持到YB（尧字节，2^80字节）
 * 2. 对于超大数字（超过Number.MAX_SAFE_INTEGER）可能会有精度问题
 *
 * 使用示例：
 * formatMemorySize(1024) => "1 KB"
 * formatMemorySize(1500) => "1.46 KB"
 * formatMemorySize(0) => "0 B"
 */
export const formatMemorySize = (
  bytes: number,
  decimals: number = 2
): string => {
  // 处理零字节的特殊情况
  if (bytes === 0) return '0 B';

  // 定义单位数组（从小到大）
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  // 定义转换基数（1024进制）
  const base = 1024;

  // 计算单位索引 (0=B, 1=KB, 2=MB...)
  const unitIndex = Math.floor(Math.log(bytes) / Math.log(base));

  // 计算转换后的值并保留指定小数位数
  const value = parseFloat(
    (bytes / Math.pow(base, unitIndex)).toFixed(decimals)
  );

  // 清理多余的小数位（如 1.00 -> 1）
  const cleanValue = value.toString().replace(/\.0+$/, '');

  // 返回格式化后的字符串（值 + 单位）
  return `${cleanValue} ${units[unitIndex]}`;
};

