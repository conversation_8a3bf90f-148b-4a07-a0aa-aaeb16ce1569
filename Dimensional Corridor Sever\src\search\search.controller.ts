import { Body, Controller, Delete, Get, Post, UseGuards } from '@nestjs/common';
import { SearchService } from './search.service';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { formatResponse } from 'src/common/utils';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { Public } from 'src/common/decorator/public.decorator';
import { RolesGuard } from 'src/common/guard/roles.guard';
import { Roles } from 'src/common/decorator/roles.decorator';
import { createHotKeyWordsDto } from './dto/create-hotKeyWords.dto';
import { deleteHotKeyWordsDto } from './dto/delete-hotKeyWords.dto';

@Controller('search')
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @ApiOperation({ summary: '添加热搜词' })
  @ApiOkResponse({ description: '热搜词添加成功' })
  // ! 定义一个post请求，用于添加热搜词
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @Post('hotKeyWords')
  async addHotKeyWord(
    @Body()
    createHotKeyWordDto: createHotKeyWordsDto
  ) {
    const hotKeyWord = await this.searchService.addHotKeyWord(
      createHotKeyWordDto.keyword
    );

    return formatResponse(HttpStatusEnum.OK, '热搜词添加成功', hotKeyWord);
  }

  @ApiOperation({ summary: '获取热搜词列表' })
  @ApiOkResponse({ description: '获取热搜词列表成功' })
  // ! 定义一个get请求，用于获取热搜词列表
  @Get('hotKeyWords')
  @Public()
  async getHotKeyWords() {
    const hotKeyWords = await this.searchService.getHotKeyWords();
    return formatResponse(HttpStatusEnum.OK, '获取热搜词列表成功', hotKeyWords);
  }

  @ApiOperation({ summary: '删除热搜词' })
  @ApiOkResponse({ description: '热搜词删除成功' })
  // ! 定义一个delete请求，用于删除热搜词
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @Delete('hotKeyWords')
  async deleteHotKeyWord(@Body() deleteHotKeyWord: deleteHotKeyWordsDto) {
    const hotKeyWord = await this.searchService.deleteHotKeyWord(
      deleteHotKeyWord.keyword
    );

    return formatResponse(HttpStatusEnum.OK, '热搜词删除成功', hotKeyWord);
  }
}
