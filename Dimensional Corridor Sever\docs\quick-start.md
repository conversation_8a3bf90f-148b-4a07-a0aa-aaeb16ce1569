# WebSocket 快速开始

## 🎯 3分钟快速上手

### 安装和连接
```bash
npm install socket.io-client
```

```javascript
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3000/ws', {
  auth: { token: 'Bearer your-jwt-token' }
});

socket.on('connect', () => console.log('✅ 连接成功'));
socket.on('private_message', (data) => console.log('💬', data.content));
```

### 发送消息
```javascript
// 私聊
socket.emit('private_message', { receiverId: 'user123', content: '你好' });

// 群聊 (需先加入)
socket.emit('join_group', 'groupId');
socket.emit('group_message', { groupId: 'groupId', content: '大家好' });

// 更新状态
socket.emit('update_user_status', { status: 'away' });
```

## 📋 常用事件

```javascript
// 发送事件
socket.emit('private_message', { receiverId, content });
socket.emit('group_message', { groupId, content });
socket.emit('update_user_status', { status });
socket.emit('get_all_online_users');

// 监听事件
socket.on('connect', () => {});
socket.on('private_message', (data) => {});
socket.on('user_online', (data) => {});
socket.on('connect_error', (error) => {});
```

## 🔧 常见问题

**连接失败？** 检查JWT Token是否有效  
**自动重连？** 设置 `reconnection: true`  
**调试模式？** 使用 `localStorage.debug = 'socket.io-client:socket'`

## 📚 详细文档

- [连接与认证](./01-connection-auth.md) - 连接配置和认证机制
- [消息传递](./02-messaging.md) - 私聊、群聊、系统通知  
- [用户状态](./03-user-status.md) - 在线状态管理
- [群组管理](./04-group-management.md) - 群组功能
- [错误处理](./05-error-handling.md) - 错误处理和恢复
