<template>
  <RikkaDialog
    :show="show"
    title="消息通知"
    width="40rem"
    :show-footer="false"
    @close="close"
  >
    <!-- 在对话框内显示一个居中的提示信息 -->
    <div style="text-align: center">暂时没有开发功能哦~</div>
  </RikkaDialog>
  <!-- 结束 RikkaDialog 组件 -->
</template>

<script lang="ts" setup>
// 定义组件的 props，接收一个名为 show 的布尔值属性
const { show } = defineProps({
  show: Boolean,
});

// 定义组件可以触发的事件，这里定义了一个名为 close 的事件
const emit = defineEmits<{
  close: [value: boolean]; // close 事件接收一个布尔值参数
}>();

// 定义 close 方法，用于关闭对话框
const close = () => {
  emit('close', false); // 触发 close 事件，并传递 false 表示关闭对话框
};
</script>

<style lang="scss" scoped></style>

