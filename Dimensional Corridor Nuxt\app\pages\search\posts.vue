<template>
  <div class="page-container" ref="scrollParent">
    <Head>
      <Title>帖子搜索-{{ key }}</Title>
    </Head>
    <!-- 帖子瀑布流画廊组件 -->
    <RikkaPostsWaterfallGallery
      :posts="postsArray"
      :columns="0"
      :card-width="300"
      :gap="20"
      :loading="loading"
      :has-more="hasMore"
      @load-more="handleLoadMore"
    />
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'search',
});

const route = useRoute();
const key = computed(() => route.query.key as string);

// 响应式帖子数组
const postsArray = ref<PostsWaterfallGalleryItem[]>([]);
// 加载状态
const loading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
);
// 分页信息
const paginated = ref<Paginated<PostsSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

/**
 * 获取帖子列表数据
 * @param query 可选查询参数
 */
const getPostsList = async (query?: PostsListQuery) => {
  try {
    // 获取帖子列表数据
    const { list, ...res } = await useApi().getPostList(query);

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      postsArray.value = [];
    }

    // 更新帖子数组和分页信息
    postsArray.value.push(...newData);
    paginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMore = async () => {
  // 如果正在加载或没有更多数据，则直接返回
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;

  // 加载下一页数据
  await getPostsList({
    page: paginated.value.page + 1,
    pageSize: 20,
    keyword: key.value,
  });
  loading.value = false;
};

watch(key, async () => {
  postsArray.value = [];
  await getPostsList({
    page: 1,
    pageSize: 20,
    keyword: key.value,
  });
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
