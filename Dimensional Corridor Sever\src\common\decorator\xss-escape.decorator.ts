import {
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { Transform } from 'class-transformer';

// 自定义 XSS 清洗函数
const xssSanitize = (value: string) => {
  return value
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};
//
@ValidatorConstraint({ name: 'xssSanitize' })
class XssSanitizeConstraint implements ValidatorConstraintInterface {
  validate(value: any) {
    return typeof value === 'string';
  }

  defaultMessage() {
    return 'Invalid input format';
  }
}

export function XssSanitize(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    // 先注册转换器
    Transform(({ value }) =>
      typeof value === 'string' ? xssSanitize(value) : value
    )(object, propertyName);

    // 再注册验证器
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: XssSanitizeConstraint,
    });
  };
}
