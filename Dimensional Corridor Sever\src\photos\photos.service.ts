import { HttpException, Injectable } from '@nestjs/common';
import { CreatePhotoDto } from './dtos/create-photo.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Photos, PhotosDocument } from './schemas/photos.schema';
import { Model, PipelineStage, Types } from 'mongoose';
import { UpdatePhotoDto } from './dtos/update-photo.dto';
import { UsersService } from 'src/users/service/users.service';
import { Users } from 'src/users/schemas/users.schema';
import { PhotosLike } from './schemas/photos-like.schema';
import { PhotosFavorite } from './schemas/photos-favorite.schema';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { isFavoritedPhoto, isLikedPhoto } from 'src/common/utils';

/** 图片服务 */
@Injectable()
export class PhotosService {
  constructor(
    @InjectModel(Photos.name) private readonly photoModel: Model<Photos>,
    @InjectModel(Users.name) private readonly usersModel: Model<Users>,
    @InjectModel(PhotosLike.name) private photosLikeModel: Model<PhotosLike>,
    @InjectModel(PhotosFavorite.name)
    private photosFavoriteModel: Model<PhotosFavorite>,
    private readonly usersService: UsersService
  ) {}

  /**
   * !: 创建图片的异步方法
   * @param userId 用户id
   * @param createPhotoDto 图片创建dto
   * @returns 创建后的图片
   * @throws HttpException 用户不存在
   */
  async create(userId: string, createPhotoDto: CreatePhotoDto) {
    // 查看用户是否存在
    const user = await this.usersService.findOneById(userId);
    if (!user) {
      throw new HttpException('用户不存在', 404);
    }
    // 获取用户设置
    const userSettings = await this.usersService.getSetting(user.uid);

    // 创建图片
    const photo = new this.photoModel({
      user: userId,
      ...createPhotoDto,
      isPublic: userSettings.showMyPhotoList,
    });
    await photo.save();
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { photosCount: 1 },
    });
    return photo.toJSON();
  }

  /**
   * !: 修改图片信息的异步方法
   * @param userId 用户id
   * @param id 图片id
   * @param updatePhotoDto 图片修改dto
   * @returns 修改后的图片
   * @throws HttpException 图片不存在
   */
  async update(userId: string, id: string, updatePhotoDto: UpdatePhotoDto) {
    // 查看图片是否存在
    const photo = await this.photoModel.findById(id);
    if (!photo?.toJSON()) {
      throw new HttpException('图片不存在', 404);
    }
    // 查看用户是否为图片所有者

    if (photo.user !== userId) {
      throw new HttpException('无权修改此图片', 403);
    }
    // 修改图片信息
    photo.set(updatePhotoDto);
    photo.save();
    // 返回修改后的图片
    return photo.toJSON();
  }

  /**
   * !: 根据id删除图片的异步方法
   * @param userId 用户id
   * @param id 图片id
   * @returns 删除状态
   * @throws HttpException 图片不存在
   * @throws HttpException 无权删除此图片
   */
  async delete(userId: string, id: string) {
    // 查看图片是否存在
    const photo = await this.photoModel.findById(id);
    if (!photo?.toJSON()) {
      throw new HttpException('图片不存在', 404);
    }

    // 查看用户是否为图片所有者
    if (photo.user !== userId) {
      throw new HttpException('无权删除此图片', 403);
    }
    // 删除图片
    photo.isDeleted = true;
    photo.save();
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { photosCount: -1 },
    });
    return true;
  }

  /**
   * !: 根据id查找图片的异步方法
   * @param id 图片id
   * @param userId 用户id
   * @returns 图片
   * @throws HttpException 图片不存在
   */
  async findOneById(id: string, userId?: string) {
    const photo = await this.photoModel.findById(id).populate('user');
    if (!photo?.toJSON()) {
      throw new HttpException('图片不存在', 404);
    }

    // 当图片为非公开状态时要检查用户id
    if (!photo.isPublic) {
      // 必须传入用户id验证所属用户是否为当前用户
      // 附加所属用户信息
      const user = await this.usersService.findOneById(photo.user);

      if (!userId || user.id !== userId) {
        throw new HttpException('无权查看此图片', 403);
      }
    }

    const pipeline: PipelineStage[] = [
      { $match: { _id: new Types.ObjectId(id) } },
      ...isLikedPhoto(userId),
      ...isFavoritedPhoto(userId),
    ];

    const data = await this.photoModel.aggregate<Photos>(pipeline);

    return { ...data[0], ...photo.toJSON() };
  }

  /**
   * !: 获取全部图片列表的异步方法
   * @param options 选项
   * @param userId 用户id
   * @returns 图片列表
   * @throws HttpException 用户不存在
   */
  async findList(
    options: Partial<Photos> & PaginationQuery<Photos> & { uid?: string },
    userId?: string
  ) {
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'asc',
      ...rest
    } = options;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const query = { isDeleted: false, isPublic: true, status: 'approved' };

    // 用户筛选
    if (rest.uid) {
      const user = await this.usersService.findOneByUid(rest.uid);
      if (!user) throw new HttpException('用户不存在', 404);
      const settings = await this.usersService.getSetting(user.uid);
      if (!settings?.showMyPhotoList) {
        if (user.id !== userId) {
          throw new HttpException(
            '隐私设置不允许查看',
            HttpStatusEnum.BAD_REQUEST
          );
        }
        query.isPublic = false;
      }
      query['user'] = user.id;
    }
    // 分类筛选
    if (rest.category) query['category'] = rest.category;
    // 标签筛选
    if (rest.tags) query['tags'] = { $in: rest.tags };
    // 关键词模糊
    if (rest.keyword) {
      query['$or'] = [
        { filename: { $regex: rest.keyword, $options: 'i' } },
        { category: { $regex: rest.keyword, $options: 'i' } },
        { tags: { $elemMatch: { $regex: rest.keyword, $options: 'i' } } },
      ];
    }

    // 聚合管道
    const pipeline: PipelineStage[] = [
      { $match: query },
      {
        $project: {
          user: 0, // 移除 user 数组字段
        },
      },
      {
        $facet: {
          meta: [{ $count: 'total' }],
          data: [
            { $sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 } },
            { $skip: skip },
            { $limit: pageSize },
          ],
        },
      },
    ];

    type FacetResult = { meta: { total: number }[]; data: PhotosDocument[] };
    const result = await this.photoModel.aggregate<FacetResult>(pipeline);
    const [facetResult] = result;
    const total = facetResult.meta[0]?.total || 0;
    const list = await Promise.all(
      facetResult.data.map(async (item) => {
        return await this.findOneById(item._id.toString(), userId);
      })
    );

    return {
      page,
      pageSize,
      totalCount: total,
      totalPage: Math.ceil(total / pageSize),
      sortField,
      sortOrder,
      list,
    };
  }

  /**
   * !: 图片浏览量自增的异步方法
   * @param id 图片id
   * @returns 图片
   * @throws HttpException 图片不存在
   */
  async increaseVisitors(id: string) {
    const photo = await this.photoModel.findByIdAndUpdate(
      id,
      {
        $inc: { viewCount: 1 },
      },
      { new: true }
    );
    if (!photo?.toJSON()) {
      throw new HttpException('图片不存在', 404);
    }
    return photo.toJSON();
  }

  /**
   * !: 图片下载次数自增的异步方法
   * @param id 图片id
   * @returns 图片
   * @throws HttpException 图片不存在
   */
  async increaseDownloads(id: string) {
    const photo = await this.photoModel.findByIdAndUpdate(
      id,
      {
        $inc: { downloadCount: 1 },
      },
      { new: true }
    );
    if (!photo?.toJSON()) {
      throw new HttpException('图片不存在', 404);
    }
    const totalDownloads = await this.usersService.getUserTotalDownloads(
      photo.user
    );
    await this.usersModel.findByIdAndUpdate(photo.user, {
      totalDownloads,
    });
    return photo.toJSON();
  }

  /**
   * !: 动态点赞或取消点赞图片
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @description
   * 1. 如果点赞关系不存在，则执行点赞操作。
   * 2. 如果点赞关系已存在，则执行取消点赞操作。
   * 3. 使用事务确保操作的原子性。
   * @returns 返回一个布尔值，表示是否成功执行了点赞或取消点赞操作。
   */
  async toggleLikePhoto(userId: string, photoId: string): Promise<boolean> {
    // 检查是否已点赞
    const exists = await this.photosLikeModel.exists({
      user: userId,
      photo: photoId,
    });

    if (exists) {
      // 如果已点赞，则执行取消点赞逻辑
      await this.unlikePhotoInternal(userId, photoId);
      return false; // 返回 false 表示取消点赞
    } else {
      // 如果未点赞，则执行点赞逻辑
      await this.likePhotoInternal(userId, photoId);
      return true; // 返回 true 表示点赞
    }
  }

  /**
   * !: 点赞图片的内部逻辑
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @param session 当前的事务会话
   * @description
   * 1. 创建新的点赞记录。
   * 2. 更新图片的点赞数量（likeCount）。
   */
  private async likePhotoInternal(
    userId: string,
    photoId: string
  ): Promise<void> {
    // 创建新的点赞记录
    await this.photosLikeModel.create({
      user: userId,
      photo: photoId,
    });

    // 更新图片的点赞数量（likeCount）
    await this.photoModel.findByIdAndUpdate(photoId, {
      $inc: { likeCount: 1 },
    });

    // 更新用户点赞图片数量（likePhotosCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { likePhotosCount: 1 },
    });
  }

  /**
   * !: 取消点赞图片的内部逻辑
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @description
   * 1. 删除点赞记录。
   * 2. 更新图片的点赞数量（likeCount）。
   */
  private async unlikePhotoInternal(
    userId: string,
    photoId: string
  ): Promise<void> {
    // 删除点赞记录
    await this.photosLikeModel.deleteOne({
      user: userId,
      photo: photoId,
    });

    // 更新图片的点赞数量（likeCount）
    await this.photoModel.findByIdAndUpdate(photoId, {
      $inc: { likeCount: -1 },
    });

    // 更新用户点赞图片数量（likePhotosCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { likePhotosCount: -1 },
    });
  }

  /**
   * !: 动态收藏或取消收藏图片
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @description
   * 1. 如果收藏关系不存在，则执行收藏操作。
   * 2. 如果收藏关系已存在，则执行取消收藏操作。
   * 3. 使用事务确保操作的原子性。
   * @returns 返回一个布尔值，表示是否成功执行了收藏或取消收藏操作。
   */
  async toggleFavoritePhoto(userId: string, photoId: string): Promise<boolean> {
    // 检查是否已收藏
    const exists = await this.photosFavoriteModel.exists({
      user: userId,
      photo: photoId,
    });

    if (exists) {
      // 如果已收藏，则执行取消收藏逻辑
      await this.unfavoritePhotoInternal(userId, photoId);
      return false; // 返回 false 表示取消收藏
    } else {
      // 如果未收藏，则执行收藏逻辑
      await this.favoritePhotoInternal(userId, photoId);
      return true; // 返回 true 表示收藏
    }
  }

  /**
   * !: 收藏图片的内部逻辑
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @description
   * 1. 创建新的收藏记录。
   * 2. 更新图片的收藏数量（favoriteCount）。
   */
  private async favoritePhotoInternal(
    userId: string,
    photoId: string
  ): Promise<void> {
    // 创建新的收藏记录
    await this.photosFavoriteModel.create({
      user: userId,
      photo: photoId,
    });

    // 更新图片的收藏数量（favoriteCount）
    await this.photoModel.findByIdAndUpdate(photoId, {
      $inc: { favoriteCount: 1 },
    });

    // 更新用户收藏图片数量（favoritePhotosCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { favoritePhotosCount: 1 },
    });
  }

  /**
   * !: 取消收藏图片的内部逻辑
   * @param userId 用户 ID
   * @param photoId 图片 ID
   * @description
   * 1. 删除收藏记录。
   * 2. 更新图片的收藏数量（favoriteCount）。
   */
  private async unfavoritePhotoInternal(
    userId: string,
    photoId: string
  ): Promise<void> {
    // 删除收藏记录
    await this.photosFavoriteModel.deleteOne({
      user: userId,
      photo: photoId,
    });

    // 更新图片的收藏数量（favoriteCount）
    await this.photoModel.findByIdAndUpdate(photoId, {
      $inc: { favoriteCount: -1 },
    });

    // 更新用户收藏图片数量（favoritePhotosCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { favoritePhotosCount: -1 },
    });
  }
}
