# 🌟 次元回廊 - 前端应用

[![Nuxt](https://img.shields.io/badge/Nuxt-002E3B?style=flat-square&logo=nuxtdotjs&logoColor=#00DC82)](https://nuxt.com/)
[![Vue.js](https://img.shields.io/badge/vuejs-%2335495e.svg?style=flat-square&logo=vuedotjs&logoColor=%234FC08D)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-%23007ACC.svg?style=flat-square&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![TailwindCSS](https://img.shields.io/badge/tailwindcss-%2338B2AC.svg?style=flat-square&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
[![Pinia](https://img.shields.io/badge/pinia-yellow?style=flat-square&logo=pinia&logoColor=white)](https://pinia.vuejs.org/)

## 📋 项目简介

次元回廊前端应用是一个基于 Nuxt.js 3 构建的现代化图片分享社交平台。采用响应式设计，完美适配桌面端和移动端，为用户提供流畅的社交体验和优质的图片分享功能。

## ✨ 核心功能

### 🎨 用户界面

- **响应式设计**: 完美适配桌面端、平板和移动端
- **深色/浅色主题**: 支持主题切换，保护用户视力
- **流畅动画**: 基于Motion-V的页面过渡动画
- **现代化UI**: 基于TailwindCSS的精美界面设计

### 🔐 用户系统

- **多种登录方式**: 支持邮箱、手机号、微信登录
- **用户资料管理**: 完整的个人信息编辑功能
- **头像上传**: 支持图片裁剪和头像自定义
- **账户安全**: 密码修改、绑定手机/邮箱等安全功能

### 📱 内容管理

- **图片上传**: 支持多图上传和实时预览
- **图片裁剪**: 集成CropperJS的专业图片裁剪功能
- **瀑布流展示**: 响应式图片瀑布流布局
- **内容发布**: 图文混合内容发布系统

### 💬 社交功能

- **实时聊天**: WebSocket实时消息系统
- **AI对话**: 集成AI聊天助手
- **用户互动**: 点赞、评论、关注功能
- **搜索功能**: 全文搜索用户和内容

### 📱 移动端优化

- **移动端适配**: 专门的移动端布局和交互
- **触摸手势**: 支持滑动、拖拽等手势操作
- **下拉刷新**: 移动端下拉刷新功能
- **PWA支持**: 渐进式Web应用特性

## 🛠️ 技术栈

| 功能模块     | 技术选型          | 版本   |
| ------------ | ----------------- | ------ |
| **框架**     | Nuxt.js           | 3.16.x |
| **前端框架** | Vue.js            | 3.x    |
| **语言**     | TypeScript        | 5.x    |
| **状态管理** | Pinia             | 3.x    |
| **UI框架**   | TailwindCSS       | 3.x    |
| **工具库**   | VueUse            | 13.x   |
| **图片处理** | CropperJS         | 1.6.x  |
| **动画库**   | Motion-V          | 1.1.x  |
| **时间处理** | Day.js            | 1.11.x |
| **设备检测** | UA-Parser-JS      | 2.x    |
| **地理位置** | MaxMind           | 4.x    |
| **开发工具** | ESLint + Prettier | -      |

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0 (推荐使用)
- **现代浏览器** (支持ES2020+)

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd "Dimensional Corridor Nuxt"
```

2. **安装依赖**

```bash
pnpm install
```

3. **环境配置**

创建环境变量文件：

```bash
# .env 文件
NUXT_PUBLIC_API_BASE=https://api.sixflower.love:3000
NUXT_PUBLIC_API_KEY=your-api-key
```

4. **开发环境启动**

```bash
# 启动开发服务器
pnpm dev
```

访问 `https://web.sixflower.love:1314` 查看应用

5. **生产环境构建**

```bash
# 构建生产版本
pnpm build

# 预览生产版本
pnpm preview

# 生成静态站点
pnpm generate
```

### 🔧 可用脚本

```bash
pnpm dev          # 开发模式启动
pnpm build        # 构建生产版本
pnpm generate     # 生成静态站点
pnpm preview      # 预览生产版本
pnpm postinstall  # 安装后处理
```

## 📁 项目结构

```bash
Dimensional Corridor Nuxt/
│
├── app/                               # 应用源代码
│   ├── app.vue                        # 根组件
│   ├── error.vue                      # 错误页面
│   │
│   ├── assets/                        # 静态资源
│   │   ├── font/                      # 字体文件
│   │   └── styles/                    # 样式文件
│   │       └── css/                   # CSS样式
│   │
│   ├── components/                    # 组件库
│   │   ├── App*.vue                   # 应用级组件
│   │   ├── Rikka*.vue                 # 自定义UI组件库
│   │   ├── Card*.vue                  # 卡片组件
│   │   ├── Mobile*.vue                # 移动端组件
│   │   └── ...                        # 其他功能组件
│   │
│   ├── composables/                   # 组合式API
│   │   ├── useApi.ts                  # API调用封装
│   │   ├── useAuthApi.ts              # 认证API
│   │   ├── usePhotosApi.ts            # 图片API
│   │   ├── usePostsApi.ts             # 内容API
│   │   ├── useSearchApi.ts            # 搜索API
│   │   ├── useUserApi.ts              # 用户API
│   │   ├── useCaptchaApi.ts           # 验证码API
│   │   ├── useRequest.ts              # 请求封装
│   │   ├── useLoading.ts              # 加载状态
│   │   └── useMessage.ts              # 消息提示
│   │
│   ├── layouts/                       # 布局组件
│   │   ├── auth.vue                   # 认证页面布局
│   │   ├── home.vue                   # 首页布局
│   │   ├── details.vue                # 详情页布局
│   │   ├── search.vue                 # 搜索页布局
│   │   ├── mobile.vue                 # 移动端布局
│   │   └── mobile-auth.vue            # 移动端认证布局
│   │
│   ├── pages/                         # 页面路由
│   │   ├── index/                     # 首页相关页面
│   │   ├── auth/                      # 认证相关页面
│   │   ├── mobile/                    # 移动端页面
│   │   └── search/                    # 搜索相关页面
│   │
│   ├── middleware/                    # 中间件
│   │   ├── auth.global.ts             # 全局认证中间件
│   │   └── device-redirect.global.ts  # 设备重定向中间件
│   │
│   ├── plugins/                       # 插件
│   │   ├── device-detector.ts         # 设备检测插件
│   │   ├── loading.ts                 # 加载插件
│   │   ├── message.ts                 # 消息插件
│   │   └── request.ts                 # 请求插件
│   │
│   ├── stores/                        # 状态管理
│   │   ├── authStore.ts               # 认证状态
│   │   ├── messageStore.ts            # 消息状态
│   │   ├── routerStore.ts             # 路由状态
│   │   ├── sendCodeStore.ts           # 验证码状态
│   │   └── tabStore.ts                # 标签页状态
│   │
│   ├── types/                         # 类型定义
│   │   ├── api/                       # API类型
│   │   ├── AppComponents.d.ts         # 组件类型
│   │   ├── IconSvg.d.ts               # 图标类型
│   │   ├── device.d.ts                # 设备类型
│   │   ├── loading.d.ts               # 加载类型
│   │   ├── message.d.ts               # 消息类型
│   │   ├── request.d.ts               # 请求类型
│   │   └── index.ts                   # 类型导出
│   │
│   ├── utils/                         # 工具函数
│   │   ├── calculateAge.ts            # 年龄计算
│   │   ├── checkPassword.ts           # 密码验证
│   │   ├── dayjs.ts                   # 时间处理
│   │   ├── downloadImage.ts           # 图片下载
│   │   ├── encryptContact.ts          # 联系方式加密
│   │   ├── formatMemorySize.ts        # 内存格式化
│   │   └── useMouseState.ts           # 鼠标状态
│   │
│   └── lib/                           # 库文件
│       └── utils.ts                   # 通用工具
│
├── public/                            # 公共静态文件
├── server/                            # 服务端代码
├── nuxt.config.ts                     # Nuxt配置文件
├── tailwind.config.js                 # TailwindCSS配置
├── tsconfig.json                      # TypeScript配置
├── eslint.config.mjs                  # ESLint配置
├── package.json                       # 项目依赖
└── pnpm-lock.yaml                     # 依赖锁定文件
```

## 🎨 核心组件

### 自定义UI组件库 (Rikka系列)

- **RikkaDialog**: 对话框组件
- **RikkaInput**: 输入框组件
- **RikkaSelect**: 选择器组件
- **RikkaDatePicker**: 日期选择器
- **RikkaImageCropper**: 图片裁剪器
- **RikkaImageUpload**: 图片上传组件
- **RikkaImagesWaterfallGallery**: 图片瀑布流
- **RikkaPostsWaterfallGallery**: 内容瀑布流
- **RikkaPullToRefresh**: 下拉刷新
- **RikkaUsersGrid**: 用户网格
- **RikkaTabs**: 标签页组件
- **RikkaTagInput**: 标签输入
- **RikkaLoading**: 加载组件

### 应用级组件

- **AppHearder**: 应用头部
- **AppFooter**: 应用底部
- **AppSideMenu**: 侧边菜单
- **AppPostCard**: 内容卡片
- **AppImageCard**: 图片卡片
- **AppUserCard**: 用户卡片
- **AppSettingModal**: 设置弹窗
- **AppNotificationModel**: 通知弹窗

## 🔧 核心功能实现

### 状态管理 (Pinia)

```typescript
// stores/authStore.ts
export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isLoggedIn: false,
  }),

  actions: {
    async login(credentials) {
      // 登录逻辑
    },

    async logout() {
      // 登出逻辑
    },
  },

  persist: true, // 持久化存储
});
```

### API调用封装

```typescript
// composables/useApi.ts
export const useApi = () => {
  const config = useRuntimeConfig();

  const request = async (url: string, options = {}) => {
    return await $fetch(url, {
      baseURL: config.public.baseUrl,
      headers: {
        'X-API-Key': config.public.ApiKey,
      },
      ...options,
    });
  };

  return { request };
};
```

### 响应式设计

```typescript
// composables/useDevice.ts
export const useDevice = () => {
  const { width } = useWindowSize();

  const isMobile = computed(() => width.value < 768);
  const isTablet = computed(() => width.value >= 768 && width.value < 1024);
  const isDesktop = computed(() => width.value >= 1024);

  return { isMobile, isTablet, isDesktop };
};
```

## 🌐 路由配置

### 页面路由结构

- `/` - 首页 (桌面端)
- `/mobile` - 移动端首页
- `/auth/login` - 登录页面
- `/auth/register` - 注册页面
- `/search` - 搜索页面
- `/profile` - 用户资料页面

### 中间件

- **auth.global.ts**: 全局认证检查
- **device-redirect.global.ts**: 设备自动重定向

## 🎯 性能优化

### 代码分割

- 路由级别的代码分割
- 组件懒加载
- 动态导入第三方库

### 图片优化

- 图片懒加载
- 响应式图片
- WebP格式支持

### 缓存策略

- 状态持久化
- API响应缓存
- 静态资源缓存

## 🚀 部署配置

### 静态站点生成

```bash
# 生成静态站点
pnpm generate

# 部署到静态托管服务
# 如 Vercel, Netlify, GitHub Pages
```

### 服务端渲染部署

```bash
# 构建SSR版本
pnpm build

# 启动生产服务器
pnpm preview
```

### Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .
RUN npm run build

EXPOSE 1314

CMD ["npm", "run", "preview"]
```

## 🔒 环境变量配置

```bash
# .env 文件
# API配置
NUXT_PUBLIC_API_BASE=https://api.sixflower.love:3000
NUXT_PUBLIC_API_KEY=your-api-key

# 开发服务器配置
NUXT_HOST=web.sixflower.love
NUXT_PORT=1314

# SSL证书路径 (开发环境)
NUXT_SSL_KEY=/path/to/ssl.key
NUXT_SSL_CERT=/path/to/ssl.crt
```

## 🛠️ 开发工具

### 代码规范

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型安全检查

### 开发辅助

- **Nuxt DevTools**: 开发调试工具
- **Vue DevTools**: Vue调试工具
- **TailwindCSS IntelliSense**: 样式智能提示

## 📱 浏览器支持

- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动端**: iOS Safari 14+, Chrome Mobile 90+
- **特性支持**: ES2020, WebP, CSS Grid, Flexbox

## 📄 许可证

本项目采用 UNLICENSED 许可证。

---

**次元回廊前端应用** - 现代化的图片分享社交平台 ✨
