# 用户聊天功能设计方案

基于"次元回廊"项目的技术栈（NestJS + Socket.IO + Nuxt.js），我设计了一个高效、可扩展的实时聊天系统方案。

## 一、系统架构

```mermaid
graph TD
    A[前端 Nuxt.js] -->|WebSocket| B(Socket.IO服务器)
    A -->|HTTP API| C[NestJS后端]
    B --> D[Redis Pub/Sub]
    C --> E[MongoDB 持久化]
    D --> B
    E -->|用户数据| C
    F[消息推送服务] -->|离线消息| G[移动设备]
```

## 二、数据库设计（MongoDB）

### 1. 用户模型（扩展）

```javascript
{
  _id: ObjectId,
  username: String,
  online: Boolean,       // 在线状态
  lastSeen: Date,        // 最后在线时间
  socketId: String,      // 当前连接的Socket ID
  settings: {
    chat: {
      notifyNewMessage: Boolean, // 新消息通知
      notifySound: Boole<PERSON>,      // 提示音
      theme: String              // 聊天主题
    }
  }
}
```

### 2. 聊天会话模型

```javascript
{
  _id: ObjectId,
  participants: [ObjectId], // 参与者ID（支持群聊）
  type: { // 会话类型
    type: String,
    enum: ['direct', 'group'],
    default: 'direct'
  },
  lastMessage: ObjectId,  // 最后一条消息
  createdAt: Date,
  updatedAt: Date,
  unreadCount: {          // 每个参与者的未读消息数
    type: Map,
    of: Number
  }
}
```

### 3. 消息模型

```javascript
{
  _id: ObjectId,
  conversation: ObjectId, // 所属会话
  sender: ObjectId,       // 发送者
  content: String,        // 消息内容
  type: {                 // 消息类型
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  readBy: [ObjectId],     // 已读用户ID
  createdAt: Date,
  metadata: {             // 附加元数据
    fileName: String,     // 文件名（如果是文件）
    fileSize: Number,     // 文件大小
    imageWidth: Number,   // 图片宽度
    imageHeight: Number   // 图片高度
  }
}
```

### 索引设计

```javascript
// 会话集合
db.conversations.createIndex({ participants: 1 })
db.conversations.createIndex({ updatedAt: -1 })

// 消息集合
db.messages.createIndex({ conversation: 1, createdAt: -1 })
db.messages.createIndex({ sender: 1 })
```

## 三、后端实现（NestJS）

### 1. WebSocket 网关 (ChatGateway)

```typescript
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets'
import { Server, Socket } from 'socket.io'
import { UseGuards } from '@nestjs/common'
import { WsJwtGuard } from '../auth/ws-jwt.guard'
import { UserService } from '../user/user.service'
import { ChatService } from './chat.service'
import { RedisAdapter } from '@socket.io/redis-adapter'
import { createClient } from 'redis'

@WebSocketGateway({
  namespace: 'chat',
  transports: ['websocket'],
  cors: { origin: '*' },
  adapter: new RedisAdapter(
    createClient({ url: 'redis://localhost:6379' }),
    createClient({ url: 'redis://localhost:6379' })
  ),
})
@UseGuards(WsJwtGuard)
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server

  constructor(private userService: UserService, private chatService: ChatService) {}

  // 客户端连接
  async handleConnection(client: Socket) {
    const userId = client.data.user._id.toString()

    // 更新用户在线状态
    await this.userService.updateUserStatus(userId, true, client.id)

    // 通知相关用户
    const conversations = await this.chatService.getUserConversations(userId)
    conversations.forEach((conv) => {
      conv.participants.forEach((participant) => {
        if (participant.toString() !== userId) {
          this.server.to(`user_${participant}`).emit('user-online', { userId })
        }
      })
    })

    // 加入用户专属房间
    client.join(`user_${userId}`)
  }

  // 客户端断开
  async handleDisconnect(client: Socket) {
    const userId = client.data.user._id.toString()

    // 更新用户在线状态
    await this.userService.updateUserStatus(userId, false)

    // 延迟通知离线状态（避免频繁切换）
    setTimeout(async () => {
      const user = await this.userService.findById(userId)
      if (!user.online) {
        const conversations = await this.chatService.getUserConversations(userId)
        conversations.forEach((conv) => {
          conv.participants.forEach((participant) => {
            if (participant.toString() !== userId) {
              this.server.to(`user_${participant}`).emit('user-offline', { userId })
            }
          })
        })
      }
    }, 5000)
  }

  // 发送消息
  @SubscribeMessage('send-message')
  async handleMessage(client: Socket, payload: any) {
    const { conversationId, content, type, metadata } = payload
    const senderId = client.data.user._id.toString()

    // 创建消息
    const message = await this.chatService.createMessage(
      conversationId,
      senderId,
      content,
      type,
      metadata
    )

    // 获取会话
    const conversation = await this.chatService.getConversation(conversationId)

    // 发送给参与者
    conversation.participants.forEach((participant) => {
      const participantId = participant.toString()

      if (participantId !== senderId) {
        // 增加未读计数
        this.chatService.incrementUnreadCount(conversationId, participantId)
      }

      // 发送消息
      this.server.to(`user_${participantId}`).emit('new-message', {
        conversationId,
        message,
      })
    })
  }

  // 消息已读
  @SubscribeMessage('mark-as-read')
  async handleMarkAsRead(client: Socket, payload: any) {
    const { conversationId, messageIds } = payload
    const userId = client.data.user._id.toString()

    // 更新消息状态
    await this.chatService.markMessagesAsRead(conversationId, messageIds, userId)

    // 重置未读计数
    await this.chatService.resetUnreadCount(conversationId, userId)

    // 通知发送者
    const conversation = await this.chatService.getConversation(conversationId)
    conversation.participants.forEach((participant) => {
      const participantId = participant.toString()
      if (participantId !== userId) {
        this.server.to(`user_${participantId}`).emit('messages-read', {
          conversationId,
          userId,
          messageIds,
        })
      }
    })
  }

  // 创建会话
  @SubscribeMessage('create-conversation')
  async handleCreateConversation(client: Socket, payload: any) {
    const { participantIds } = payload
    const userId = client.data.user._id.toString()

    // 确保包含自己
    const allParticipants = [...new Set([userId, ...participantIds])]

    // 创建会话
    const conversation = await this.chatService.createConversation(
      allParticipants,
      participantIds.length > 1 ? 'group' : 'direct'
    )

    // 通知所有参与者
    allParticipants.forEach((participantId) => {
      this.server.to(`user_${participantId}`).emit('new-conversation', conversation)
    })
  }
}
```

### 2. 聊天服务 (ChatService)

```typescript
import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Conversation } from './schemas/conversation.schema'
import { Message } from './schemas/message.schema'
import { UserService } from '../user/user.service'

@Injectable()
export class ChatService {
  constructor(
    @InjectModel(Conversation.name) private conversationModel: Model<Conversation>,
    @InjectModel(Message.name) private messageModel: Model<Message>,
    private userService: UserService
  ) {}

  // 创建会话
  async createConversation(participants: string[], type: 'direct' | 'group' = 'direct') {
    const conversation = new this.conversationModel({
      participants,
      type,
      unreadCount: new Map(participants.map((id) => [id, 0])),
    })
    return conversation.save()
  }

  // 获取用户会话列表
  async getUserConversations(userId: string): Promise<Conversation[]> {
    return this.conversationModel
      .find({ participants: userId })
      .sort({ updatedAt: -1 })
      .populate('participants', 'username avatar online lastSeen')
      .populate('lastMessage')
      .exec()
  }

  // 创建消息
  async createMessage(
    conversationId: string,
    senderId: string,
    content: string,
    type: string = 'text',
    metadata: any = {}
  ): Promise<Message> {
    const message = new this.messageModel({
      conversation: conversationId,
      sender: senderId,
      content,
      type,
      metadata,
      readBy: [senderId],
    })

    const savedMessage = await message.save()

    // 更新会话最后一条消息
    await this.conversationModel.findByIdAndUpdate(conversationId, {
      lastMessage: savedMessage._id,
      updatedAt: new Date(),
    })

    return savedMessage.populate('sender', 'username avatar')
  }

  // 获取会话消息
  async getConversationMessages(
    conversationId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<Message[]> {
    const skip = (page - 1) * limit

    return this.messageModel
      .find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('sender', 'username avatar')
      .exec()
  }

  // 标记消息为已读
  async markMessagesAsRead(conversationId: string, messageIds: string[], userId: string) {
    await this.messageModel
      .updateMany(
        {
          _id: { $in: messageIds },
          conversation: conversationId,
          readBy: { $ne: userId },
        },
        { $addToSet: { readBy: userId } }
      )
      .exec()
  }

  // 增加未读计数
  async incrementUnreadCount(conversationId: string, userId: string) {
    await this.conversationModel
      .updateOne({ _id: conversationId }, { $inc: { [`unreadCount.${userId}`]: 1 } })
      .exec()
  }

  // 重置未读计数
  async resetUnreadCount(conversationId: string, userId: string) {
    await this.conversationModel
      .updateOne({ _id: conversationId }, { $set: { [`unreadCount.${userId}`]: 0 } })
      .exec()
  }
}
```

## 四、前端实现（Nuxt.js）

### 1. Socket.IO 客户端集成

```javascript
// plugins/socket.client.js
export default defineNuxtPlugin(async (nuxtApp) => {
  const { $auth } = nuxtApp

  if (!$auth.loggedIn) return

  const socket = io('https://api.sixflower.love/chat', {
    transports: ['websocket'],
    auth: {
      token: $auth.token, // JWT token
    },
  })

  // 将socket实例注入nuxtApp
  nuxtApp.provide('socket', socket)

  // 监听事件
  socket.on('new-message', (data) => {
    const chatStore = useChatStore()
    chatStore.addMessage(data.conversationId, data.message)

    // 播放提示音
    if (chatStore.settings.notifySound) {
      playNotificationSound()
    }

    // 显示通知
    if (document.visibilityState !== 'visible') {
      showNotification(data.message)
    }
  })

  socket.on('user-online', ({ userId }) => {
    const chatStore = useChatStore()
    chatStore.setUserOnline(userId)
  })

  socket.on('user-offline', ({ userId }) => {
    const chatStore = useChatStore()
    chatStore.setUserOffline(userId)
  })

  socket.on('messages-read', (data) => {
    const chatStore = useChatStore()
    chatStore.markMessagesAsRead(data.conversationId, data.messageIds)
  })

  socket.on('new-conversation', (conversation) => {
    const chatStore = useChatStore()
    chatStore.addConversation(conversation)
  })

  // 断开连接时清理
  nuxtApp.hook('app:beforeMount', () => {
    if (socket) socket.disconnect()
  })
})

function playNotificationSound() {
  const audio = new Audio('/sounds/notification.mp3')
  audio.volume = 0.3
  audio.play()
}

function showNotification(message) {
  if (!('Notification' in window)) return

  if (Notification.permission === 'granted') {
    new Notification(`${message.sender.username} 发来消息`, {
      body: message.content,
      icon: message.sender.avatar,
    })
  }
}
```

### 2. Pinia 状态管理 (chatStore)

```javascript
// stores/chat.js
export const useChatStore = defineStore('chat', {
  state: () => ({
    conversations: [],
    activeConversation: null,
    messages: new Map(), // conversationId -> messages[]
    onlineUsers: new Set(), // 在线用户ID集合
    settings: {
      notifyNewMessage: true,
      notifySound: true,
      theme: 'light',
    },
  }),

  actions: {
    // 加载对话列表
    async loadConversations() {
      const { data } = await useFetch('/api/chat/conversations')
      this.conversations = data.value || []
    },

    // 选择对话
    async selectConversation(conversationId) {
      this.activeConversation = conversationId

      // 如果还没有加载过消息，则加载
      if (!this.messages.has(conversationId)) {
        const { data } = await useFetch(`/api/chat/messages/${conversationId}`)
        this.messages.set(conversationId, data.value || [])
      }

      // 标记为已读
      const unreadMessages = this.getUnreadMessages(conversationId)
      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map((msg) => msg._id)
        this.$socket.emit('mark-as-read', {
          conversationId,
          messageIds,
        })
      }
    },

    // 发送消息
    async sendMessage(content, type = 'text', metadata = {}) {
      if (!this.activeConversation) return

      // 乐观更新
      const message = {
        _id: `temp_${Date.now()}`,
        content,
        type,
        metadata,
        sender: this.$auth.user,
        createdAt: new Date(),
        readBy: [this.$auth.user._id],
      }

      this.addMessage(this.activeConversation, message)

      // 通过Socket发送
      this.$socket.emit('send-message', {
        conversationId: this.activeConversation,
        content,
        type,
        metadata,
      })
    },

    // 添加消息（从Socket或历史加载）
    addMessage(conversationId, message) {
      if (!this.messages.has(conversationId)) {
        this.messages.set(conversationId, [])
      }

      const messages = this.messages.get(conversationId)

      // 替换临时消息
      if (message._id.startsWith('temp_')) {
        const index = messages.findIndex((m) => m._id === message._id)
        if (index !== -1) {
          messages[index] = message
          return
        }
      }

      messages.push(message)
      // 按时间排序
      messages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    },

    // 获取未读消息
    getUnreadMessages(conversationId) {
      if (!this.messages.has(conversationId)) return []

      const userId = this.$auth.user._id
      return this.messages
        .get(conversationId)
        .filter((msg) => !msg.readBy.includes(userId) && msg.sender._id !== userId)
    },

    // 设置用户在线状态
    setUserOnline(userId) {
      this.onlineUsers.add(userId)

      // 更新会话中的用户状态
      this.conversations.forEach((conv) => {
        const participant = conv.participants.find((p) => p._id === userId)
        if (participant) {
          participant.online = true
        }
      })
    },

    // 设置用户离线状态
    setUserOffline(userId) {
      this.onlineUsers.delete(userId)

      // 更新会话中的用户状态
      this.conversations.forEach((conv) => {
        const participant = conv.participants.find((p) => p._id === userId)
        if (participant) {
          participant.online = false
          participant.lastSeen = new Date()
        }
      })
    },
  },
})
```

### 3. 聊天界面组件

```vue
<!-- components/ChatWindow.vue -->
<template>
  <div class="chat-container">
    <!-- 左侧：会话列表 -->
    <div class="conversation-list">
      <div
        v-for="conv in conversations"
        :key="conv._id"
        :class="['conversation-item', { active: activeConversation === conv._id }]"
        @click="selectConversation(conv._id)">
        <div class="avatar-container">
          <UserAvatar
            :user="getOtherParticipant(conv)"
            size="md" />
          <div
            v-if="getUnreadCount(conv) > 0"
            class="unread-badge">
            {{ getUnreadCount(conv) }}
          </div>
        </div>
        <div class="info">
          <div class="header">
            <span class="name">{{ getOtherParticipant(conv).username }}</span>
            <span class="time">{{ formatTime(conv.updatedAt) }}</span>
          </div>
          <div class="preview">
            {{ getPreview(conv) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧：聊天区域 -->
    <div
      v-if="activeConversation"
      class="chat-area">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <UserAvatar
          :user="currentRecipient"
          size="md" />
        <div class="user-info">
          <div class="username">{{ currentRecipient.username }}</div>
          <div class="status">
            <span
              v-if="isOnline(currentRecipient._id)"
              class="online"
              >在线</span
            >
            <span
              v-else
              class="offline">
              最后在线: {{ formatTime(currentRecipient.lastSeen) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 消息区域 -->
      <div
        ref="messagesContainer"
        class="messages">
        <div
          v-for="msg in currentMessages"
          :key="msg._id"
          class="message-wrapper">
          <div :class="['message', msg.sender._id === $auth.user._id ? 'sent' : 'received']">
            <div
              v-if="msg.sender._id !== $auth.user._id"
              class="avatar">
              <UserAvatar
                :user="msg.sender"
                size="sm" />
            </div>
            <div class="content">
              <div
                v-if="msg.type === 'text'"
                class="text">
                {{ msg.content }}
              </div>

              <div
                v-if="msg.type === 'image'"
                class="image-container">
                <img
                  :src="msg.content"
                  alt="图片"
                  @load="scrollToBottom" />
              </div>

              <div
                v-if="msg.type === 'file'"
                class="file-container">
                <Icon
                  name="ph:file"
                  size="24" />
                <div class="file-info">
                  <div class="file-name">{{ msg.metadata.fileName }}</div>
                  <div class="file-size">{{ formatFileSize(msg.metadata.fileSize) }}</div>
                </div>
                <a
                  :href="msg.content"
                  download
                  class="download-btn"
                  >下载</a
                >
              </div>

              <div class="meta">
                <span>{{ formatTime(msg.createdAt) }}</span>
                <span
                  v-if="msg.sender._id === $auth.user._id"
                  class="read-status">
                  {{ getReadStatus(msg) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="actions">
          <button @click="openFilePicker">
            <Icon
              name="ph:paperclip"
              size="24" />
          </button>
          <button @click="sendImage">
            <Icon
              name="ph:image"
              size="24" />
          </button>
        </div>
        <textarea
          v-model="messageContent"
          @keyup.enter.exact="sendTextMessage"
          placeholder="输入消息..."></textarea>
        <button
          class="send-btn"
          @click="sendTextMessage">
          <Icon
            name="ph:paper-plane-right"
            size="24" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useChatStore } from '~/stores/chat'

const chatStore = useChatStore()
const messageContent = ref('')
const messagesContainer = ref(null)

// 初始化加载对话
onMounted(() => {
  chatStore.loadConversations()
})

// 当前会话的消息
const currentMessages = computed(() => {
  return chatStore.messages.get(chatStore.activeConversation) || []
})

// 当前会话的对方用户
const currentRecipient = computed(() => {
  if (!chatStore.activeConversation) return null

  const conversation = chatStore.conversations.find((c) => c._id === chatStore.activeConversation)

  if (!conversation) return null

  return conversation.participants.find((p) => p._id !== $auth.user._id)
})

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化
watch(
  currentMessages,
  () => {
    scrollToBottom()
  },
  { deep: true }
)

// 发送文本消息
const sendTextMessage = () => {
  if (messageContent.value.trim()) {
    chatStore.sendMessage(messageContent.value.trim())
    messageContent.value = ''
  }
}

// 发送图片
const sendImage = async () => {
  // 实现图片上传逻辑
  // 上传后会获得图片URL
  const imageUrl = await uploadImage()
  chatStore.sendMessage(imageUrl, 'image', {
    imageWidth: 800,
    imageHeight: 600,
  })
}

// 获取消息已读状态
const getReadStatus = (msg) => {
  const conversation = chatStore.conversations.find((c) => c._id === chatStore.activeConversation)

  if (!conversation) return ''

  const otherParticipants = conversation.participants.filter((p) => p._id !== $auth.user._id)

  if (otherParticipants.length === 0) return ''

  if (otherParticipants.every((p) => msg.readBy.includes(p._id))) {
    return '已读'
  }

  return '已发送'
}

// 检查用户是否在线
const isOnline = (userId) => {
  return chatStore.onlineUsers.has(userId)
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 80vh;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.conversation-list {
  width: 300px;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.active {
    background-color: #e3f2fd;
  }
}

.avatar-container {
  position: relative;
  margin-right: 12px;
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.info {
  flex: 1;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
}

.name {
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time {
  color: #757575;
  font-size: 0.8rem;
}

.preview {
  color: #9e9e9e;
  font-size: 0.9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
}

.user-info {
  margin-left: 12px;
}

.status {
  font-size: 0.9rem;
  color: #757575;

  .online {
    color: #4caf50;
  }
}

.messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.message-wrapper {
  margin-bottom: 16px;
}

.message {
  display: flex;
  max-width: 80%;

  &.sent {
    align-self: flex-end;
    flex-direction: row-reverse;

    .content {
      background-color: #daf8cb;
      border-radius: 12px 0 12px 12px;
    }
  }

  &.received {
    .content {
      background-color: #ffffff;
      border-radius: 0 12px 12px 12px;
    }
  }
}

.avatar {
  margin-right: 8px;
}

.content {
  padding: 10px 14px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text {
  white-space: pre-wrap;
  word-break: break-word;
}

.image-container img {
  max-width: 300px;
  max-height: 300px;
  border-radius: 8px;
}

.file-container {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 8px;
}

.file-info {
  flex: 1;
  margin: 0 12px;
}

.file-name {
  font-weight: 500;
}

.file-size {
  font-size: 0.8rem;
  color: #757575;
}

.download-btn {
  color: #2196f3;
  text-decoration: none;
}

.meta {
  font-size: 0.75rem;
  color: #9e9e9e;
  text-align: right;
  margin-top: 4px;
}

.read-status {
  margin-left: 4px;
  color: #2196f3;
}

.input-area {
  border-top: 1px solid #e0e0e0;
  padding: 12px;
  display: flex;
  align-items: flex-end;
}

.actions {
  display: flex;
  margin-right: 8px;
}

textarea {
  flex: 1;
  border: none;
  resize: none;
  min-height: 60px;
  max-height: 120px;
  padding: 8px;
  font-family: inherit;
  font-size: 1rem;

  &:focus {
    outline: none;
  }
}

.send-btn {
  margin-left: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #2196f3;
}
</style>
```

## 五、高级功能扩展

### 1. 文件上传服务

```typescript
// file.service.ts
import { Injectable } from '@nestjs/common'
import { S3 } from 'aws-sdk'
import { v4 as uuidv4 } from 'uuid'

@Injectable()
export class FileService {
  private s3: S3

  constructor() {
    this.s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    })
  }

  async uploadFile(file: Express.Multer.File) {
    const key = `chat/files/${uuidv4()}-${file.originalname}`

    const params = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'public-read',
    }

    const result = await this.s3.upload(params).promise()

    return {
      url: result.Location,
      fileName: file.originalname,
      fileSize: file.size,
      contentType: file.mimetype,
    }
  }

  async uploadImage(file: Express.Multer.File) {
    const key = `chat/images/${uuidv4()}-${file.originalname}`

    // 使用Sharp进行图片压缩和优化
    const optimizedBuffer = await this.optimizeImage(file.buffer)

    const params = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      Body: optimizedBuffer,
      ContentType: file.mimetype,
      ACL: 'public-read',
    }

    const result = await this.s3.upload(params).promise()

    return {
      url: result.Location,
      fileName: file.originalname,
      fileSize: optimizedBuffer.byteLength,
      contentType: file.mimetype,
      // 从优化后的图片获取尺寸
      ...(await this.getImageDimensions(optimizedBuffer)),
    }
  }

  private async optimizeImage(buffer: Buffer): Promise<Buffer> {
    // 使用Sharp库实现图片压缩
    // 返回优化后的Buffer
  }

  private async getImageDimensions(buffer: Buffer): Promise<{ width: number; height: number }> {
    // 使用Sharp获取图片尺寸
  }
}
```

### 2. 消息搜索功能

```typescript
// chat.service.ts
async searchMessages(userId: string, query: string, page: number = 1, limit: number = 20) {
  // 获取用户参与的会话
  const conversations = await this.conversationModel.find({
    participants: userId
  }).select('_id');

  const conversationIds = conversations.map(c => c._id);

  // 使用全文搜索
  return this.messageModel.aggregate([
    {
      $match: {
        conversation: { $in: conversationIds },
        content: { $regex: query, $options: 'i' }
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $skip: (page - 1) * limit
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'users',
        localField: 'sender',
        foreignField: '_id',
        as: 'sender'
      }
    },
    {
      $unwind: '$sender'
    },
    {
      $project: {
        _id: 1,
        content: 1,
        type: 1,
        createdAt: 1,
        'sender._id': 1,
        'sender.username': 1,
        'sender.avatar': 1
      }
    }
  ]);
}
```

### 3. 离线消息支持

```typescript
// chat.gateway.ts
// 在用户连接时发送离线消息
async handleConnection(client: Socket) {
  // ...现有代码

  // 发送离线消息
  const offlineMessages = await this.chatService.getOfflineMessages(userId);
  if (offlineMessages.length > 0) {
    client.emit('offline-messages', offlineMessages);

    // 标记为已送达
    await this.chatService.markMessagesAsDelivered(offlineMessages.map(m => m._id));
  }
}

// chat.service.ts
async getOfflineMessages(userId: string) {
  // 获取用户参与的会话
  const conversations = await this.conversationModel.find({
    participants: userId
  }).select('_id');

  const conversationIds = conversations.map(c => c._id);

  // 获取未送达的消息
  return this.messageModel.find({
    conversation: { $in: conversationIds },
    delivered: { $ne: true },
    sender: { $ne: userId }
  })
  .populate('sender', 'username avatar')
  .exec();
}
```

## 六、安全与性能优化

1. **安全措施**：

   - WebSocket JWT 认证
   - 消息内容过滤（防止 XSS）
   - 文件类型和大小限制
   - 速率限制（防止消息轰炸）
   - 端到端加密选项（使用 Diffie-Hellman 密钥交换）

2. **性能优化**：

   - 消息分页加载
   - 虚拟滚动支持
   - 图片和文件懒加载
   - Redis 消息缓存
   - WebSocket 消息压缩

3. **监控与日志**：
   - 消息送达率监控
   - 连接数统计
   - 错误日志跟踪
   - 消息延迟指标

## 七、移动端优化

1. **PWA 支持**：

   - 添加到主屏幕
   - 后台消息同步
   - 离线消息缓存

2. **移动端适配**：

   - 响应式设计
   - 触摸手势支持
   - 键盘优化

3. **推送通知**：
   - 集成 Firebase Cloud Messaging
   - 离线消息推送
   - 通知偏好设置
